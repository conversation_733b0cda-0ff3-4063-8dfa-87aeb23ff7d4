import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_controller.dart';
import '../../../models/deal_model.dart';

class DealsRows extends DataTableSource {
  final DealController _controller = Get.find<DealController>();

  @override
  DataRow? getRow(int index) {
    if (index >= _controller.filteredItems.length) return null;

    final deal = _controller.filteredItems[index];
    final isSelected = _controller.selectedRows[index];
    final statusColor = _getStatusColor(deal.status);

    return DataRow2(
      selected: isSelected,
     Deal Module:
1.	Header Information: Billing/Shipping addresses, Payment Term, Quotation Validity, Project Details, Delivery Time.

2.	Line Items: Serial Number, Brand, Description, Unit, Quantity, Unit Price (AED), Total Price (AED).

3.	Product Types: A mix of "MTR" (meters) and "NOS" (numbers) for units, and descriptions indicating dimensions (e.g., 100X100X2MMX3MTR).

4.	Summary: Total (AED), Freight Charges (AED), VAT (%), Grand Total (Including VAT).

5.	Footer: T&C, Company Stamp & Sign, Sales Person Name, Contact Info, and various logos.

I understand that for the final quotation design, I should aim for a modern, best-in-class format, and I will keep these elements in mind for the future UI/PDF generation phase.

Regarding your specific points:
1.	Single Brand "Metar":
•	Noted: This simplifies things as we won't need a brandId or brandName field on every DealItemModel if it's always "Metar". We can perhaps store "Metar" as a default in the SettingsModel or simply hardcode it in the quotation generation if it's truly static. This means the "Brand" column in your current quotation image will always display "Metar".

2.	Length Items (Meters vs. Nos of 3m):
•	Noted: This is a critical distinction for both quoting and production.
•	Quoting: Items can be quoted in MTR (meters) or NOS (number of pieces, typically 3m).
•	Production: Production planning is always in NOS (of 3m), except for specific orders needing smaller lengths.
•	Pricing: Always based on weight factory (per KG).

This implies that for each DealItemModel, we'll need to capture:

3.	quotedUnit: (e.g., "MTR", "NOS")

4.	quotedQuantity: The quantity in the quotedUnit.

5.	itemLengthMeters: The length of a single piece in meters (e.g., 3.0 for a standard piece, or a specific smaller value). This is crucial for calculating total length and then total weight.

6.	weightPerMeter or weightPerPiece: This will likely be derived from the ProductVariantModel (which has weight and sqm). If weight in ProductVariantModel refers to the weight of a standard 3m piece, then we'll use that. If it's weight per meter, we'll need to be clear. I will assume ProductVariantModel.weight is the weight of a standard piece (e.g., 3-meter piece), and ProductVariantModel.sqm is for the area if applicable. We can add weightPerMeter to ProductVariantModel if needed.

The calculation flow will likely be:
quotedQuantity (in MTR or NOS) -> totalLength (in meters) -> totalWeight (in KG) -> totalPrice (based on ratePerKg from settings + markup).

My Advice on Lengths and Pricing:
1.	To handle this effectively and provide clarity, I propose the following for the DealItemModel:

2.	quotedUnit (String/Enum): Store "MTR" or "NOS".

3.	quotedQuantity (double/int): The quantity entered by the user in the quotedUnit.

4.	unitLengthMeters (double): The length of a single piece in meters for this specific item. For standard 3m pieces, this would be 3.0. For custom lengths, it would be that custom length. This is crucial for calculating total length and then total weight.

5.	totalLengthMeters (double, calculated): quotedQuantity * unitLengthMeters (if quotedUnit is "NOS") OR quotedQuantity (if quotedUnit is "MTR"). This needs careful logic.

6.	If quotedUnit is "MTR", then totalLengthMeters is simply quotedQuantity.

7.	If quotedUnit is "NOS", then totalLengthMeters is quotedQuantity * unitLengthMeters.

8.	totalWeightKg (double, calculated): This will be the basis for pricing. It will be derived from the ProductVariantModel's weightPerPiece (or weightPerMeter) and the totalLengthMeters.

We need to ensure ProductVariantModel has the necessary information to calculate weightPerMeter or weightPerPiece. If ProductVariantModel.weight is already weightPerPiece for a 3m length, then for a 3m piece, weightPerMeter = weight / 3.

Clarification Needed: Is ProductVariantModel.weight the weight of a 3-meter piece, or the weight per meter? Let's assume for now ProductVariantModel.weight is the weight of a standard 3m piece. If an item is quoted at X meters, its totalWeightKg = (X / 3) * ProductVariantModel.weight. If quoted at Y NOS of Z meters, then totalWeightKg = Y * (Z / 3) * ProductVariantModel.weight. This needs to be precise.


Understanding & Approach for Each Point:
1. Sales User Bifurcation (Permissions)
•	Requirement: Sales users see only their data (accounts, contacts, quotations, deals). Admin/Manager can view all.
•	Approach: This is a fundamental change to data access.
o	We will introduce a UserRole enum (e.g., SalesUser, Manager, Admin, ProductionReviewer).
o	The UserModel will be updated to include a List<UserRole> roles field.
o	Every document in relevant collections (Accounts, Contacts, Quotations, Deals) will include an ownerId (the userId of the sales user who created it).
o	Firestore Security Rules will be implemented to enforce data visibility:
	If the current user has Admin or Manager role, they can read all documents.
	If the current user has SalesUser role, they can only read documents where ownerId matches their userId.
2. Rates & Values in Quotations
•	2.a. Rates per KG from Settings + Markup:
o	Requirement: Automatically apply rates from settings based on material selection. Add a markupPercentage column (default 15%).
o	Approach: When adding an item to a quotation, the system will fetch the relevant ratePerKg from the application settings (which we've already set up).
o	The DealItemModel will have a markupPercentage field (defaulting to 15%). The autoCalculatedUnitPrice will be (settingsRatePerKg * item_weight_kg) * (1 + markupPercentage / 100).
•	2.b. User Entered Rate per Item (Supersedes, not lower than settings):
o	Requirement: Users can enter a userProvidedUnitPrice which supersedes the calculated value but cannot be lower than the base calculated value (before markup).
o	Approach: The DealItemModel will have a nullable userProvidedUnitPrice field. If this field is populated, it will override the autoCalculatedUnitPrice. Validation will ensure userProvidedUnitPrice >= (settingsRatePerKg * item_weight_kg).
•	2.c. Global Quotation Discount:
o	Requirement: Apply a percentage or value discount to the entire quotation.
o	Approach: The DealModel will include fields for discountType (enum: Percentage, Value) and discountValue. The total quotation calculation will account for this global discount.
•	2.d. Store Settings Rates & Calculated Values for Comparison:
o	Requirement: Store settingsRatePerKgAtCreation, autoCalculatedUnitPrice, userProvidedUnitPrice for each item for future comparison.
o	Approach: These specific fields will be explicitly stored within the DealItemModel to provide a historical record and enable auditing/comparison.
•	2.e. Rate/KG shown when User Price Entered:
o	Requirement: If userProvidedUnitPrice is used, also display the implied ratePerKg for that item.
o	Approach: This will be a UI calculation. If userProvidedUnitPrice is present, the UI will calculate (userProvidedUnitPrice * quantity) / total_weight_of_item and display it.
•	2.f. Summary: Combined Rate/KG per Category:
o	Requirement: Display combined rate/kg for each category (e.g., Cable Tray, Channel, Accessories) as a summary on the form.
o	Approach: The QuotationController will aggregate this information based on the materialType or a category property of the products within the quotation items. This will be a dynamically calculated summary displayed on the quotation form.
3. Quotation Approval Workflow
•	Requirement: Quotations require approval by a line Manager or Admin. Once approved, they are locked. Users can have multiple reporting managers.
•	Approach:
o	UserModel Update: Add List<String> reportingManagerIds to the UserModel.
o	DealModel Fields: Add status (enum: Draft, PendingApproval, Approved, Rejected), approvedBy (userId), approvalDate, and isLocked (boolean).
o	Logic: When a sales user submits a quotation for approval, its status changes to PendingApproval. Managers/Admins can view and change the status to Approved or Rejected. Upon Approved, isLocked will be set to true, disabling further edits by the sales user.
4. PDF Export/Print Restrictions
•	Requirement: Sales users can only PDF export/print approved quotations. Admin/Managers have no restrictions.
•	Approach: The PDF generation function will incorporate a check: if the user's role is SalesUser, it will verify that the quotation's status is Approved before proceeding. Admins/Managers will bypass this check.
5. Counter Offer / Re-enabling Locked Quotation
•	Requirement: User can request manager/admin to enable a locked quotation for changes due to counter offers, then resubmit for approval.
•	Approach:
o	Introduce a new QuotationStatus (e.g., UnlockRequested).
o	A sales user can trigger an "Request Unlock" action on a locked quotation. This changes its status to UnlockRequested.
o	Managers/Admins will see these requests and can then choose to change the status back to Draft and set isLocked to false, allowing the sales user to edit and resubmit. This will also involve versioning (see my suggestion below).
6. Optional Manager Approval
•	Requirement: User can decide if manager approval is required for a specific quotation.
•	Approach: Add a requiresApproval (boolean) field to DealModel. If set to false by the sales user, the quotation automatically becomes Approved upon submission (skipping the PendingApproval state). The default for this field should be true for general safety.
7. Production Priority (Challenging but doable)
•	Requirement: Store partial quantity planning based on client/manager priorities. Priorities can be renewed/updated.
•	Approach: This is indeed complex. My recommendation is to use a subcollection approach.
o	Under each deal document (or possibly quotation if production planning starts pre-deal conversion), we create a subcollection named production_priorities.
o	Each document in this production_priorities subcollection will represent a distinct priority batch/phase (e.g., "Phase 1 Delivery", "Urgent Batch").
o	Each priority document will contain:
	id
	name (e.g., "Initial Delivery", "Phase B")
	priorityOrder (int, for sorting)
	dueDate (nullable)
	items (a List of maps, where each map contains { 'quotationItemId': '...', 'allocatedQuantity': N }). This links back to specific items in the main quotation/deal and the quantity assigned to this priority.
o	This allows for granular control, easy updates, and clear tracking of what is allocated to which production priority.
8. Client Approval & Production Review
•	Requirement: Button for client approval. Deal final, forwarded for production review. Reviewer sets desired thickness and material for each item.
•	Approach:
o	When the client approves, the DealModel's status can transition to ClientApproved (or it might be converted into a DealModel).
o	New fields will be added to DealItemModel (or DealItemModel if a separate Deal is created): reviewerSelectedThickness (double, nullable) and reviewerConfirmedMaterialType (String, nullable).
o	These fields will only be editable by users with the ProductionReviewer role (or Admin/Manager).
9. User Access (Roles/Modules)
•	Requirement: Deals viewed by sales/commercial or approved users. Factory reviewer sees deals when approved, adds data, approves for production.
•	Approach (Decision): I recommend using a list of roles assigned to each user (List<UserRole> roles in UserModel). This is scalable and common.
o	UserRole Enum: SalesUser, Manager, Admin, ProductionReviewer, FactoryWorker, Accountant, etc.
o	For UI elements (buttons, forms, visibility), you'll check currentUser.roles.contains(UserRole.X).
o	For data access (Firestore read/write), robust Firestore Security Rules will enforce what each role can do on specific collections and documents.
10. Production Stage (To be detailed later)
•	Requirement: Partial quantity planning, partial production, partial HDG, partial delivery, partial return, inventory conversion, inventory assignment.
•	Approach: This will be a major module built upon the Quotations/Deals and Raw Material modules. We will design dedicated models for ProductionOrder, ProductionBatch, Delivery, Return, and integrate them with inventory. This is a very large scope that we will address after the core Quotation/Deal and Raw Material modules are solid.
11. Raw Material Module (To be detailed later)
•	Requirement: Each deal shows required vs. available raw material.
•	Approach: This requires a Bill of Materials (BOM) for each ProductModel (or ProductVariantModel). When a Deal is moved to production, a ProductionOrder will calculate the total raw material needs based on the BOM of the items in the deal. This will then be compared against a separate RawMaterialInventory module.
12. Missed Critical Stages (My Suggestions)
•	Audit Trails: Crucial for any ERP. We should add createdAt, createdBy, updatedAt, updatedBy fields to all models. For complex actions (like approval or status changes), a dedicated auditLog subcollection or an event-based logging system might be necessary.
•	Quotation Versioning: Instead of just unlocking and editing a quotation (point 5), a more robust approach is to create a new version of the quotation when changes are made after approval or a counter-offer. Each DealModel could have a version number and a previousVersionId. This preserves history.
•	Notifications: Implement a system to notify managers of pending approvals, or sales users of approvals/rejections.
•	Payment Terms: Fields for payment terms (creditDays, paymentMethod, paymentSchedule) should be part of the DealModel.


      cells: [
        // Deal Number
        DataCell(Text(deal.dealNumber, style: const TextStyle(fontWeight: FontWeight.w600))),

        // Client
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                deal.clientName,
                style: const TextStyle(fontWeight: FontWeight.w500),
                overflow: TextOverflow.ellipsis,
              ),
              if (deal.contactPersons.isNotEmpty)
                Text(
                  deal.contactPersons.first.name,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ),

        // Sales Person
        DataCell(Text(deal.salesPersonName, overflow: TextOverflow.ellipsis)),

        // Status
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
              border: Border.all(color: statusColor.withValues(alpha: 0.3)),
            ),
            child: Text(
              deal.status.name.toUpperCase(),
              style: TextStyle(color: statusColor, fontWeight: FontWeight.bold, fontSize: 11),
            ),
          ),
        ),

        // Total Amount
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'AED ${deal.grandTotalAmount.toStringAsFixed(2)}',
                style: const TextStyle(fontWeight: FontWeight.w600, color: TColors.primary),
              ),
              if (deal.discountValue > 0)
                Text(
                  'Disc: ${deal.totalDiscountAmount.toStringAsFixed(2)}',
                  style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                ),
            ],
          ),
        ),

        // Total Weight
        DataCell(
          Text(
            'N/A', // TODO: Calculate from deal items
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),

        // Progress
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('${deal.progressPercentage.toInt()}%', style: const TextStyle(fontSize: 12)),
              const SizedBox(height: 2),
              Container(
                width: 60,
                height: 4,
                decoration: BoxDecoration(color: Colors.grey[300], borderRadius: BorderRadius.circular(2)),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: deal.progressPercentage / 100,
                  child: Container(
                    decoration: BoxDecoration(color: TColors.primary, borderRadius: BorderRadius.circular(2)),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Created Date
        DataCell(
          Text(
            deal.createdAt != null ? THelperFunctions.getFormattedDate(deal.createdAt!) : 'N/A',
            style: const TextStyle(fontSize: 12),
          ),
        ),

        // Actions
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // View/Edit Button
              IconButton(
                icon: Icon(
                  _controller.canEditDeal(deal) ? Iconsax.edit : Iconsax.eye,
                  size: 18,
                  color: TColors.primary,
                ),
                onPressed: () {
                  if (_controller.canEditDeal(deal)) {
                    Get.toNamed('/edit-deal', arguments: deal);
                  } else {
                    Get.toNamed('/deal-details', arguments: deal);
                  }
                },
                tooltip: _controller.canEditDeal(deal) ? 'Edit Deal' : 'View Deal',
              ),

              // Approval Actions (for managers/admins)
              if (deal.status == DealStatus.PendingApproval && _controller.canApproveDeal(deal))
                PopupMenuButton<String>(
                  icon: const Icon(Iconsax.more, size: 18, color: TColors.primary),
                  onSelected: (value) {
                    switch (value) {
                      case 'approve':
                        _controller.approveDeal(deal);
                        break;
                      case 'reject':
                        _controller.rejectDeal(deal);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'approve',
                      child: Row(
                        children: [
                          Icon(Iconsax.tick_circle, color: Colors.green, size: 16),
                          SizedBox(width: 8),
                          Text('Approve'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'reject',
                      child: Row(
                        children: [
                          Icon(Iconsax.close_circle, color: Colors.red, size: 16),
                          SizedBox(width: 8),
                          Text('Reject'),
                        ],
                      ),
                    ),
                  ],
                ),

              // Unlock Actions (for admins)
              if (deal.status == DealStatus.UnlockRequested && _controller.canApproveDeal(deal))
                IconButton(
                  icon: const Icon(Iconsax.unlock, size: 18, color: Colors.orange),
                  onPressed: () => _controller.unlockDeal(deal),
                  tooltip: 'Unlock Deal',
                ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => _controller.filteredItems.length;

  @override
  int get selectedRowCount => _controller.selectedRows.where((selected) => selected).length;

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.Draft:
        return Colors.grey;
      case DealStatus.PendingApproval:
        return Colors.orange;
      case DealStatus.Approved:
        return Colors.green;
      case DealStatus.Rejected:
        return Colors.red;
      case DealStatus.UnlockRequested:
        return Colors.purple;
      case DealStatus.Superseded:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }
}
