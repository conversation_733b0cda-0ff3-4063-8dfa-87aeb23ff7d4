import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_controller.dart';
import '../../../models/deal_model.dart';

class DealsRows extends DataTableSource {
  final DealController _controller = Get.find<DealController>();

  @override
  DataRow? getRow(int index) {
    if (index >= _controller.filteredItems.length) return null;

    final deal = _controller.filteredItems[index];
    final isSelected = _controller.selectedRows[index];
    final statusColor = _getStatusColor(deal.status);

    return DataRow2(
      selected: isSelected,
      onSelectChanged: (selected) => _controller.toggleRowSelection(index),
      cells: [
        // Deal Number
        DataCell(Text(deal.dealNumber, style: const TextStyle(fontWeight: FontWeight.w600))),

        // Client
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                deal.clientName,
                style: const TextStyle(fontWeight: FontWeight.w500),
                overflow: TextOverflow.ellipsis,
              ),
              if (deal.contactPersons.isNotEmpty)
                Text(
                  deal.contactPersons.first.name,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        ),

        // Sales Person
        DataCell(Text(deal.salesPersonName, overflow: TextOverflow.ellipsis)),

        // Status
        DataCell(
          Container(
            padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
              border: Border.all(color: statusColor.withValues(alpha: 0.3)),
            ),
            child: Text(
              deal.status.name.toUpperCase(),
              style: TextStyle(color: statusColor, fontWeight: FontWeight.bold, fontSize: 11),
            ),
          ),
        ),

        // Total Amount
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'AED ${deal.grandTotalAmount.toStringAsFixed(2)}',
                style: const TextStyle(fontWeight: FontWeight.w600, color: TColors.primary),
              ),
              if (deal.discountValue > 0)
                Text(
                  'Disc: ${deal.totalDiscountAmount.toStringAsFixed(2)}',
                  style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                ),
            ],
          ),
        ),

        // Total Weight
        DataCell(
          Text(
            'N/A', // TODO: Calculate from deal items
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),

        // Progress
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('${deal.progressPercentage.toInt()}%', style: const TextStyle(fontSize: 12)),
              const SizedBox(height: 2),
              Container(
                width: 60,
                height: 4,
                decoration: BoxDecoration(color: Colors.grey[300], borderRadius: BorderRadius.circular(2)),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: deal.progressPercentage / 100,
                  child: Container(
                    decoration: BoxDecoration(color: TColors.primary, borderRadius: BorderRadius.circular(2)),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Created Date
        DataCell(
          Text(
            deal.createdAt != null ? THelperFunctions.getFormattedDate(deal.createdAt!) : 'N/A',
            style: const TextStyle(fontSize: 12),
          ),
        ),

        // Actions
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // View/Edit Button
              IconButton(
                icon: Icon(
                  _controller.canEditDeal(deal) ? Iconsax.edit : Iconsax.eye,
                  size: 18,
                  color: TColors.primary,
                ),
                onPressed: () {
                  if (_controller.canEditDeal(deal)) {
                    Get.toNamed('/edit-deal', arguments: deal);
                  } else {
                    Get.toNamed('/deal-details', arguments: deal);
                  }
                },
                tooltip: _controller.canEditDeal(deal) ? 'Edit Deal' : 'View Deal',
              ),

              // Approval Actions (for managers/admins)
              if (deal.status == DealStatus.PendingApproval && _controller.canApproveDeal(deal))
                PopupMenuButton<String>(
                  icon: const Icon(Iconsax.more, size: 18, color: TColors.primary),
                  onSelected: (value) {
                    switch (value) {
                      case 'approve':
                        _controller.approveDeal(deal);
                        break;
                      case 'reject':
                        _controller.rejectDeal(deal);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'approve',
                      child: Row(
                        children: [
                          Icon(Iconsax.tick_circle, color: Colors.green, size: 16),
                          SizedBox(width: 8),
                          Text('Approve'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'reject',
                      child: Row(
                        children: [
                          Icon(Iconsax.close_circle, color: Colors.red, size: 16),
                          SizedBox(width: 8),
                          Text('Reject'),
                        ],
                      ),
                    ),
                  ],
                ),

              // Unlock Actions (for admins)
              if (deal.status == DealStatus.UnlockRequested && _controller.canApproveDeal(deal))
                IconButton(
                  icon: const Icon(Iconsax.unlock, size: 18, color: Colors.orange),
                  onPressed: () => _controller.unlockDeal(deal),
                  tooltip: 'Unlock Deal',
                ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => _controller.filteredItems.length;

  @override
  int get selectedRowCount => _controller.selectedRows.where((selected) => selected).length;

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.Draft:
        return Colors.grey;
      case DealStatus.PendingApproval:
        return Colors.orange;
      case DealStatus.Approved:
        return Colors.green;
      case DealStatus.Rejected:
        return Colors.red;
      case DealStatus.UnlockRequested:
        return Colors.purple;
      case DealStatus.Superseded:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }
}
