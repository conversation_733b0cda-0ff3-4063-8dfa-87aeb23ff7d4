import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../routes/routes.dart';
import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_controller.dart';
import '../../../models/deal_model.dart';

class DealsRows extends DataTableSource {
  final DealController _controller = Get.find<DealController>();

  @override
  DataRow? getRow(int index) {
    if (index >= _controller.filteredItems.length) return null;

    final deal = _controller.filteredItems[index];
    final isSelected = _controller.selectedRows[index];
    final statusColor = _getStatusColor(deal.status);

    return DataRow2(
      selected: isSelected,
      onSelectChanged: (selected) => _controller.toggleRowSelection(index),
      cells: [
        // Deal Number with Icon and Version
        DataCell(
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(_getStatusIcon(deal.status), size: 14, color: statusColor),
              ),
              const SizedBox(width: TSizes.xs),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      deal.dealNumber,
                      style: TextStyle(fontWeight: FontWeight.w600, color: statusColor),
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (deal.version > 1)
                      Text('Version ${deal.version}', style: TextStyle(fontSize: 10, color: Colors.blue[600])),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Client with Enhanced Info
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                deal.clientName,
                style: const TextStyle(fontWeight: FontWeight.w500),
                overflow: TextOverflow.ellipsis,
              ),
              if (deal.contactPersons.isNotEmpty)
                Text(
                  deal.contactPersons.first.name,
                  style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                  overflow: TextOverflow.ellipsis,
                ),
              // Days since created
              Text(
                '${_calculateDaysSinceCreated(deal)} days active',
                style: TextStyle(fontSize: 10, color: Colors.orange[600]),
              ),
            ],
          ),
        ),

        // Sales Person with Performance Indicator
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                deal.salesPersonName,
                style: const TextStyle(fontWeight: FontWeight.w500),
                overflow: TextOverflow.ellipsis,
              ),
              Row(
                children: [
                  Icon(Iconsax.user, size: 10, color: Colors.blue[600]),
                  const SizedBox(width: 2),
                  Text('Owner', style: TextStyle(fontSize: 10, color: Colors.blue[600])),
                ],
              ),
            ],
          ),
        ),

        // Enhanced Status with Progress Indicator
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                ),
                child: Text(
                  deal.status.name.toUpperCase(),
                  style: TextStyle(color: statusColor, fontWeight: FontWeight.bold, fontSize: 10),
                ),
              ),
              const SizedBox(height: 2),
              // Mini progress indicator
              Container(
                width: 60,
                height: 3,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(2),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _calculateProgressPercentage(deal) / 100,
                  child: Container(
                    decoration: BoxDecoration(color: statusColor, borderRadius: BorderRadius.circular(2)),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Total Amount
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                'AED ${deal.grandTotalAmount.toStringAsFixed(2)}',
                style: const TextStyle(fontWeight: FontWeight.w600, color: TColors.primary),
              ),
              if (deal.discountValue > 0)
                Text(
                  'Disc: ${deal.totalDiscountAmount.toStringAsFixed(2)}',
                  style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                ),
            ],
          ),
        ),

        // Total Weight
        DataCell(
          Text(
            'N/A', // TODO: Calculate from deal items
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
        ),

        // Enhanced Progress with Sales Pipeline
        DataCell(
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Row(
                children: [
                  Text(
                    '${_calculateProgressPercentage(deal).toInt()}%',
                    style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold, color: statusColor),
                  ),
                  const SizedBox(width: 4),
                  Icon(_getProgressIcon(deal.status), size: 12, color: statusColor),
                ],
              ),
              const SizedBox(height: 3),
              Container(
                width: 80,
                height: 6,
                decoration: BoxDecoration(
                  color: Colors.grey.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(3),
                ),
                child: FractionallySizedBox(
                  alignment: Alignment.centerLeft,
                  widthFactor: _calculateProgressPercentage(deal) / 100,
                  child: Container(
                    decoration: BoxDecoration(
                      color: statusColor,
                      borderRadius: BorderRadius.circular(3),
                      gradient: LinearGradient(
                        colors: [statusColor.withValues(alpha: 0.7), statusColor],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 2),
              Text(_getProgressStage(deal.status), style: TextStyle(fontSize: 9, color: Colors.grey[600])),
            ],
          ),
        ),

        // Created Date
        DataCell(
          Text(
            deal.createdAt != null ? THelperFunctions.getFormattedDate(deal.createdAt!) : 'N/A',
            style: const TextStyle(fontSize: 12),
          ),
        ),

        // Actions
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              // View/Edit Button
              IconButton(
                icon: Icon(
                  _controller.canEditDeal(deal) ? Iconsax.edit : Iconsax.eye,
                  size: 18,
                  color: TColors.primary,
                ),
                onPressed: () {
                  if (_controller.canEditDeal(deal)) {
                    Get.toNamed(TRoutes.editDeal, arguments: deal);
                  } else {
                    Get.toNamed(TRoutes.viewDeal, arguments: deal);
                  }
                },
                tooltip: _controller.canEditDeal(deal) ? 'Edit Deal' : 'View Deal',
              ),

              // Approval Actions (for managers/admins)
              if (deal.status == DealStatus.pendingApproval && _controller.canApproveDeal(deal))
                PopupMenuButton<String>(
                  icon: const Icon(Iconsax.more, size: 18, color: TColors.primary),
                  onSelected: (value) {
                    switch (value) {
                      case 'approve':
                        _controller.approveDeal(deal);
                        break;
                      case 'reject':
                        _controller.rejectDeal(deal);
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'approve',
                      child: Row(
                        children: [
                          Icon(Iconsax.tick_circle, color: Colors.green, size: 16),
                          SizedBox(width: 8),
                          Text('Approve'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'reject',
                      child: Row(
                        children: [
                          Icon(Iconsax.close_circle, color: Colors.red, size: 16),
                          SizedBox(width: 8),
                          Text('Reject'),
                        ],
                      ),
                    ),
                  ],
                ),

              // Unlock Actions (for admins)
              if (deal.status == DealStatus.unlockRequested && _controller.canApproveDeal(deal))
                IconButton(
                  icon: const Icon(Iconsax.unlock, size: 18, color: Colors.orange),
                  onPressed: () => _controller.unlockDeal(deal),
                  tooltip: 'Unlock Deal',
                ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => _controller.filteredItems.length;

  @override
  int get selectedRowCount => _controller.selectedRows.where((selected) => selected).length;

  // Calculate days since deal was created
  int _calculateDaysSinceCreated(DealModel deal) {
    if (deal.createdAt == null) return 0;
    return DateTime.now().difference(deal.createdAt!).inDays;
  }

  // Calculate progress percentage based on deal status
  double _calculateProgressPercentage(DealModel deal) {
    // Use the progressPercentage field if available, otherwise calculate based on status
    if (deal.progressPercentage > 0) {
      return deal.progressPercentage;
    }

    switch (deal.status) {
      case DealStatus.draft:
        return 10.0;
      case DealStatus.pendingApproval:
        return 30.0;
      case DealStatus.approved:
        return 60.0;
      case DealStatus.clientApproved:
        return 90.0;
      case DealStatus.closed:
        return 100.0;
      case DealStatus.rejected:
      case DealStatus.clientDeclined:
        return 0.0;
      case DealStatus.unlockRequested:
        return 45.0;
      case DealStatus.superseded:
        return 100.0;
      case DealStatus.quotationGenerated:
        return 75.0;
    }
  }

  // Get appropriate icon for deal status
  IconData _getStatusIcon(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Iconsax.document_text;
      case DealStatus.pendingApproval:
        return Iconsax.clock;
      case DealStatus.approved:
        return Iconsax.tick_circle;
      case DealStatus.rejected:
        return Iconsax.close_circle;
      case DealStatus.unlockRequested:
        return Iconsax.unlock;
      case DealStatus.clientApproved:
        return Iconsax.medal_star;
      case DealStatus.clientDeclined:
        return Iconsax.dislike;
      case DealStatus.superseded:
        return Iconsax.refresh;
      case DealStatus.closed:
        return Iconsax.archive_tick;
      case DealStatus.quotationGenerated:
        return Iconsax.document_download;
    }
  }

  // Get progress icon for status
  IconData _getProgressIcon(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Iconsax.edit;
      case DealStatus.pendingApproval:
        return Iconsax.clock;
      case DealStatus.approved:
        return Iconsax.tick_circle;
      case DealStatus.clientApproved:
        return Iconsax.medal_star;
      case DealStatus.closed:
        return Iconsax.archive_tick;
      case DealStatus.rejected:
      case DealStatus.clientDeclined:
        return Iconsax.close_circle;
      case DealStatus.unlockRequested:
        return Iconsax.unlock;
      case DealStatus.superseded:
        return Iconsax.refresh;
      case DealStatus.quotationGenerated:
        return Iconsax.document_download;
    }
  }

  // Get progress stage description
  String _getProgressStage(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return 'Drafting';
      case DealStatus.pendingApproval:
        return 'Under Review';
      case DealStatus.approved:
        return 'Approved';
      case DealStatus.clientApproved:
        return 'Client Approved';
      case DealStatus.closed:
        return 'Completed';
      case DealStatus.rejected:
        return 'Rejected';
      case DealStatus.clientDeclined:
        return 'Client Declined';
      case DealStatus.unlockRequested:
        return 'Unlock Requested';
      case DealStatus.superseded:
        return 'Superseded';
      case DealStatus.quotationGenerated:
        return 'Quotation Generated';
    }
  }

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Colors.grey;
      case DealStatus.pendingApproval:
        return Colors.orange;
      case DealStatus.approved:
        return Colors.green;
      case DealStatus.rejected:
        return Colors.red;
      case DealStatus.unlockRequested:
        return Colors.purple;
      case DealStatus.clientApproved:
        return Colors.teal;
      case DealStatus.clientDeclined:
        return Colors.red.shade700;
      case DealStatus.superseded:
        return Colors.brown;
      case DealStatus.closed:
        return Colors.blue;
      case DealStatus.quotationGenerated:
        return Colors.indigo;
    }
  }
}
