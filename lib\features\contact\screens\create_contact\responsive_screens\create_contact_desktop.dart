import 'package:alloy/features/contact/screens/create_contact/widgets/enhanced_create_contact_form.dart';
import 'package:alloy/routes/routes.dart';
import 'package:flutter/material.dart';
import '../../../../../common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import '../../../../../utils/constants/sizes.dart';

class CreateContactDesktop extends StatelessWidget {
  const CreateContactDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Back to Contacts List',
                breadcrumbItems: [
                  TBreadcrumbItem(text: 'Contacts', route: TRoutes.contacts),
                  TBreadcrumbItem(text: 'Create New'),
                ],
                showBackButton: true,
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              const EnhancedCreateContactForm(),
            ],
          ),
        ),
      ),
    );
  }
}
