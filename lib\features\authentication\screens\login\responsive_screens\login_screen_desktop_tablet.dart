import 'package:alloy/common/layouts/templates/login_template.dart';
import 'package:alloy/features/authentication/screens/login/widgets/header_widget.dart';
import 'package:alloy/features/authentication/screens/login/widgets/login_form_widget.dart';
import 'package:flutter/material.dart';

class LoginScreenDesktopTablet extends StatelessWidget {
  const LoginScreenDesktopTablet({super.key});

  @override
  Widget build(BuildContext context) {
    return TLoginTemplate(
      child: Column(
        children: [
          /// -- Header
          THeaderWidget(),

          /// -- FORM
          TLoginFormWidget(),
        ],
      ),
    );
  }
}
