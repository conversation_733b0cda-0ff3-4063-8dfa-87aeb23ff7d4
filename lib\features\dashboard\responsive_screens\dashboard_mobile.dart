import 'package:flutter/material.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/features/dashboard/table/dashboard_order_table.dart';
import 'package:alloy/features/dashboard/widgets/dashboard_card.dart';
import 'package:alloy/features/dashboard/widgets/order_status_pie_chart.dart';
import 'package:alloy/features/dashboard/widgets/weekly_sales_graph.dart';
import 'package:alloy/utils/constants/sizes.dart';

class DashboardMobile extends StatelessWidget {
  const DashboardMobile({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Heading
              Text('Dashboard', style: Theme.of(context).textTheme.headlineLarge),
              SizedBox(height: TSizes.spaceBtwSections),

              // Cards
              TDashboardCard(title: 'Sales Total', subtitle: '\$12,000', stats: 25),
              SizedBox(height: TSizes.spaceBtwItems),
              TDashboardCard(title: 'Average Order Value', subtitle: '\$120', stats: 15),
              SizedBox(height: TSizes.spaceBtwItems),
              TDashboardCard(title: 'Total Orders', subtitle: '36', stats: 44),
              SizedBox(height: TSizes.spaceBtwItems),
              TDashboardCard(title: 'Visitors', subtitle: '25,035', stats: 2),

              // Bar Graph
              TWeeklySalesGraph(),
              SizedBox(height: TSizes.spaceBtwSections),

              /// Orders
              TRoundedContainer(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Recent Orders', style: Theme.of(context).textTheme.headlineSmall),
                    const SizedBox(height: TSizes.spaceBtwSections),
                    const DashboardOrderTable(),
                  ],
                ),
              ),
              SizedBox(height: TSizes.spaceBtwSections),

              // Pie Chart
              TOrderStatusPieChart(),
            ],
          ),
        ),
      ),
    );
  }
}
