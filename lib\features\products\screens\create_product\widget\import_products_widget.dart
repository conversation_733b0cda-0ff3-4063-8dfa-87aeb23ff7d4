import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../../../../../common/media/models/image_model.dart';
import '../../../../../common/media/widgets/image_popup.dart';
import '../../../../../common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../routes/routes.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../controller/product_controller/import_products_controller.dart'; // For Iconsax icons

class ImportProductsWidget extends StatelessWidget {
  const ImportProductsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ImportProductsController()); // Initialize the controller

    // Define the expected column headers for the Excel file
    final List<String> excelColumns = [
      'Product Name (Required)',
      'Category (Required)',
      'Description',
      'Segment (e.g., lengths, accessories)',
      'Width (mm)',
      'Height (mm)',
      'Material (e.g., GI, HDG)',
      'Thickness (e.g., 1.2mm)',
      'Finish (e.g., Standard, Epoxy)',
      'Length (e.g., 3.0m)',
      'SKU (Unique Identifier)',
      'Quantity On Hand',
      'Quantity On Order',
      'Quantity In Production',
      'Thumbnail URL (Optional)',
    ];

    // Updated placeholder image URL for a more descriptive Excel template look
    final String placeholderImageUrl =
        'https://placehold.co/1000x300/E0E0E0/333333?text=Excel+Import+Template\n\n1.+Product+Name+%7C+2.+Category+%7C+3.+Description+%7C+4.+Segment+%7C+5.+Width+%7C+6.+Height+%7C+7.+Material+%7C+8.+Thickness+%7C+9.+Finish+%7C+10.+Length+%7C+11.+SKU+%7C+12.+Qty+On+Hand+%7C+13.+Qty+On+Order+%7C+14.+Qty+In+Production+%7C+15.+Thumbnail+URL';

    // Create a dummy ImageModel for the popup
    final ImageModel sampleImageModel = ImageModel(
      id: 'excel_template_guide',
      url: placeholderImageUrl,
      filename: 'Product_Import_Template_Guide.png',
      folder: 'Import Guides',
      sizeBytes: 1000 * 300 * 3, // Approximate size for a 1000x300 image
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Import Products',
                breadcrumbItems: [
                  TBreadcrumbItem(text: 'Products', route: TRoutes.products),
                  TBreadcrumbItem(text: 'Import'),
                ],
                showBackButton: true,
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Import Form Section
              TRoundedContainer(
                padding: const EdgeInsets.all(TSizes.defaultSpace),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Upload Excel File', style: Theme.of(context).textTheme.headlineSmall),
                    const SizedBox(height: TSizes.spaceBtwSections),

                    // Instructions for Excel Format - Now as bullet points
                    Text('Please ensure your Excel file follows this column order:', style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: TSizes.spaceBtwItems),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: List.generate(excelColumns.length, (index) {
                        return Padding(
                          padding: const EdgeInsets.only(bottom: TSizes.xs),
                          child: Text('${index + 1}. ${excelColumns[index]}', style: Theme.of(context).textTheme.bodyMedium),
                        );
                      }),
                    ),
                    const SizedBox(height: TSizes.spaceBtwSections),

                    // Sample Table Image (Placeholder) with GestureDetector for Popup
                    Text('Click on the image below to view the full Excel file structure example:', style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: TSizes.spaceBtwItems),
                    Center(
                      child: GestureDetector(
                        onTap: () {
                          showDialog(
                            context: context,
                            builder: (dialogContext) => ImagePopup(image: sampleImageModel),
                          );
                        },
                        child: Image.network(
                          placeholderImageUrl,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) => const Icon(Icons.broken_image, size: 100, color: Colors.grey),
                        ),
                      ),
                    ),
                    const SizedBox(height: TSizes.spaceBtwSections),

                    // File Selection Area
                    GestureDetector(
                      onTap: () => controller.pickAndImportFile(),
                      child: Obx(
                        () => TRoundedContainer(
                          width: double.infinity,
                          height: 150,
                          borderColor: Colors.grey.shade200,
                          radius: TSizes.cardRadiusLg,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(controller.selectedFileName.value.isEmpty ? Iconsax.document_upload : Iconsax.document_text, size: 50, color: Colors.grey),
                              const SizedBox(height: TSizes.spaceBtwItems),
                              Text(
                                controller.selectedFileName.value.isEmpty ? 'Tap to select Excel file (.xlsx)' : controller.selectedFileName.value,
                                style: Theme.of(context).textTheme.bodyLarge,
                                textAlign: TextAlign.center,
                              ),
                              if (controller.selectedFileName.value.isNotEmpty)
                                Text(
                                  'File selected. Tap again to change or proceed to import.',
                                  style: Theme.of(context).textTheme.labelMedium!.copyWith(color: Colors.grey),
                                  textAlign: TextAlign.center,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: TSizes.spaceBtwSections),

                    // Action Buttons (Import and Download Template)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // Download Template Button
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: () => controller.exportExcelTemplate(),
                            icon: const Icon(Iconsax.document_download),
                            label: const Text('Download Template'),
                          ),
                        ),
                        const SizedBox(width: TSizes.spaceBtwItems),
                        // Import Button (only enabled if a file is selected)
                        Expanded(
                          child: Obx(
                            () => ElevatedButton(
                              onPressed: controller.selectedFileName.value.isNotEmpty
                                  ? () => controller.importSelectedFile()
                                  : null, // Disable button if no file selected
                              child: const Text('Import Products'),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
