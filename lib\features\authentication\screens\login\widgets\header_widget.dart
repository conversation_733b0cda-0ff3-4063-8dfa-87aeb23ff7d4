import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:flutter/material.dart';

class THeaderWidget extends StatelessWidget {
  const THeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,

      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Image(image: AssetImage(TImages.bonnAppLogo), width: 100, height: 100),
          SizedBox(height: TSizes.spaceBtwSections),
          Text(TTexts.loginTitle, style: Theme.of(context).textTheme.headlineMedium),
          SizedBox(height: TSizes.sm),
          Text(TTexts.loginSubTitle, style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }
}
