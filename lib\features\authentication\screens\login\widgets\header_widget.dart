import 'package:alloy/features/settings/controllers/settings_controller.dart';
import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class THeaderWidget extends StatelessWidget {
  const THeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Dynamic App Logo with Settings Integration
          Obx(() {
            final settingsController = Get.find<SettingsController>();
            final appLogoUrl = settingsController.appLogoUrl.value;

            return Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
                child: appLogoUrl.isNotEmpty
                    ? Image.network(
                        appLogoUrl,
                        width: 120,
                        height: 120,
                        fit: BoxFit.contain,
                        errorBuilder: (context, error, stackTrace) {
                          return Image.asset(TImages.bonnAppLogo, width: 120, height: 120, fit: BoxFit.contain);
                        },
                      )
                    : Image.asset(TImages.bonnAppLogo, width: 120, height: 120, fit: BoxFit.contain),
              ),
            );
          }),
          const SizedBox(height: TSizes.spaceBtwSections),

          // Dynamic Company Name with Settings Integration
          Obx(() {
            final settingsController = Get.find<SettingsController>();
            final companyName = settingsController.globalSettings.value.companyName;

            return Text(
              companyName.isNotEmpty ? 'Welcome to $companyName' : TTexts.loginTitle,
              style: Theme.of(
                context,
              ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor),
              textAlign: TextAlign.center,
            );
          }),
          const SizedBox(height: TSizes.sm),

          // Enhanced Subtitle
          Text(
            TTexts.loginSubTitle,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).textTheme.bodyMedium?.color?.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: TSizes.spaceBtwSections),
        ],
      ),
    );
  }
}
