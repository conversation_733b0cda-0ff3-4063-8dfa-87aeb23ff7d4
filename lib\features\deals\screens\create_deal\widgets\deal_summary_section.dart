import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/formatters/formatter.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_controller.dart';
import '../../../controllers/deal_item_controller.dart';

/// Deal Summary Section for Stage 2 Deal Creation
/// Shows real-time calculations, totals, and deal overview
class DealSummarySection extends StatelessWidget {
  const DealSummarySection({super.key});

  @override
  Widget build(BuildContext context) {
    final dealController = Get.put(DealController());
    final itemController = Get.put(DealItemController());
    final dark = THelperFunctions.isDarkMode(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          children: [
            Icon(Iconsax.receipt, color: TColors.primary),
            const SizedBox(width: TSizes.xs),
            Text('Deal Summary', style: Theme.of(context).textTheme.titleLarge),
          ],
        ),
        const SizedBox(height: TSizes.spaceBtwItems),

        // Deal basic info
        _buildDealBasicInfo(context, dealController),
        const SizedBox(height: TSizes.spaceBtwItems),

        // Items summary
        _buildItemsSummary(context, itemController),
        const SizedBox(height: TSizes.spaceBtwItems),

        // Financial summary
        _buildFinancialSummary(context, itemController),
      ],
    );
  }

  /// Deal basic information
  Widget _buildDealBasicInfo(BuildContext context, DealController controller) {
    return Obx(() {
      final deal = controller.currentDeal.value;
      if (deal == null) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.all(TSizes.md),
        decoration: BoxDecoration(
          color: TColors.light,
          borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
          border: Border.all(color: TColors.borderPrimary),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Deal Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
            ),
            const SizedBox(height: TSizes.spaceBtwItems),

            _buildInfoRow(context, 'Deal Number', deal.dealNumber, Iconsax.document_text),
            const SizedBox(height: TSizes.spaceBtwItems / 2),
            _buildInfoRow(context, 'Client', deal.clientName, Iconsax.user),
            const SizedBox(height: TSizes.spaceBtwItems / 2),
            _buildInfoRow(context, 'Contact', deal.contactPersonName ?? 'Not specified', Iconsax.call),
            const SizedBox(height: TSizes.spaceBtwItems / 2),
            _buildInfoRow(context, 'Status', _getStatusText(deal.status), Iconsax.flag),
          ],
        ),
      );
    });
  }

  /// Items summary section
  Widget _buildItemsSummary(BuildContext context, DealItemController controller) {
    return Obx(() {
      final items = controller.dealItems;
      final totalItems = items.length;
      final totalWeight = items.fold<double>(0.0, (sum, item) => sum + (item.totalFactoryWeightKg ?? 0.0));

      return Container(
        padding: const EdgeInsets.all(TSizes.md),
        decoration: BoxDecoration(
          color: TColors.primary.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
          border: Border.all(color: TColors.primary.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Items Overview',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: TColors.primary),
            ),
            const SizedBox(height: TSizes.spaceBtwItems),

            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(context, 'Total Items', totalItems.toString(), Iconsax.box, TColors.info),
                ),
                const SizedBox(width: TSizes.spaceBtwItems),
                Expanded(
                  child: _buildSummaryCard(
                    context,
                    'Total Weight',
                    '${TFormatter.formatNumber(totalWeight)} kg',
                    Iconsax.weight,
                    TColors.success,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  /// Financial summary section
  Widget _buildFinancialSummary(BuildContext context, DealItemController controller) {
    return Obx(() {
      final items = controller.dealItems;
      final subtotal = items.fold<double>(0.0, (sum, item) => sum + item.totalItemPrice);
      final taxRate = 18.0; // TODO: Get from settings
      final taxAmount = subtotal * (taxRate / 100);
      final grandTotal = subtotal + taxAmount;

      return Container(
        padding: const EdgeInsets.all(TSizes.md),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [TColors.primary.withValues(alpha: 0.1), TColors.primary.withValues(alpha: 0.05)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
          border: Border.all(color: TColors.primary.withValues(alpha: 0.3)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Iconsax.money, color: TColors.primary),
                const SizedBox(width: TSizes.xs),
                Text(
                  'Financial Summary',
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: TColors.primary),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwItems),

            // Subtotal
            _buildFinancialRow(context, 'Subtotal', '₹${TFormatter.formatNumber(subtotal)}', false),
            const SizedBox(height: TSizes.spaceBtwItems / 2),

            // Tax
            _buildFinancialRow(
              context,
              'Tax (${taxRate.toStringAsFixed(0)}%)',
              '₹${TFormatter.formatNumber(taxAmount)}',
              false,
            ),

            const Divider(height: TSizes.spaceBtwItems),

            // Grand total
            _buildFinancialRow(context, 'Grand Total', '₹${TFormatter.formatNumber(grandTotal)}', true),
          ],
        ),
      );
    });
  }

  /// Info row helper
  Widget _buildInfoRow(BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: TColors.darkGrey),
        const SizedBox(width: TSizes.xs),
        Text('$label: ', style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500)),
        Expanded(
          child: Text(value, style: Theme.of(context).textTheme.bodySmall, overflow: TextOverflow.ellipsis),
        ),
      ],
    );
  }

  /// Summary card helper
  Widget _buildSummaryCard(BuildContext context, String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: TSizes.xs),
          Text(title, style: Theme.of(context).textTheme.labelSmall, textAlign: TextAlign.center),
          const SizedBox(height: TSizes.xs / 2),
          Text(
            value,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(color: color, fontWeight: FontWeight.w600),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Financial row helper
  Widget _buildFinancialRow(BuildContext context, String label, String amount, bool isTotal) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: isTotal
              ? Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: TColors.primary)
              : Theme.of(context).textTheme.bodyMedium,
        ),
        Text(
          amount,
          style: isTotal
              ? Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: TColors.primary)
              : Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
        ),
      ],
    );
  }

  /// Get status display text
  String _getStatusText(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return 'Draft';
      case DealStatus.pendingApproval:
        return 'Pending Approval';
      case DealStatus.approved:
        return 'Approved';
      case DealStatus.rejected:
        return 'Rejected';
      case DealStatus.unlockRequested:
        return 'Unlock Requested';
      case DealStatus.quotationGenerated:
        return 'Quotation Generated';
      case DealStatus.clientApproved:
        return 'Client Approved';
      case DealStatus.clientDeclined:
        return 'Client Declined';
      case DealStatus.superseded:
        return 'Superseded';
      case DealStatus.closed:
        return 'Closed';
    }
  }
}
