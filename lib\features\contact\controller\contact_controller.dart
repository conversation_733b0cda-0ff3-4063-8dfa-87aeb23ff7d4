import 'package:alloy/data/abstract/base_data_table_controller.dart';
import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/features/contact/repository/contact_repository.dart';
import 'package:get/get.dart' hide Rx;

class ContactController extends TBaseController<ContactModel> {
  final ContactRepository _contactRepository = ContactRepository.instance;

  @override
  Future<List<ContactModel>> fetchItems() async {
    final contacts = await _contactRepository.getAllContacts();
    allItems.assignAll(contacts);
    return allItems;
  }

  @override
  Stream<List<ContactModel>> streamItems() {
    return _contactRepository.streamAllContacts();
  }

  @override
  bool containsSearchQuery(ContactModel item, String searchText) {
    final searchLower = searchText.toLowerCase();
    return item.name.toLowerCase().contains(searchLower) ||
        (item.email?.toLowerCase().contains(searchLower) ?? false) ||
        item.phone.toLowerCase().contains(searchLower) ||
        (item.designation?.toLowerCase().contains(searchLower) ?? false);
  }

  @override
  Future<void> deleteItem(ContactModel item) {
    return _contactRepository.deleteContact(item.id);
  }

  @override
  Comparable getComparableProperty(ContactModel item, int columnIndex) {
    // Implement this method to return a Comparable value for sorting based on column index
    // Ensure you always return a String, int, double, or DateTime.
    // Handle null values by providing a default comparable value (e.g., empty string, 0).
    switch (columnIndex) {
      case 0:
        return item.name.toLowerCase(); // Name
      case 1:
        return item.email?.toLowerCase() ?? ''; // Email (assuming nullable)
      case 2:
        return item.phone.toLowerCase(); // Phone (non-nullable in ContactModel)
      case 3:
        return item.designation?.toLowerCase() ?? ''; // Designation (assuming nullable)
      case 4:
        return item.active.toString(); // Status (boolean converted to string "true" or "false")
      case 5:
        return item.accountDetails?.firstOrNull?.name.toLowerCase() ?? ''; // Accounts (sort by first account name, if any)
      case 6:
        return ''; // Action column is not sortable
      default:
        return ''; // Fallback for undefined columns
    }
  }
}
