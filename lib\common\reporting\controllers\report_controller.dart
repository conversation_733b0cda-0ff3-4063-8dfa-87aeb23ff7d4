import 'package:file_saver/file_saver.dart';
import 'package:get/get.dart';
import 'package:flutter/foundation.dart';
import 'package:printing/printing.dart';

import '../../../features/account/controller/account_controller.dart';
import '../../../features/contact/controller/contact_controller.dart';
import '../models/report_config.dart';
import '../services/pdf_service.dart';
import '../services/excel_service.dart';
import '../models/report_type.dart'; // Import the new enum
import '../../../utils/popups/full_screen_loader.dart';
import '../../../utils/popups/loaders.dart';
import 'package:universal_html/html.dart'
    show AnchorElement, Blob, Url; // <-- Explicitly import Blob, Url, AnchorElement

class ReportController extends GetxController {
  static ReportController get instance => Get.find();

  final pdfService = PdfService();
  final excelService = ExcelService();

  /// Generic function to process and generate any report (PDF or Excel).
  Future<void> _processReport({required ReportType type, required String fileExtension}) async {
    try {
      TFullScreenLoader.popUpCircular();

      // 1. Get the ReportConfig for the requested type
      ReportConfig config;
      List<dynamic> models; // Raw list of models

      switch (type) {
        case ReportType.accounts:
          final accountController = Get.find<AccountController>();
          config = ReportConfig.accountsReport;
          models = accountController.allItems.toList();
          break;
        case ReportType.contacts:
          final contactController = Get.find<ContactController>();
          config = ReportConfig.contactsReport;
          models = contactController.allItems.toList();
          break;
        case ReportType.accountsWithContacts:
          config = ReportConfig.accountsWithContactsReport;
          models = Get.find<AccountController>().allItems.toList(); // Fetch accounts, as contacts are nested
          break;
        // Add more cases for future report types here
        // case ReportType.salesOrders:
        //   config = ReportConfig.salesOrdersReport;
        //   models = Get.find<SalesOrderController>().allItems.toList();
        //   break;
      }

      if (models.isEmpty) {
        TLoaders.warningSnackBar(
          title: 'No Data',
          message: 'No ${config.title.toLowerCase()} found to generate report.',
        );
        TFullScreenLoader.stopLoading();
        return;
      }

      // 2. Call the appropriate service based on fileExtension
      if (fileExtension == 'pdf') {
        final bytes = await pdfService.generatePdfReport(config, models);
        _handleFileDownload(bytes, config.filenamePrefix, fileExtension, 'application/pdf');
      } else if (fileExtension == 'xlsx') {
        await excelService.exportExcelReport(config, models);
        // ExcelService handles its own file saving/download logic.
      } else {
        TLoaders.errorSnackBar(title: 'Error', message: 'Unsupported file type: $fileExtension');
      }

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: '${fileExtension.toUpperCase()} Generated',
        message: '${config.title} ${fileExtension.toUpperCase()} generated successfully.',
      );
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: 'Failed to generate ${type.name} report: $e');
      print(e); // Log the error for debugging
    }
  }

  // Helper for actual file download/preview (common for PDF, Excel handles itself)
  Future<void> _handleFileDownload(
    Uint8List bytes,
    String filenamePrefix,
    String fileExtension,
    String mimeType,
  ) async {
    String filename = 'BonnMetals_${filenamePrefix}_Report.$fileExtension';

    if (kIsWeb) {
      final Blob blob = Blob([bytes], mimeType);
      final url = Url.createObjectUrlFromBlob(blob);
      AnchorElement(href: url)
        ..setAttribute('download', filename)
        ..click();
      await Future.delayed(const Duration(milliseconds: 100)); // Small delay for web downloads
      Url.revokeObjectUrl(url);
    } else {
      MimeType fsMimeType = _getMimeTypeForFileSaver(fileExtension);
      if (fileExtension == 'pdf') {
        await Printing.layoutPdf(onLayout: (format) => bytes);
      } else {
        await FileSaver.instance.saveFile(name: filename, bytes: bytes, ext: fileExtension, mimeType: fsMimeType);
      }
    }
  }

  MimeType _getMimeTypeForFileSaver(String fileExtension) {
    switch (fileExtension) {
      case 'pdf':
        return MimeType.pdf;
      case 'xlsx':
        return MimeType.microsoftExcel;
      case 'csv':
        return MimeType.csv;
      default:
        return MimeType.other;
    }
  }

  // Public methods to be called from UI actions (e.g., buttons)
  Future<void> generateAndPreviewPdf(ReportType type) async {
    await _processReport(type: type, fileExtension: 'pdf');
  }

  Future<void> exportToExcel(ReportType type) async {
    await _processReport(type: type, fileExtension: 'xlsx');
  }
}
