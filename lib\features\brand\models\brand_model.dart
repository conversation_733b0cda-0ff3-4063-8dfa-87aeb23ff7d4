import 'package:alloy/features/category/models/category_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:alloy/utils/formatters/formatter.dart';

class BrandModel {
  String id;
  String name;
  String image;
  bool isFeatured;
  int? productsCount;
  DateTime? createdAt;
  DateTime? updatedAt;

  // Not mapped
  List<CategoryModel>? brandCategories;

  BrandModel({
    required this.id,
    required this.image,
    required this.name,
    this.isFeatured = false,
    this.productsCount,
    this.createdAt,
    this.updatedAt,
    this.brandCategories,
  });

  /// Empty Helper Function
  static BrandModel empty() => BrandModel(id: '', image: '', name: '');

  String get formattedDate => TFormatter.formatDate(createdAt);
  String get formattedCreatedAtDate => TFormatter.formatDate(createdAt);
  String get formattedUpdatedAtDate => TFormatter.formatDate(updatedAt);

  /// CopyWith method for updating specific fields
  BrandModel copyWith({String? id, String? name, String? image, bool? isFeatured, int? productsCount}) {
    return BrandModel(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      isFeatured: isFeatured ?? this.isFeatured,
      productsCount: productsCount ?? this.productsCount,
      createdAt: createdAt,
      updatedAt: updatedAt,
      // brandCategories: brandCategories,
    );
  }

  /// Convert model to Json structure so that you can store data in Firebase

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'image': image,
      'createdAt': createdAt,
      'isFeatured': isFeatured,
      'productsCount': productsCount = 0,
      'updatedAt': updatedAt = DateTime.now(),
    };
  }

  /// Factory constructor from a Map (e.g., for local data or JSON decoding)
  factory BrandModel.fromJson(Map<String, dynamic> document) {
    final data = document;

    if (data.isEmpty) return BrandModel.empty();
    return BrandModel(
      id: data['id'] ?? '',
      name: data['name'] ?? '',
      image: data['image'] ?? '',
      isFeatured: data['isFeatured'] ?? false,
      productsCount: int.parse((data['productsCount'] ?? 0).toString()),

      createdAt: data.containsKey('createdAt') ? data['createdAt']?.toDate() : null,
      updatedAt: data.containsKey('updatedAt') ? data['updatedAt']?.toDate() : null,
    );
  }

  /// Map Json oriented document snapshot from Firebase to BrandModel
  factory BrandModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (document.data() != null) {
      final data = document.data()!;
      return BrandModel(
        id: document.id,
        name: data['name'] ?? '',
        image: data['image'] ?? '',
        productsCount: data['productsCount'] ?? '',
        isFeatured: data['isFeatured'] ?? false,
        createdAt: data.containsKey('createdAt') ? data['createdAt']?.toDate() : null,
        updatedAt: data.containsKey('updatedAt') ? data['updatedAt']?.toDate() : null,
      );
    } else {
      return BrandModel.empty();
    }
  }

  @override
  String toString() {
    return 'BrandModel(id: $id, name: $name, image: $image, isFeatured: $isFeatured, productsCount: $productsCount, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}
