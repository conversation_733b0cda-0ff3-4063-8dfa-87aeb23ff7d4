import 'package:alloy/features/authentication/repository/authentication_repository.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:alloy/utils/exceptions/firebase_exceptions.dart';
import 'package:alloy/utils/exceptions/format_exceptions.dart';
import 'package:alloy/utils/exceptions/platform_exceptions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class UserRepository extends GetxController {
  static UserRepository get instance => Get.find();

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  /// Save new user record to Firestore.
  Future<void> createUser(UserModel user) async {
    try {
      final userJson = user.toJson();
      // Ensure timestamps are set for Firestore
      userJson['createdAt'] = FieldValue.serverTimestamp();
      userJson['updatedAt'] = FieldValue.serverTimestamp();
      await _db.collection('users').doc(user.id).set(userJson);
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Get all user records from Firestore.
  Future<List<UserModel>> getAllUsers() async {
    try {
      final snapshot = await _db.collection('users').get();
      return snapshot.docs.map((document) => UserModel.fromSnapshot(document)).toList();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Fetch a user record once from Firestore using their ID.
  Future<UserModel> fetchUserData() async {
    try {
      final documentSnapshot = await _db.collection('users').doc(AuthenticationRepository.instance.authUser?.uid).get();
      if (documentSnapshot.exists) {
        return UserModel.fromSnapshot(documentSnapshot);
      } else {
        return UserModel.empty();
      }
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Stream User Record for real-time updates from Firestore.
  Stream<UserModel> streamUserRecord(String userId) {
    return _db.collection("users").doc(userId).snapshots().map((documentSnapshot) {
      if (documentSnapshot.exists) {
        return UserModel.fromSnapshot(documentSnapshot);
      } else {
        return UserModel.empty();
      }
    });
  }

  /// Stream all user records for real-time updates from Firestore.
  Stream<List<UserModel>> streamAllUsers() {
    try {
      return _db.collection('users').snapshots().map((snapshot) {
        return snapshot.docs.map((doc) => UserModel.fromSnapshot(doc)).toList();
      });
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }
}
