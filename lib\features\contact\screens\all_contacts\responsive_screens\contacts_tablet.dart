import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/data_table/table_header.dart';
import 'package:alloy/features/contact/controller/contact_controller.dart';
import 'package:alloy/features/contact/screens/all_contacts/data_table/contacts_table.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ContactsTablet extends StatelessWidget {
  const ContactsTablet({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ContactController>();
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Contacts',
                breadcrumbItems: [TBreadcrumbItem(text: 'Contacts')],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Table Body
              TRoundedContainer(
                child: Column(
                  children: [
                    // Table Header
                    TTableHeader(
                      hintText: 'Search Contacts',
                      searchOnChanged: (query) => controller.searchQuery(query),
                      actions: [ElevatedButton(onPressed: () => Get.toNamed(TRoutes.createContact), child: const Text('Create New Contact'))],
                    ),
                    const SizedBox(height: TSizes.spaceBtwItems),

                    // Table
                    Obx(() {
                      if (controller.isLoading.value) return const Center(child: CircularProgressIndicator());
                      return const ContactsTable();
                    }),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
