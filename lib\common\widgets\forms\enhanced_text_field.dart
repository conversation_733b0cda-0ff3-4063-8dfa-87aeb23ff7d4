import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../utils/constants/sizes.dart';
import '../../../utils/validators/validation.dart';

/// Enhanced text field with improved validation, styling, and UX features
class TEnhancedTextField extends StatefulWidget {
  const TEnhancedTextField({
    super.key,
    required this.controller,
    required this.labelText,
    this.hintText,
    this.helperText,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.onChanged,
    this.onFieldSubmitted,
    this.focusNode,
    this.nextFocusNode,
    this.textInputAction,
    this.keyboardType,
    this.inputFormatters,
    this.maxLines = 1,
    this.maxLength,
    this.enabled = true,
    this.readOnly = false,
    this.obscureText = false,
    this.isRequired = false,
    this.showCharacterCount = false,
    this.enableRealTimeValidation = true,
    this.customErrorColor,
    this.customSuccessColor,
    this.suffixText,
    this.prefixText,
  });

  final TextEditingController controller;
  final String labelText;
  final String? hintText;
  final String? helperText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;
  final FocusNode? focusNode;
  final FocusNode? nextFocusNode;
  final TextInputAction? textInputAction;
  final TextInputType? keyboardType;
  final List<TextInputFormatter>? inputFormatters;
  final int maxLines;
  final int? maxLength;
  final bool enabled;
  final bool readOnly;
  final bool obscureText;
  final bool isRequired;
  final bool showCharacterCount;
  final bool enableRealTimeValidation;
  final Color? customErrorColor;
  final Color? customSuccessColor;
  final String? suffixText;
  final String? prefixText;

  @override
  State<TEnhancedTextField> createState() => _TEnhancedTextFieldState();
}

class _TEnhancedTextFieldState extends State<TEnhancedTextField> {
  String? _errorText;
  bool _hasBeenFocused = false;
  bool _isValid = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
    widget.focusNode?.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    widget.focusNode?.removeListener(_onFocusChanged);
    super.dispose();
  }

  void _onTextChanged() {
    if (widget.enableRealTimeValidation && _hasBeenFocused) {
      _validateField();
    }
    widget.onChanged?.call(widget.controller.text);
  }

  void _onFocusChanged() {
    if (widget.focusNode?.hasFocus == false) {
      setState(() {
        _hasBeenFocused = true;
      });
      _validateField();
    }
  }

  void _validateField() {
    if (widget.validator != null) {
      final error = widget.validator!(widget.controller.text);
      setState(() {
        _errorText = error;
        _isValid = error == null && widget.controller.text.isNotEmpty;
      });
    }
  }

  Color? _getBorderColor(ThemeData theme) {
    if (!widget.enabled) return theme.disabledColor;
    if (_errorText != null) return widget.customErrorColor ?? theme.colorScheme.error;
    if (_isValid) return widget.customSuccessColor ?? Colors.green;
    return null;
  }

  Widget? _buildSuffixIcon(ThemeData theme) {
    if (widget.suffixIcon != null) return widget.suffixIcon;

    if (widget.enableRealTimeValidation && _hasBeenFocused) {
      if (_errorText != null) {
        return Icon(Icons.error_outline, color: widget.customErrorColor ?? theme.colorScheme.error, size: 20);
      } else if (_isValid) {
        return Icon(Icons.check_circle_outline, color: widget.customSuccessColor ?? Colors.green, size: 20);
      }
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final borderColor = _getBorderColor(theme);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator
        if (widget.labelText.isNotEmpty) ...[
          Row(
            children: [
              Text(widget.labelText, style: theme.textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w500)),
              if (widget.isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: TextStyle(color: theme.colorScheme.error, fontWeight: FontWeight.bold),
                ),
              ],
            ],
          ),
          const SizedBox(height: TSizes.xs),
        ],

        // Text Field
        TextFormField(
          controller: widget.controller,
          focusNode: widget.focusNode,
          validator: widget.validator,
          onFieldSubmitted: (value) {
            widget.onFieldSubmitted?.call(value);
            if (widget.nextFocusNode != null) {
              FocusScope.of(context).requestFocus(widget.nextFocusNode);
            }
          },
          textInputAction:
              widget.textInputAction ?? (widget.nextFocusNode != null ? TextInputAction.next : TextInputAction.done),
          keyboardType: widget.keyboardType,
          inputFormatters: widget.inputFormatters,
          maxLines: widget.maxLines,
          maxLength: widget.maxLength,
          enabled: widget.enabled,
          readOnly: widget.readOnly,
          obscureText: widget.obscureText,
          decoration: InputDecoration(
            hintText: widget.hintText,
            prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
            suffixIcon: _buildSuffixIcon(theme),
            prefixText: widget.prefixText,
            suffixText: widget.suffixText,
            errorText: _errorText,
            helperText: widget.helperText,
            counterText: widget.showCharacterCount ? null : '',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
              borderSide: BorderSide(color: borderColor ?? theme.dividerColor, width: 1),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
              borderSide: BorderSide(color: borderColor ?? theme.dividerColor, width: 1),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
              borderSide: BorderSide(color: borderColor ?? theme.primaryColor, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
              borderSide: BorderSide(color: widget.customErrorColor ?? theme.colorScheme.error, width: 1),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
              borderSide: BorderSide(color: widget.customErrorColor ?? theme.colorScheme.error, width: 2),
            ),
          ),
        ),
      ],
    );
  }
}

/// Specialized enhanced text field for numeric inputs
class TEnhancedNumberField extends StatelessWidget {
  const TEnhancedNumberField({
    super.key,
    required this.controller,
    required this.labelText,
    this.hintText,
    this.helperText,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.onChanged,
    this.onFieldSubmitted,
    this.focusNode,
    this.nextFocusNode,
    this.enabled = true,
    this.isRequired = false,
    this.allowDecimals = true,
    this.allowNegative = false,
    this.min,
    this.max,
    this.suffixText,
    this.prefixText,
  });

  final TextEditingController controller;
  final String labelText;
  final String? hintText;
  final String? helperText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onFieldSubmitted;
  final FocusNode? focusNode;
  final FocusNode? nextFocusNode;
  final bool enabled;
  final bool isRequired;
  final bool allowDecimals;
  final bool allowNegative;
  final double? min;
  final double? max;
  final String? suffixText;
  final String? prefixText;

  String? _validateNumber(String? value) {
    // First run custom validator if provided
    final customError = validator?.call(value);
    if (customError != null) return customError;

    // Required field validation
    if (isRequired && (value == null || value.isEmpty)) {
      return '$labelText is required';
    }

    // Skip validation if field is empty and not required
    if (value == null || value.isEmpty) return null;

    // Numeric validation
    if (!TValidator.isNumeric(value)) {
      return 'Please enter a valid number';
    }

    final numValue = double.tryParse(value);
    if (numValue == null) return 'Please enter a valid number';

    // Negative number validation
    if (!allowNegative && numValue < 0) {
      return 'Negative values are not allowed';
    }

    // Range validation
    if (min != null && numValue < min!) {
      return 'Value must be at least $min';
    }
    if (max != null && numValue > max!) {
      return 'Value must be at most $max';
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return TEnhancedTextField(
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      validator: _validateNumber,
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
      focusNode: focusNode,
      nextFocusNode: nextFocusNode,
      enabled: enabled,
      isRequired: isRequired,
      keyboardType: TextInputType.numberWithOptions(decimal: allowDecimals, signed: allowNegative),
      inputFormatters: [
        if (!allowDecimals) FilteringTextInputFormatter.digitsOnly,
        if (allowDecimals && !allowNegative) FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
        if (allowDecimals && allowNegative) FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d*')),
      ],
      suffixText: suffixText,
      prefixText: prefixText,
    );
  }
}
