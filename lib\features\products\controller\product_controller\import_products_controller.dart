import 'dart:typed_data';
import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/features/products/models/product_model.dart';
import 'package:alloy/features/products/models/product_variant_model.dart';
import 'package:alloy/features/products/repository/product_repository.dart';
import 'package:alloy/features/products/repository/product_variant_repository.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:excel/excel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:file_saver/file_saver.dart';
import 'package:get/get.dart';

class ImportProductsController extends GetxController {
  static ImportProductsController get instance => Get.find();

  // --- Repositories & Other Controllers
  final _productRepository = Get.put(ProductRepository());
  final _variantRepository = ProductVariantRepository.instance; // Get.find() if already put
  final _categoryController = Get.put(CategoryController());

  // --- State Variables
  final RxString selectedFileName = ''.obs;
  Uint8List? _selectedFileBytes; // Stores the actual file bytes

  // Define the expected column headers for validation and template export
  final List<String> _expectedHeaders = const [
    'Product Name (Required)',
    'Category (Required)',
    'Description',
    'Segment (e.g., lengths, accessories)',
    'Width (mm)',
    'Height (mm)',
    'Material (e.g., GI, HDG)',
    'Thickness (e.g., 1.2mm)',
    'Finish (e.g., Standard, Epoxy)',
    'Length (e.g., 3.0m)',
    'SKU (Unique Identifier)',
    'Quantity On Hand',
    'Quantity On Order',
    'Quantity In Production',
    'Thumbnail URL (Optional)',
  ];

  @override
  void onInit() {
    super.onInit();
    // Pre-load categories for lookup during import
    if (_categoryController.allItems.isEmpty) {
      _categoryController.fetchItems();
    }
  }

  /// --- Pick Excel File
  Future<void> pickAndImportFile() async {
    try {
      TFullScreenLoader.popUpCircular(); // Show loading indicator

      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['xlsx'],
        allowMultiple: false,
        withData: true, // Crucial to get file bytes
      );

      if (result != null && result.files.single.bytes != null) {
        _selectedFileBytes = result.files.single.bytes;
        selectedFileName.value = result.files.single.name;
        TLoaders.successSnackBar(title: 'File Selected', message: 'Ready to import ${selectedFileName.value}');
      } else {
        selectedFileName.value = '';
        _selectedFileBytes = null;
        TLoaders.warningSnackBar(title: 'No File Selected', message: 'Please select an Excel file (.xlsx) to import.');
      }
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: 'Failed to pick file: $e');
    } finally {
      TFullScreenLoader.stopLoading(); // Hide loading indicator
    }
  }

  /// --- Import Selected File
  Future<void> importSelectedFile() async {
    if (_selectedFileBytes == null) {
      TLoaders.warningSnackBar(title: 'No File', message: 'Please select an Excel file first.');
      return;
    }

    try {
      TFullScreenLoader.popUpCircular(); // Show loading indicator

      // Check Internet Connectivity
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      await _parseAndSaveData(_selectedFileBytes!);

      TFullScreenLoader.stopLoading(); // Hide loading indicator
      TLoaders.successSnackBar(title: 'Success', message: 'Products imported successfully!');

      // Optionally, clear selected file after successful import
      selectedFileName.value = '';
      _selectedFileBytes = null;

      // Navigate back to products list or refresh it
      // Get.offAllNamed(TRoutes.products); // Example navigation
      // Or if you want to just refresh the table on the current screen:
      // Get.find<ProductController>().listenToStream(); // Re-trigger stream to update table
    } catch (e) {
      TFullScreenLoader.stopLoading(); // Ensure loading is stopped on error
      TLoaders.errorSnackBar(title: 'Import Failed', message: e.toString());
    }
  }

  /// Parse the excel data and save it to Firestore
  Future<void> _parseAndSaveData(Uint8List fileBytes) async {
    final excel = Excel.decodeBytes(fileBytes);
    final sheet = excel.tables.keys.first;
    final rows = excel.tables[sheet]!.rows;

    if (rows.isEmpty) {
      throw 'Excel file is empty.';
    }
    if (rows.length < 2) {
      throw 'Excel file contains only headers. No data rows found.';
    }

    // --- Header Row Validation ---
    final List<String> actualHeaders = rows[0].map((data) => data?.value.toString().trim() ?? '').toList();
    _validateHeaders(actualHeaders); // Call the new validation method

    // Ensure categories are loaded for ID lookup
    if (_categoryController.allItems.isEmpty) {
      await _categoryController.fetchItems();
    }
    final categoryMap = {for (var cat in _categoryController.allItems) cat.name.toLowerCase(): cat.id};

    // Group rows by product name to create templates and their variants
    final Map<String, List<List<Data?>>> productGroups = {};
    for (var i = 1; i < rows.length; i++) {
      // Start from index 1 to skip header row
      final row = rows[i];
      // Skip empty rows or rows where the first cell (product name) is empty
      if (row.isEmpty || row[0] == null || row[0]!.value == null || row[0]!.value.toString().trim().isEmpty) continue;

      final productName = row[0]!.value.toString().trim();
      (productGroups[productName] ??= []).add(row);
    }

    // Process each product group
    for (final entry in productGroups.entries) {
      final productName = entry.key;
      final variantRows = entry.value;
      final firstRow = variantRows.first; // The first row for this product name defines the template

      // --- Create Product Template from the first row of the group ---
      // Expected columns for ProductModel:
      // 0: Product Name
      // 1: Category Name
      // 2: Description
      // 3: Segment
      // 4: Width (mm)
      // 5: Height (mm)
      // 14: Thumbnail URL (Optional)

      final categoryName = firstRow[1]?.value.toString().trim().toLowerCase() ?? '';
      final categoryId = categoryMap[categoryName] ?? '';
      if (categoryId.isEmpty) {
        TLoaders.warningSnackBar(title: 'Skipped Product', message: 'Category "$categoryName" not found for product "$productName".');
        continue; // Skip this product and its variants if category is not found
      }

      final product = ProductModel(
        id: '', // Firestore will generate this
        name: productName,
        description: firstRow[2]?.value.toString().trim() ?? '',
        categoryId: categoryId,
        segment: ProductSegment.values.firstWhere(
          (e) => e.name == (firstRow[3]?.value.toString().trim().toLowerCase() ?? ProductSegment.lengths.name),
          orElse: () => ProductSegment.lengths,
        ),
        width: double.tryParse(firstRow[4]?.value.toString() ?? '0') ?? 0,
        height: double.tryParse(firstRow[5]?.value.toString() ?? '0') ?? 0,
        thumbnail: firstRow.length > 14 ? firstRow[14]?.value.toString().trim() : null, // Check length before accessing
      );

      final productId = await _productRepository.createProduct(product);

      // --- Create Variants for this product ---
      // Expected columns for ProductVariantModel (starting from column 6):
      // 6: Material
      // 7: Thickness
      // 8: Finish
      // 9: Length
      // 10: SKU
      // 11: Quantity On Hand
      // 12: Quantity On Order
      // 13: Quantity In Production

      final List<ProductVariantModel> variantsToCreate = [];
      for (final row in variantRows) {
        // Ensure the row has enough columns for variant attributes
        if (row.length < 14) {
          // Check minimum expected columns for variant data (up to Qty In Production)
          TLoaders.warningSnackBar(title: 'Skipped Variant', message: 'Row for product "$productName" is missing variant data columns (expected at least 14).');
          continue;
        }

        final attributes = {
          'Material': row[6]?.value.toString().trim() ?? '',
          'Thickness': row[7]?.value.toString().trim() ?? '',
          'Finish': row[8]?.value.toString().trim() ?? '',
          'Length': row[9]?.value.toString().trim() ?? '',
        };
        final sku = row[10]?.value.toString().trim() ?? '';

        // Basic validation for SKU and essential attributes
        if (sku.isEmpty ||
            attributes['Material']!.isEmpty ||
            attributes['Thickness']!.isEmpty ||
            attributes['Finish']!.isEmpty ||
            attributes['Length']!.isEmpty) {
          TLoaders.warningSnackBar(title: 'Skipped Variant', message: 'Variant for product "$productName" (SKU: $sku) is missing essential attributes or SKU.');
          continue;
        }

        variantsToCreate.add(
          ProductVariantModel(
            id: '', // Firestore will generate this
            productId: productId,
            categoryId: product.categoryId, // Use the parent product's category
            segment: product.segment, // Use the parent product's segment
            sku: sku,
            attributes: attributes,
            weight: null, // Not imported, will be calculated by the app
            sqm: null, // Not imported, will be calculated by the app
            quantityOnHand: int.tryParse(row[11]?.value.toString() ?? '0') ?? 0,
            quantityOnOrder: int.tryParse(row[12]?.value.toString() ?? '0') ?? 0,
            quantityInProduction: int.tryParse(row[13]?.value.toString() ?? '0') ?? 0,
          ),
        );
      }

      if (variantsToCreate.isNotEmpty) {
        await _variantRepository.createVariantsInBatch(variantsToCreate);
      } else {
        TLoaders.warningSnackBar(
          title: 'No Variants',
          message: 'No valid variants found for product "$productName". Product template created without variants.',
        );
      }
    }
  }

  /// Validates the header row of the Excel file against expected headers.
  /// Throws an exception if critical headers are missing or out of order.
  void _validateHeaders(List<String> actualHeaders) {
    // Define critical headers that must be present and in order
    final List<String> criticalHeaders = [
      _expectedHeaders[0], // Product Name
      _expectedHeaders[1], // Category
      _expectedHeaders[2], // Description
      _expectedHeaders[3], // Segment
      _expectedHeaders[4], // Width
      _expectedHeaders[5], // Height
      _expectedHeaders[6], // Material
      _expectedHeaders[7], // Thickness
      _expectedHeaders[8], // Finish
      _expectedHeaders[9], // Length
      _expectedHeaders[10], // SKU
    ];

    // Check if the actual headers contain all critical headers in the correct order
    if (actualHeaders.length < criticalHeaders.length) {
      throw 'Invalid Excel file format. Expected at least ${criticalHeaders.length} columns for core data.';
    }

    for (int i = 0; i < criticalHeaders.length; i++) {
      // Compare trimmed and lowercased headers for case-insensitivity and leading/trailing spaces
      if (actualHeaders[i].toLowerCase() != criticalHeaders[i].toLowerCase()) {
        throw 'Invalid Excel file format. Expected column "${criticalHeaders[i]}" at position ${i + 1}, but found "${actualHeaders[i]}". Please check your column headers and order.';
      }
    }

    // Optional: You could add warnings for non-critical missing/misplaced columns here
    // For example, if 'Thumbnail URL' is missing, you might log a warning instead of throwing an error.
    if (actualHeaders.length < _expectedHeaders.length) {
      TLoaders.warningSnackBar(
        title: 'Missing Columns',
        message:
            'Some optional columns like "Quantity On Hand", "Quantity On Order", "Quantity In Production", or "Thumbnail URL" might be missing. Data for these will be defaulted.',
      );
    }
  }

  /// Exports an empty Excel template with predefined headers.
  Future<void> exportExcelTemplate() async {
    try {
      TFullScreenLoader.popUpCircular();

      final excel = Excel.createExcel();
      final sheet = excel.sheets[excel.getDefaultSheet()!];

      // Add headers to the first row
      for (int i = 0; i < _expectedHeaders.length; i++) {
        // Corrected: Use CellIndex.indexByColumnRow and cast String to TextCellValue
        sheet!.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).value = TextCellValue(_expectedHeaders[i]);
        // Optional: Apply some basic styling to headers
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0)).cellStyle = CellStyle(
          bold: true,
          horizontalAlign: HorizontalAlign.Center,
          verticalAlign: VerticalAlign.Center,
        );
      }

      // Save the Excel file
      final List<int>? excelBytes = excel.encode();
      if (excelBytes == null) {
        throw 'Failed to encode Excel file.';
      }

      final String fileName = 'Product_Import_Template_${DateTime.now().millisecondsSinceEpoch}.xlsx';

      // Use file_saver to save the file
      await FileSaver.instance.saveFile(name: fileName, bytes: Uint8List.fromList(excelBytes), ext: 'xlsx', mimeType: MimeType.microsoftExcel);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Template Downloaded', message: 'Excel import template saved as "$fileName".');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Download Failed', message: 'Failed to download Excel template: $e');
    }
  }
}
