import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/features/personilization/controllers/settings_controller.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class SettingsForm extends StatelessWidget {
  const SettingsForm({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(TSettingsController());
    return Column(
      children: [
        // App Settings
        TRoundedContainer(
          padding: const EdgeInsets.symmetric(vertical: TSizes.lg, horizontal: TSizes.md),
          child: Form(
            key: controller.formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('App Settings', style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: TSizes.spaceBtwSections),

                // App Name
                TextFormField(
                  controller: controller.appNameController,
                  decoration: const InputDecoration(hintText: 'App Name', label: Text('App Name'), prefixIcon: Icon(Iconsax.user)), // InputDecoration
                ), // TextFormField
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // Note: The comment below seems like a copy-paste error.
                // Email and Phone
                Row(
                  children: [
                    // Tax Rate
                    Expanded(
                      child: TextFormField(
                        controller: controller.taxController,
                        decoration: const InputDecoration(hintText: 'Tax %', label: Text('Tax Rate (%)'), prefixIcon: Icon(Iconsax.tag)), // InputDecoration
                      ), // TextFormField
                    ), // Expanded
                    const SizedBox(width: TSizes.spaceBtwItems),

                    // Shipping Cost
                    Expanded(
                      child: TextFormField(
                        controller: controller.shippingController,
                        decoration: const InputDecoration(
                          hintText: 'Shipping Cost',
                          label: Text('Shipping Cost (\$)'),
                          prefixIcon: Icon(Iconsax.ship),
                        ), // InputDecoration
                      ), // TextFormField
                    ), // Expanded
                    const SizedBox(width: TSizes.spaceBtwItems),

                    // Free Shipping Threshold
                    Expanded(
                      child: TextFormField(
                        controller: controller.freeShippingThresholdController,
                        decoration: const InputDecoration(
                          hintText: 'Free Shipping After',
                          label: Text('Free Shipping Threshold (\$)'),
                          prefixIcon: Icon(Iconsax.ship),
                        ), // InputDecoration
                      ), // TextFormField
                    ), // Expanded
                  ],
                ), // Row
                const SizedBox(height: TSizes.spaceBtwInputFields * 2),
                Obx(
                  // ADDED Obx to rebuild ElevatedButton when loading changes
                  () => SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: controller.loading.value
                          ? null // Disable button when loading
                          : () => controller.updateSettingInformation(),
                      child: controller.loading.value
                          ? const CircularProgressIndicator(color: Colors.white, strokeWidth: 2)
                          : const Text('Update App Settings'),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
