import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/images/image_uploader.dart';
import 'package:alloy/features/personilization/controllers/settings_controller.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class ImageAndMeta extends StatelessWidget {
  const ImageAndMeta({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(TSettingsController());
    return TRoundedContainer(
      padding: const EdgeInsets.symmetric(vertical: TSizes.lg, horizontal: TSizes.md),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Column(
            children: [
              // User Image
              Obx(
                () => TImageUploader(
                  right: 10,
                  bottom: 20,
                  left: null,
                  width: 200,
                  height: 200,
                  circular: true,
                  icon: Iconsax.camera,
                  imageType: controller.settings.value.appLogo.isEmpty ? ImageType.asset : ImageType.network,
                  image: controller.settings.value.appLogo.isEmpty ? TImages.user : controller.settings.value.appLogo,
                  onIconButtonPressed: () => controller.updateAppLogo(),
                ),
              ),
              const SizedBox(height: TSizes.spaceBtwItems),
              Obx(() => Text(controller.settings.value.appName, style: Theme.of(context).textTheme.headlineLarge)),
              const Text('<EMAIL>'),
              const SizedBox(height: TSizes.spaceBtwSections),
            ],
          ),
        ],
      ),
    );
  }
}
