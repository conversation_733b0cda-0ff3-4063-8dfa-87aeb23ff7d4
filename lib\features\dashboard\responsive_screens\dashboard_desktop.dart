import 'dart:async';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/controllers/dashboard/dashboard_controller.dart';
import 'package:alloy/features/dashboard/table/dashboard_order_table.dart';
import 'package:alloy/features/dashboard/widgets/dashboard_card.dart';
import 'package:alloy/features/dashboard/widgets/order_status_pie_chart.dart';
import 'package:alloy/features/dashboard/widgets/weekly_sales_graph.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';

class DashboardDesktop extends StatelessWidget {
  const DashboardDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Heading
              Text('Dashboard', style: Theme.of(context).textTheme.headlineLarge),

              // Cards
              Row(
                children: [
                  Expanded(
                    child: TDashboardCard(title: 'Sales Total', subtitle: '\$12,000', stats: 25),
                  ),
                  SizedBox(width: TSizes.spaceBtwItems),
                  Expanded(
                    child: TDashboardCard(title: 'Average Order Value', subtitle: '\$120', stats: 15),
                  ),
                  SizedBox(width: TSizes.spaceBtwItems),
                  Expanded(
                    child: TDashboardCard(title: 'Total Orders', subtitle: '36', stats: 44),
                  ),
                  SizedBox(width: TSizes.spaceBtwItems),
                  Expanded(
                    child: TDashboardCard(title: 'Visitors', subtitle: '25,035', stats: 2),
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              /// Graphs
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        /// Bar Graph
                        TWeeklySalesGraph(),
                        const SizedBox(height: TSizes.spaceBtwSections),

                        /// Orders
                        TRoundedContainer(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Recent Orders', style: Theme.of(context).textTheme.headlineSmall),
                              const SizedBox(height: TSizes.spaceBtwSections),
                              const DashboardOrderTable(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: TSizes.spaceBtwSections),

                  /// Pie Chart
                  Expanded(child: TOrderStatusPieChart()),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// *******************************************************************************
// with STREAM SUBSCRIPTION. Suggestion from Gemini
class MyData2 extends DataTableSource {
  // Use Get.find() to get the existing instance of DashboardController
  final DashboardController controller = DashboardController.instance;
  // ignore: unused_field
  StreamSubscription? _dataListSubscription; // To manage the listener

  MyData2() {
    // Listen to changes in the dataList.
    // When dataList changes (e.g., after fetchData), call notifyListeners().
    // RxList's stream emits the new list when it changes.
    _dataListSubscription = controller.dataList.stream.listen((newList) {
      print("MyData: dataList updated with ${newList.length} items. Notifying listeners.");
      notifyListeners();
    });

    // Initial check in case data is already there or loads very fast before listener attaches
    // (though with a 2-sec delay, the listener is likely set up first)
    // If controller.dataList is already populated when MyData is created,
    // and no further changes occur, notifyListeners might need an initial trigger
    // if the table is built before the first data emission.
    // However, PaginatedDataTable2 usually fetches rowCount initially.
    // The listener handles subsequent updates.
  }

  @override
  DataRow getRow(int index) {
    // Add a safety check, especially during fast updates or if rowCount becomes inconsistent.
    if (index >= controller.dataList.length || index < 0) {
      print("MyData: getRow called with invalid index $index for dataList length ${controller.dataList.length}");
      // Return a placeholder or empty row to prevent crashing
      return DataRow(cells: [DataCell(Text('Error: Invalid index')), DataCell(Text('')), DataCell(Text(''))]);
    }

    final data = controller.dataList[index];
    return DataRow(cells: [DataCell(Text(data['id'].toString())), DataCell(Text(data['name'].toString())), DataCell(Text(data['description'].toString()))]);
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount {
    // This will be called by PaginatedDataTable2
    print("MyData: rowCount queried, returning ${controller.dataList.length}");
    return controller.dataList.length;
  }

  @override
  int get selectedRowCount => 0;

  // It's crucial to dispose of the listener when DataTableSource is no longer needed.
  // DataTableSource itself doesn't have a dispose method that PaginatedDataTable calls.
  // This can be tricky if MyData is recreated often.
  // For a DataTableSource that lives as long as the PaginatedDataTable2's state,
  // this manual listener is okay. If MyData() is frequently recreated in a build method,
  // this listener could leak. A more robust solution might involve making MyData itself
  // a GetxController or using a different pattern if MyData instances are ephemeral.
  // However, PaginatedDataTable2 usually keeps its source alive.
  // Let's assume for now the MyData instance is stable enough.
  // If you were using a StatefulWidget for DashboardDesktop and MyData was created in initState,
  // you could dispose the subscription in its dispose method.
  // With GetX, if MyData were a GetxService/Controller, onClose would be the place.
  // Since it's a plain class instantiated in a StatelessWidget, explicit disposal is harder.
  // For this pattern, the listener will be active as long as the MyData instance exists.
  // GetX's RxList stream usually handles closing itself when the RxList is disposed (which happens when DashboardController is closed).
  // So, explicit disposal of _dataListSubscription might not be strictly necessary if DashboardController's lifecycle manages the RxList correctly.
  // However, it's good practice if you have a clear point to do it.
  // Let's remove explicit dispose for now, relying on RxList stream behavior with GetX.
}
