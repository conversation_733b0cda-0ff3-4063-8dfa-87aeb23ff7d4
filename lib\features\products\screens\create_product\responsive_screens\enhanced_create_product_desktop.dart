import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import '../../../../../common/widgets/forms/form_section.dart';
import '../../../../../common/widgets/forms/enhanced_text_field.dart';
import '../../../../../common/widgets/forms/enhanced_dropdown.dart';
import '../../../../../common/widgets/forms/enhanced_image_upload.dart';
import '../../../../../common/widgets/forms/form_feedback.dart';
import '../../../../../common/widgets/containers/rounded_container.dart';

import '../../../../../routes/routes.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/validators/validation.dart';
import '../../../../category/controller/category_controller.dart';
import '../../../controller/product_controller/create_product_controller.dart';

class EnhancedCreateProductDesktop extends StatelessWidget {
  const EnhancedCreateProductDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CreateProductController());
    final categoryController = Get.find<CategoryController>();

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Create New Product',
                breadcrumbItems: [
                  TBreadcrumbItem(text: 'Products', route: TRoutes.products),
                  TBreadcrumbItem(text: 'Create Product'),
                ],
                showBackButton: true,
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Single page form
              _buildSinglePageForm(controller, categoryController),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSinglePageForm(CreateProductController controller, CategoryController categoryController) {
    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Validation errors summary
            Obx(() {
              if (controller.formErrors.isNotEmpty) {
                return Column(
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(TSizes.md),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
                        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Iconsax.warning_2, color: Colors.red, size: 20),
                              const SizedBox(width: TSizes.spaceBtwItems),
                              Text(
                                'Please fix the following errors:',
                                style: TextStyle(color: Colors.red, fontWeight: FontWeight.w600),
                              ),
                            ],
                          ),
                          const SizedBox(height: TSizes.spaceBtwItems),
                          ...controller.formErrors.map(
                            (error) => Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Row(
                                children: [
                                  const SizedBox(width: 24),
                                  Text('• $error', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: TSizes.spaceBtwSections),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),

            // Section 1: Basic Information
            TFormSection(
              title: 'Product Identity',
              icon: Iconsax.box,
              isRequired: true,
              children: [
                TEnhancedTextField(
                  controller: controller.name,
                  labelText: 'Product Name',
                  hintText: 'Enter a descriptive product name',
                  prefixIcon: Iconsax.box,
                  isRequired: true,
                  validator: (value) => TValidator.validateEmptyText('Product Name', value),
                ),
                TEnhancedTextField(
                  controller: controller.description,
                  labelText: 'Description',
                  hintText: 'Describe the product features and benefits',
                  prefixIcon: Iconsax.note,
                  maxLines: 3,
                  helperText: 'Provide a detailed description to help customers understand the product',
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Section 2: Classification
            TFormSection(
              title: 'Classification',
              icon: Iconsax.category,
              isRequired: true,
              children: [
                Obx(
                  () => TEnhancedDropdown<String>(
                    labelText: 'Category',
                    hintText: 'Select product category',
                    prefixIcon: Iconsax.category,
                    isRequired: true,
                    items: categoryController.allItems.map((cat) => cat.id).toList(),
                    value: controller.selectedCategoryId.value.isEmpty ? null : controller.selectedCategoryId.value,
                    onChanged: (value) {
                      if (value != null) controller.selectedCategoryId.value = value;
                    },
                    displayStringForOption: (id) {
                      final category = categoryController.allItems.firstWhereOrNull((cat) => cat.id == id);
                      return category?.name ?? 'Unknown Category';
                    },
                    validator: (value) => TValidator.validateEmptyText('Category', value),
                  ),
                ),
                Obx(
                  () => TEnhancedDropdown<ProductSegment>(
                    labelText: 'Segment',
                    hintText: 'Select product segment',
                    prefixIcon: Iconsax.tag,
                    isRequired: true,
                    items: ProductSegment.values,
                    value: controller.selectedSegment.value,
                    onChanged: (value) {
                      if (value != null) controller.selectedSegment.value = value;
                    },
                    displayStringForOption: (segment) => segment.name.capitalizeFirst!,
                    validator: (value) => value == null ? 'Please select a segment' : null,
                  ),
                ),
              ],
            ),
            // Section 3: Specifications
            TFormSection(
              title: 'Dimensions',
              icon: Iconsax.ruler,
              isRequired: true,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TEnhancedTextField(
                        controller: controller.width,
                        labelText: 'Width (mm)',
                        hintText: 'Enter width',
                        prefixIcon: Iconsax.arrow_left_3,
                        isRequired: true,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) return 'Width is required';
                          final width = double.tryParse(value);
                          if (width == null || width <= 0) return 'Enter a valid width';
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(width: TSizes.spaceBtwInputFields),
                    Expanded(
                      child: TEnhancedTextField(
                        controller: controller.height,
                        labelText: 'Height (mm)',
                        hintText: 'Enter height',
                        prefixIcon: Iconsax.arrow_up_3,
                        isRequired: true,
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) return 'Height is required';
                          final height = double.tryParse(value);
                          if (height == null || height <= 0) return 'Enter a valid height';
                          return null;
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
            // Section 4: Media & Images (Optional)
            TFormSection(
              title: 'Product Images',
              icon: Iconsax.image,
              children: [
                Obx(
                  () => TEnhancedImageUpload(
                    labelText: 'Product Thumbnail',
                    helperText: 'Upload a main product image (JPG, PNG, GIF - Max 5MB)',
                    initialImageUrl: controller.thumbnailPath.value.isNotEmpty ? controller.thumbnailPath.value : null,
                    onImageSelected: (file) => controller.pickAndUploadThumbnail(),
                    onImageRemoved: () => controller.thumbnailPath.value = '',
                    maxWidth: 200,
                    maxHeight: 200,
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwItems),
                TFormFeedback(
                  type: FormFeedbackType.info,
                  message: 'You can add more images and media files after creating the product template.',
                  showIcon: true,
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections * 2),

            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: () {
                  if (controller.formKey.currentState!.validate()) {
                    controller.createProduct();
                  }
                },
                icon: const Icon(Iconsax.add_circle),
                label: const Text('Create Product'),
                style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: TSizes.buttonHeight)),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
