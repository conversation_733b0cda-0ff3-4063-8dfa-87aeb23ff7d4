import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import '../../../../../common/widgets/forms/multi_step_form.dart';
import '../../../../../common/widgets/forms/form_section.dart';
import '../../../../../common/widgets/forms/enhanced_text_field.dart';
import '../../../../../common/widgets/forms/enhanced_dropdown.dart';
import '../../../../../common/widgets/forms/enhanced_image_upload.dart';
import '../../../../../common/widgets/forms/form_feedback.dart';
import '../../../../../routes/routes.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/validators/validation.dart';
import '../../../../category/controller/category_controller.dart';
import '../../../controller/product_controller/create_product_controller.dart';

class EnhancedCreateProductDesktop extends StatelessWidget {
  const EnhancedCreateProductDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CreateProductController());
    final categoryController = Get.find<CategoryController>();

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Create New Product Template',
                breadcrumbItems: [
                  TBreadcrumbItem(text: 'Products', route: TRoutes.products),
                  TBreadcrumbItem(text: 'Create New'),
                ],
                showBackButton: true,
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Multi-step form
              SizedBox(
                height: 600,
                child: TMultiStepForm(
                  progressIndicatorType: ProgressIndicatorType.stepper,
                  steps: [
                    FormStep(
                      title: 'Basic Information',
                      subtitle: 'Enter the basic product details',
                      content: _buildBasicInfoStep(controller, categoryController),
                      validator: () => _validateBasicInfo(controller),
                    ),
                    FormStep(
                      title: 'Specifications',
                      subtitle: 'Define product dimensions and technical details',
                      content: _buildSpecificationsStep(controller),
                      validator: () => _validateSpecifications(controller),
                    ),
                    FormStep(
                      title: 'Media & Images',
                      subtitle: 'Upload product images and media',
                      content: _buildMediaStep(controller),
                      validator: () => _validateMedia(controller),
                      isOptional: true,
                    ),
                    FormStep(
                      title: 'Review & Create',
                      subtitle: 'Review all information before creating the product',
                      content: _buildReviewStep(controller, categoryController),
                      validator: () => _validateFinalStep(controller),
                    ),
                  ],
                  onCompleted: () => controller.createProduct(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoStep(CreateProductController controller, CategoryController categoryController) {
    return SingleChildScrollView(
      child: Column(
        children: [
          TFormSection(
            title: 'Product Identity',
            icon: Iconsax.box,
            isRequired: true,
            children: [
              TEnhancedTextField(
                controller: controller.name,
                labelText: 'Product Name',
                hintText: 'Enter a descriptive product name',
                prefixIcon: Iconsax.box,
                isRequired: true,
                validator: (value) => TValidator.validateEmptyText('Product Name', value),
              ),
              TEnhancedTextField(
                controller: controller.description,
                labelText: 'Description',
                hintText: 'Describe the product features and benefits',
                prefixIcon: Iconsax.note,
                maxLines: 3,
                helperText: 'Provide a detailed description to help customers understand the product',
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwSections),

          TFormSection(
            title: 'Classification',
            icon: Iconsax.category,
            isRequired: true,
            children: [
              Obx(
                () => TEnhancedDropdown<String>(
                  labelText: 'Category',
                  hintText: 'Select product category',
                  prefixIcon: Iconsax.category,
                  isRequired: true,
                  items: categoryController.allItems.map((cat) => cat.id).toList(),
                  value: controller.selectedCategoryId.value.isEmpty ? null : controller.selectedCategoryId.value,
                  onChanged: (value) {
                    if (value != null) controller.selectedCategoryId.value = value;
                  },
                  displayStringForOption: (id) {
                    final category = categoryController.allItems.firstWhereOrNull((cat) => cat.id == id);
                    return category?.name ?? 'Unknown Category';
                  },
                  validator: (value) => TValidator.validateEmptyText('Category', value),
                ),
              ),
              Obx(
                () => TEnhancedDropdown<ProductSegment>(
                  labelText: 'Segment',
                  hintText: 'Select product segment',
                  prefixIcon: Iconsax.tag,
                  isRequired: true,
                  items: ProductSegment.values,
                  value: controller.selectedSegment.value,
                  onChanged: (value) {
                    if (value != null) controller.selectedSegment.value = value;
                  },
                  displayStringForOption: (segment) => segment.name.capitalizeFirst!,
                  validator: (value) => value == null ? 'Please select a segment' : null,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSpecificationsStep(CreateProductController controller) {
    return SingleChildScrollView(
      child: Column(
        children: [
          TFormSection(
            title: 'Dimensions',
            icon: Iconsax.ruler,
            isRequired: true,
            subtitle: 'Specify the product dimensions in millimeters',
            children: [
              TFormRow(
                children: [
                  TEnhancedNumberField(
                    controller: controller.width,
                    labelText: 'Width',
                    hintText: '0.0',
                    prefixIcon: Iconsax.ruler,
                    suffixText: 'mm',
                    isRequired: true,
                    allowDecimals: true,
                    min: 0.1,
                    max: 10000,
                  ),
                  TEnhancedNumberField(
                    controller: controller.height,
                    labelText: 'Height',
                    hintText: '0.0',
                    prefixIcon: Iconsax.ruler,
                    suffixText: 'mm',
                    isRequired: true,
                    allowDecimals: true,
                    min: 0.1,
                    max: 10000,
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwSections),

          TFormSection(
            title: 'Additional Specifications',
            icon: Iconsax.setting_2,
            children: [
              TFormFeedback(
                type: FormFeedbackType.info,
                message:
                    'Additional specifications like thickness, weight, and material properties will be defined when creating product variants.',
                showIcon: true,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMediaStep(CreateProductController controller) {
    return SingleChildScrollView(
      child: Column(
        children: [
          TFormSection(
            title: 'Product Images',
            icon: Iconsax.image,
            subtitle: 'Upload high-quality images to showcase your product',
            children: [
              Obx(
                () => TEnhancedImageUpload(
                  labelText: 'Product Thumbnail',
                  helperText: 'Upload a main product image (JPG, PNG, GIF - Max 5MB)',
                  initialImageUrl: controller.thumbnailPath.value.isNotEmpty ? controller.thumbnailPath.value : null,
                  onImageSelected: (file) => controller.pickAndUploadThumbnail(),
                  onImageRemoved: () => controller.thumbnailPath.value = '',
                  maxWidth: 200,
                  maxHeight: 200,
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwSections),

          TFormFeedback(
            type: FormFeedbackType.info,
            message: 'You can add more images and media files after creating the product template.',
            showIcon: true,
          ),
        ],
      ),
    );
  }

  Widget _buildReviewStep(CreateProductController controller, CategoryController categoryController) {
    return SingleChildScrollView(
      child: Column(
        children: [
          TFormSection(
            title: 'Review Product Information',
            icon: Iconsax.eye,
            children: [
              Obx(() => _buildReviewItem('Product Name', controller.name.text)),
              Obx(
                () => _buildReviewItem(
                  'Description',
                  controller.description.text.isEmpty ? 'No description' : controller.description.text,
                ),
              ),
              Obx(() {
                final category = categoryController.allItems.firstWhereOrNull(
                  (cat) => cat.id == controller.selectedCategoryId.value,
                );
                return _buildReviewItem('Category', category?.name ?? 'Not selected');
              }),
              Obx(() => _buildReviewItem('Segment', controller.selectedSegment.value.name.capitalizeFirst!)),
              Obx(() => _buildReviewItem('Dimensions', '${controller.width.text} × ${controller.height.text} mm')),
              Obx(
                () =>
                    _buildReviewItem('Thumbnail', controller.thumbnailPath.value.isNotEmpty ? 'Uploaded' : 'No image'),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwSections),

          TFormFeedback(
            type: FormFeedbackType.warning,
            title: 'Ready to Create',
            message:
                'Once created, some product information cannot be changed if the product becomes active. Please review carefully.',
            showIcon: true,
          ),
        ],
      ),
    );
  }

  Widget _buildReviewItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: TSizes.spaceBtwItems),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text('$label:', style: const TextStyle(fontWeight: FontWeight.w500)),
          ),
          Expanded(
            child: Text(value, style: TextStyle(color: Colors.grey[600])),
          ),
        ],
      ),
    );
  }

  bool _validateBasicInfo(CreateProductController controller) {
    final nameValid = controller.name.text.isNotEmpty;
    final categoryValid = controller.selectedCategoryId.value.isNotEmpty;
    final segmentValid = controller.selectedSegment.value != ProductSegment.lengths; // Assuming lengths is default

    return nameValid && categoryValid && segmentValid;
  }

  bool _validateSpecifications(CreateProductController controller) {
    final widthValid =
        TValidator.isNumeric(controller.width.text) &&
        double.tryParse(controller.width.text) != null &&
        double.parse(controller.width.text) > 0;
    final heightValid =
        TValidator.isNumeric(controller.height.text) &&
        double.tryParse(controller.height.text) != null &&
        double.parse(controller.height.text) > 0;

    return widthValid && heightValid;
  }

  bool _validateMedia(CreateProductController controller) {
    // Media step is optional, so always return true
    return true;
  }

  bool _validateFinalStep(CreateProductController controller) {
    // Final validation - ensure all required fields are valid
    return _validateBasicInfo(controller) && _validateSpecifications(controller);
  }
}
