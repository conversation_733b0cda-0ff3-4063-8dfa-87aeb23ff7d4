import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/features/products/controller/variant_controller/product_variant_manager_controller.dart';
import 'package:alloy/features/products/models/product_model.dart';
import 'package:alloy/features/products/screens/product_variants/widget/visual_variant_builder.dart';
import 'package:alloy/utils/constants/enums.dart';

/// Demo screen to showcase the Visual Variant Builder
class VisualBuilderDemo extends StatelessWidget {
  const VisualBuilderDemo({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize the controller if not already done
    Get.put(ProductVariantManagerController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('Visual Variant Builder Demo'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Padding(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Demo Header
            _buildDemoHeader(context),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Visual Variant Builder
            Expanded(child: VisualVariantBuilder(product: _createDemoProduct())),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.1),
            Theme.of(context).primaryColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
        border: Border.all(color: Theme.of(context).primaryColor.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.auto_awesome, color: Theme.of(context).primaryColor, size: 28),
              const SizedBox(width: TSizes.spaceBtwItems),
              Text(
                'Visual Variant Builder Demo',
                style: Theme.of(
                  context,
                ).textTheme.headlineSmall?.copyWith(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),
          Text(
            'Experience the streamlined click-to-select variant creation interface',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: Colors.grey[700]),
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Feature highlights
          Wrap(
            spacing: TSizes.spaceBtwItems,
            runSpacing: TSizes.spaceBtwItems / 2,
            children: [
              _buildFeatureChip(context, icon: Icons.drag_indicator, label: 'Drag & Drop', color: Colors.blue),
              _buildFeatureChip(context, icon: Icons.preview, label: 'Live Preview', color: Colors.green),
              _buildFeatureChip(context, icon: Icons.tune, label: 'Selective Generation', color: Colors.orange),
              _buildFeatureChip(context, icon: Icons.speed, label: 'Optimized Performance', color: Colors.purple),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureChip(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(color: color, fontWeight: FontWeight.bold, fontSize: 12),
          ),
        ],
      ),
    );
  }

  /// Create a demo product for testing
  ProductModel _createDemoProduct() {
    return ProductModel(
      id: 'demo-product-001',
      name: 'Premium Steel Sheet',
      description: 'High-quality steel sheet for industrial applications',
      categoryId: 'steel-sheets',
      segment: ProductSegment.lengths,
      status: ProductStatus.active,
      width: 1200.0, // Width in mm
      height: 2400.0, // Height in mm
      thumbnail: null,
      createdAt: DateTime.now(),
    );
  }
}

/// Quick access button to launch the demo
class VisualBuilderDemoButton extends StatelessWidget {
  const VisualBuilderDemoButton({super.key});

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton.extended(
      onPressed: () {
        Get.to(() => const VisualBuilderDemo());
      },
      icon: const Icon(Icons.auto_awesome),
      label: const Text('Demo Visual Builder'),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
    );
  }
}

/// Demo instructions widget
class DemoInstructions extends StatelessWidget {
  const DemoInstructions({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(TSizes.md),
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info, color: Colors.blue[600]),
              const SizedBox(width: TSizes.spaceBtwItems / 2),
              Text(
                'How to Use',
                style: TextStyle(color: Colors.blue[600], fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          ..._instructions.asMap().entries.map((entry) {
            final index = entry.key + 1;
            final instruction = entry.value;

            return Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 20,
                    height: 20,
                    decoration: BoxDecoration(color: Colors.blue[600], shape: BoxShape.circle),
                    child: Center(
                      child: Text(
                        '$index',
                        style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(instruction, style: TextStyle(color: Colors.blue[700], fontSize: 14)),
                  ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }

  static const List<String> _instructions = [
    'Click on attribute chips in the left panel to select them',
    'Drag attributes to the drop zone for quick selection',
    'Watch the live preview update as you make selections',
    'Use the combination matrix to see all possible variants',
    'Select specific combinations or generate all variants',
    'Switch between Visual Builder and Traditional Form tabs',
  ];
}
