import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../utils/helpers/network_manager.dart';
import '../../../../utils/helpers/product_variant_calculator.dart';
import '../../../../utils/popups/full_screen_loader.dart';
import '../../../../utils/popups/loaders.dart';
import '../../../../utils/validators/validation.dart';
import '../../models/product_model.dart';
import '../../models/product_variant_model.dart';
import '../../repository/product_repository.dart';
import '../../repository/product_variant_repository.dart';
import '../product_controller/product_controller.dart';
import 'product_variant_controller.dart';

/// A consolidated controller for managing both single-product and bulk product variant creation.
/// It handles attribute selections, custom thickness input, and the generation/saving of variants.
class ProductVariantManagerController extends GetxController {
  static ProductVariantManagerController get instance => Get.find();

  // --- Repositories & Other Controllers
  final _productRepository = Get.put(ProductRepository()); // To fetch all products for bulk creation
  final _variantRepository = ProductVariantRepository.instance;
  // This controller will need to refresh the ProductController's list if it's visible.
  // We'll use Get.find() here, assuming ProductController is already initialized elsewhere (e.g., in bindings).
  // If not, it should be Get.put(ProductController()) in your main bindings.
  final ProductController _productController = Get.find<ProductController>();

  // --- Form State
  final formKey = GlobalKey<FormState>(); // For variant creation form validation
  final isLoading = false.obs; // General loading indicator for operations

  // --- Product Selection for Bulk Creation ---
  // This list holds ALL available base products (for the bulk selection UI)
  final RxList<ProductModel> allProductsForSelection = <ProductModel>[].obs;
  // This list holds the products CURRENTLY SELECTED for bulk variant creation
  final RxList<ProductModel> selectedProductsForBulkCreation = <ProductModel>[].obs;

  // --- Variant Generation Attributes (Common to both single and bulk creation) ---
  final RxList<String> selectedMaterials = <String>[].obs;
  final RxList<String> selectedThicknesses = <String>[].obs; // For chip selections
  final RxList<String> selectedFinishes = <String>[].obs;
  final RxList<String> selectedLengths = <String>[].obs;

  // --- Custom Thickness Input
  final TextEditingController customThicknessController = TextEditingController();

  // --- Pre-defined options for the UI (can be fetched from Firestore if dynamic)
  final List<String> materialOptions = ['PG', 'GI', 'HR', 'HDG', 'SS304', 'SS316'];
  final List<String> thicknessOptions = [
    '0.45mm',
    '0.50mm',
    '0.55mm',
    '0.65mm',
    '0.70mm',
    '0.80mm',
    '0.90mm',
    '1.0mm',
    '1.2mm',
    '1.5mm',
    '1.6mm',
    '2.0mm',
    '2.5mm',
    '3.0mm',
    '4.0mm',
    '5.0mm',
    '6.0mm',
  ];
  final List<String> finishOptions = ['Standard', 'Epoxy', 'Powder Coated', 'EG'];
  final List<String> lengthOptions = ['3.0m', '2.9m', '2.44m'];

  // --- Nullable quantities (if user wants to pre-fill initial stock)
  final TextEditingController quantityOnHandController = TextEditingController(text: '0');
  final TextEditingController quantityOnOrderController = TextEditingController(text: '0');
  final TextEditingController quantityInProductionController = TextEditingController(text: '0');

  @override
  void onInit() {
    super.onInit();
    // Fetch all products when the controller initializes for potential bulk selection
    _fetchAllProductsForSelection();
  }

  @override
  void onClose() {
    customThicknessController.dispose();
    quantityOnHandController.dispose();
    quantityOnOrderController.dispose();
    quantityInProductionController.dispose();
    super.onClose();
  }

  ///################# Fetches all base products to populate the selection list for bulk creation.
  Future<void> _fetchAllProductsForSelection() async {
    try {
      isLoading.value = true;
      // Using stream.first to get current list once. If real-time updates are needed
      // for the selection list itself, you'd use a listener here.
      final products = await _productRepository.streamAllProducts().first;
      allProductsForSelection.assignAll(products);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Failed to load products for selection: $e');
    } finally {
      isLoading.value = false;
    }
  }

  ///################# Toggles the selection of a product for bulk variant creation.
  void toggleProductSelectionForBulk(ProductModel product, bool isSelected) {
    if (isSelected) {
      selectedProductsForBulkCreation.add(product);
    } else {
      selectedProductsForBulkCreation.removeWhere((p) => p.id == product.id);
    }
  }

  /// #################### Unified Method to Generate and Save Variants ---
  /// This method can be called for a single product or for multiple products.
  Future<void> generateAndSaveVariants({
    ProductModel? singleProduct, // For single product variant creation
    List<ProductModel>? productsForBulkCreation, // For bulk variant creation
  }) async {
    try {
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Determine the list of products to process
      List<ProductModel> productsToProcess = [];
      if (singleProduct != null) {
        productsToProcess.add(singleProduct);
      } else if (productsForBulkCreation != null && productsForBulkCreation.isNotEmpty) {
        productsToProcess.assignAll(productsForBulkCreation);
      } else {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'No Products Selected', message: 'Please select at least one product to create variants for.');
        return;
      }

      // Collect all thicknesses: from chips and custom input
      final List<String> allThicknesses = List.from(selectedThicknesses);
      final String customThickness = customThicknessController.text.trim();
      if (customThickness.isNotEmpty) {
        if (!TValidator.isNumeric(customThickness.replaceAll('mm', ''))) {
          TFullScreenLoader.stopLoading();
          TLoaders.warningSnackBar(title: 'Invalid Input', message: 'Custom thickness must be a valid number (e.g., 1.2mm, 0.56).');
          return;
        }
        allThicknesses.add(customThickness.endsWith('mm') ? customThickness : '${customThickness}mm');
      }

      // Basic Validation for attribute selections
      if (selectedMaterials.isEmpty || allThicknesses.isEmpty || selectedFinishes.isEmpty || selectedLengths.isEmpty) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(
          title: 'Attribute Selection Required',
          message: 'Please select at least one Material, Thickness (chip or custom), Finish, and Length.',
        );
        return;
      }

      int totalVariantsCreated = 0;
      for (final product in productsToProcess) {
        if (product.id.isEmpty) {
          TLoaders.warningSnackBar(title: 'Skipped Product', message: 'Product "${product.name}" has no ID. Skipping variant creation for this product.');
          continue;
        }

        final List<ProductVariantModel> variantsToCreate = ProductVariantCalculator.generateVariants(
          product: product,
          selectedMaterials: selectedMaterials.toList(),
          selectedThicknesses: allThicknesses,
          selectedFinishes: selectedFinishes.toList(),
          selectedLengths: selectedLengths.toList(),
          quantityOnHand: int.tryParse(quantityOnHandController.text) ?? 0,
          quantityOnOrder: int.tryParse(quantityOnOrderController.text) ?? 0,
          quantityInProduction: int.tryParse(quantityInProductionController.text) ?? 0,
        );

        if (variantsToCreate.isNotEmpty) {
          await _variantRepository.createVariantsInBatch(variantsToCreate);
          totalVariantsCreated += variantsToCreate.length;
        } else {
          TLoaders.warningSnackBar(
            title: 'No Variants Generated',
            message: 'No valid variant combinations could be generated for "${product.name}" with selected attributes.',
          );
        }
      }

      TFullScreenLoader.stopLoading();

      if (totalVariantsCreated > 0) {
        TLoaders.successSnackBar(title: 'Success', message: '$totalVariantsCreated variants created across ${productsToProcess.length} products!');

        // Clear selections after successful creation
        _clearSelections();

        // Refresh the main product list (if on that screen) or the current product's variants
        if (singleProduct != null) {
          Get.find<ProductVariantController>().loadVariantsForProduct(singleProduct.id);
        } else {
          _productController.listenToStream(); // Refresh main product list
        }
        Get.back(); // Go back to the previous screen (either product variants or products list)
      } else {
        TLoaders.warningSnackBar(title: 'No Variants Created', message: 'No variants were created. Please check your selections.');
      }
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  /// Clears all attribute selections and quantity inputs.
  void _clearSelections() {
    selectedProductsForBulkCreation.clear(); // Clear selected products for bulk
    selectedMaterials.clear();
    selectedThicknesses.clear();
    customThicknessController.clear();
    selectedFinishes.clear();
    selectedLengths.clear();
    quantityOnHandController.text = '0';
    quantityOnOrderController.text = '0';
    quantityInProductionController.text = '0';
  }
}
