import 'package:alloy/features/authentication/screens/login/widgets/header_widget.dart';
import 'package:alloy/features/authentication/screens/login/widgets/login_form_widget.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';

class LoginScreenMobile extends StatelessWidget {
  const LoginScreenMobile({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsetsGeometry.all(TSizes.defaultSpace),
          child: Column(
            children: [
              /// -- Header
              THeaderWidget(),

              /// -- FORM
              TLoginFormWidget(),
            ],
          ),
        ),
      ),
    );
  }
}
