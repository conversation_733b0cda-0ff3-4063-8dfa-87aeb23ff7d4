import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/sizes.dart';
import 'package:alloy/data/abstract/base_data_table_controller.dart'; // Assuming this path

class TDataTable<T> extends StatelessWidget {
  const TDataTable({
    // Made generic
    super.key,
    required this.columns,
    required this.source, // Changed from rows to source
    required this.controller, // Added controller
    this.minWidth = 600, // Added minWidth
    this.sortAscending = true, // Added sortAscending
  });

  /// List of columns for the data table
  final List<DataColumn> columns;
  final DataTableSource source; // Data source for the table
  final TBaseController<T> controller; // Controller for managing data
  final double minWidth;
  final bool sortAscending;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      // Set the height of the data table to 80% of the screen height
      height: MediaQuery.of(context).size.height * 0.8,
      child: DataTable2(
        columns: columns,
        rows: [],

        // Set the minimum width of the data table
        minWidth: minWidth,
        sortAscending: sortAscending,

        // Set the spacing between columns
        columnSpacing: 12,
        // Set the horizontal margin of the data table
        horizontalMargin: 12,
        // Set the color of the heading row
        headingRowColor: WidgetStateProperty.resolveWith((states) => TColors.primary),
        // Set the decoration for the data table
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(TSizes.borderRadiusMd)),
        ),
        // Set the decoration for the heading row of the data table
        headingRowDecoration: const BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(TSizes.borderRadiusMd),
            topRight: Radius.circular(TSizes.borderRadiusMd),
          ),
        ),
      ),
    );
  }
}
