import 'package:alloy/common/widgets/appbar/logout_button.dart';
import 'package:alloy/common/widgets/images/t_rounded_image.dart';
import 'package:alloy/common/widgets/shimmers/shimmer.dart';
import 'package:alloy/features/authentication/repository/authentication_repository.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:iconsax/iconsax.dart';

// PreferredSizeWidget is used to specify the height of the header as this header is used as the appbar
class THeader extends StatelessWidget implements PreferredSizeWidget {
  const THeader({super.key, this.scaffoldKey});

  /// Global key to access the scaffold state
  final GlobalKey<ScaffoldState>? scaffoldKey;

  @override
  Widget build(BuildContext context) {
    final userController = UserController.instance;

    final isDesktop = TDeviceUtils.isDesktopScreen(context);
    final isMobile = TDeviceUtils.isMobileScreen(context);
    return Container(
      decoration: BoxDecoration(
        color: TColors.white,
        border: Border(bottom: BorderSide(color: TColors.grey, width: 1)),
      ),
      padding: EdgeInsets.symmetric(horizontal: TSizes.md, vertical: TSizes.sm),
      child: AppBar(
        automaticallyImplyLeading: false,

        /// Mobile Menu
        leading: isDesktop
            ? null
            : Tooltip(
                message: 'Menu',
                child: IconButton(onPressed: () => scaffoldKey?.currentState?.openDrawer(), icon: const Icon(Iconsax.menu)),
              ),

        /// Search Field
        title: !isDesktop
            ? null
            : SizedBox(
                width: 400,
                child: TextFormField(
                  decoration: InputDecoration(prefixIcon: Icon(Iconsax.search_normal), hintText: 'Search Anything...'),
                ),
              ),

        /// Actions
        actions: [
          if (!isDesktop) ...[
            Tooltip(
              message: 'Search',
              child: IconButton(onPressed: () {}, icon: const Icon(Iconsax.search_normal)),
            ),
          ],

          // Notification
          Tooltip(
            message: 'Notifications',
            child: IconButton(onPressed: () {}, icon: const Icon(Iconsax.notification)),
          ),
          SizedBox(width: TSizes.spaceBtwItems / 2),

          // User Data
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Obx(() {
                final user = userController.user.value;
                final isProfilePicAvailable = user.profilePicture.isNotEmpty;
                // Show shimmer if user is authenticated but data is not yet loaded.
                final isLoading = (user.id?.isEmpty ?? true) && AuthenticationRepository.instance.authUser != null;
                if (isLoading) {
                  return TShimmerEffect(width: 40, height: 40);
                }
                return Tooltip(
                  message: '${user.fullName} \n ${user.roles.map((e) => e.name).join(', ') ?? ''},)}',
                  child: TRoundedImage(
                    width: 40,
                    height: 40,
                    padding: 2,
                    imageType: isProfilePicAvailable ? ImageType.network : ImageType.asset,
                    image: isProfilePicAvailable ? user.profilePicture : TImages.user,
                    // You might want to add an onTap to navigate to the profile screen
                    // onTap: () => Get.toNamed(TRoutes.profile),
                  ),
                );
              }),

              SizedBox(width: TSizes.sm),

              // Name and Email
              if (!isMobile)
                Obx(() {
                  final user = userController.user.value;
                  // Show shimmer if user is authenticated but data is not yet loaded.
                  final isLoading = (user.id?.isEmpty ?? true) && AuthenticationRepository.instance.authUser != null;

                  return Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      isLoading ? TShimmerEffect(width: 50, height: 13) : Text(user.fullName, style: Theme.of(context).textTheme.titleLarge),
                      isLoading ? TShimmerEffect(width: 50, height: 13) : Text(user.email, style: Theme.of(context).textTheme.labelMedium),
                    ],
                  );
                }),
              LogoutButton(),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(TDeviceUtils.getAppBarHeight() + 15);
}
