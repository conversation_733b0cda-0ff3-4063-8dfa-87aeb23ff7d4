import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../routes/routes.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/validators/validation.dart';
import '../../../controller/variant_controller/product_variant_manager_controller.dart';
import 'product_variant_creation_form.dart';

class BulkVariantCreationWidget extends StatelessWidget {
  const BulkVariantCreationWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProductVariantManagerController()); // Initialize the manager controller

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Form(
            key: controller.formKey, // Associate form key
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Breadcrumbs
                const TBreadcrumbsWithHeading(
                  heading: 'Bulk Create Product Variants',
                  breadcrumbItems: [
                    TBreadcrumbItem(text: 'Products', route: TRoutes.products),
                    TBreadcrumbItem(text: 'Bulk Create Variants'),
                  ],
                  showBackButton: true,
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Section 1: Select Products
                TRoundedContainer(
                  padding: const EdgeInsets.all(TSizes.defaultSpace),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Select Products', style: Theme.of(context).textTheme.headlineSmall),
                      const SizedBox(height: TSizes.spaceBtwSections),
                      Obx(() {
                        if (controller.isLoading.value && controller.allProductsForSelection.isEmpty) {
                          return const Center(child: CircularProgressIndicator());
                        }
                        if (controller.allProductsForSelection.isEmpty) {
                          return const Center(child: Text('No products available for selection.'));
                        }
                        return Wrap(
                          spacing: 8.0,
                          runSpacing: 8.0,
                          children: controller.allProductsForSelection.map((product) {
                            final isSelected = controller.selectedProductsForBulkCreation.contains(product);
                            return ChoiceChip(
                              label: Text(product.name),
                              selected: isSelected,
                              onSelected: (selected) {
                                controller.toggleProductSelectionForBulk(product, selected);
                              },
                              selectedColor: Theme.of(context).primaryColor,
                              backgroundColor: Theme.of(context).chipTheme.backgroundColor,
                              labelStyle: TextStyle(
                                color: isSelected ? Colors.white : Theme.of(context).textTheme.bodyMedium!.color,
                              ),
                            );
                          }).toList(),
                        );
                      }),
                    ],
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Section 2: Define Variant Attributes (Reusing ProductVariantCreationForm's content)
                TRoundedContainer(
                  padding: const EdgeInsets.all(TSizes.defaultSpace),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Define Variant Attributes', style: Theme.of(context).textTheme.headlineSmall),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Materials Selection (Chips)
                      AttributeSelectionWidget(
                        title: 'Materials',
                        options: controller.materialOptions,
                        selectedOptions: controller.selectedMaterials,
                        isMultiSelect: true,
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Thicknesses Selection (Chips + Custom Input)
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AttributeSelectionWidget(
                            title: 'Thicknesses',
                            options: controller.thicknessOptions,
                            selectedOptions: controller.selectedThicknesses,
                            isMultiSelect: true,
                          ),
                          const SizedBox(height: TSizes.spaceBtwInputFields),
                          TextFormField(
                            controller: controller.customThicknessController,
                            decoration: const InputDecoration(
                              labelText: 'Custom Thickness (e.g., 0.56mm)',
                              hintText: 'Enter custom thickness',
                              suffixIcon: Icon(Icons.straighten),
                            ),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            validator: (value) {
                              if (value != null && value.isNotEmpty) {
                                final cleanedValue = value.replaceAll('mm', '').trim();
                                if (!TValidator.isNumeric(cleanedValue)) {
                                  return 'Please enter a valid number for thickness.';
                                }
                              }
                              return null;
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Finishes Selection (Chips)
                      AttributeSelectionWidget(
                        title: 'Finishes',
                        options: controller.finishOptions,
                        selectedOptions: controller.selectedFinishes,
                        isMultiSelect: true,
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Lengths Selection (Chips)
                      AttributeSelectionWidget(
                        title: 'Lengths',
                        options: controller.lengthOptions,
                        selectedOptions: controller.selectedLengths,
                        isMultiSelect: true,
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Quantities (Nullable, if user wants to pre-fill initial stock)
                      Text('Initial Quantities (Optional)', style: Theme.of(context).textTheme.titleMedium),
                      const SizedBox(height: TSizes.spaceBtwInputFields),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: controller.quantityOnHandController,
                              decoration: const InputDecoration(labelText: 'On Hand'),
                              keyboardType: TextInputType.number,
                              validator: TValidator.validateNumber,
                            ),
                          ),
                          const SizedBox(width: TSizes.spaceBtwInputFields),
                          Expanded(
                            child: TextFormField(
                              controller: controller.quantityOnOrderController,
                              decoration: const InputDecoration(labelText: 'On Order'),
                              keyboardType: TextInputType.number,
                              validator: TValidator.validateNumber,
                            ),
                          ),
                          const SizedBox(width: TSizes.spaceBtwInputFields),
                          Expanded(
                            child: TextFormField(
                              controller: controller.quantityInProductionController,
                              decoration: const InputDecoration(labelText: 'In Production'),
                              keyboardType: TextInputType.number,
                              validator: TValidator.validateNumber,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Action Button
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      width: 300, // Wider button for clarity
                      child: ElevatedButton(
                        onPressed: () => controller.generateAndSaveVariants(
                          productsForBulkCreation: controller.selectedProductsForBulkCreation.toList(),
                        ),
                        child: const Text('Generate & Add Variants for Selected Products'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
