// Test Routes
class TRoutes {
  // Mainly used to observe the sidebar state and highlight the correct menu item when
  // Navigating to the previous screen
  static final List<String> sideMenuItems = [
    dashboard,
    media,
    brands,
    accounts,
    createAccount,
    importAccounts,
    contacts,
    createContact,
    importContacts,
    productsMenu, // Include parent route if it leads to a page or is just a collapsible header
    humanResources, // Include parent route if it leads to a page or is just a collapsible header
    users,
    workers,
    products,
    createProduct,
    categories,
    createCategory,
    orders,
    profile,
    settings,
    logout,
  ];
  // -- Auth Routes
  static const String login = '/login';
  static const String logout = '/logout';
  static const String signup = '/signup';
  static const String forgotPassword = '/forgotPassword/';
  static const String resetPassword = '/resetPassword';
  static const String dashboard = '/dashboard';
  static const String media = '/media';

  // -- Account Routes
  static const String accounts = '/accounts';
  static const String createAccount = '/createAccount';
  static const String editAccount = '/editAccount';
  static const String importAccounts = '/importAccounts';

  // -- Contact Routes
  static const String contacts = '/contacts';
  static const String createContact = '/createContact';
  static const String editContact = '/editContact';
  static const String importContacts = '/importContacs';

  // -- Category Routes
  static const String categories = '/categories';
  static const String createCategory = '/createCategory';
  static const String editCategory = '/editCategory';

  // -- Brand Routes
  static const String brands = '/brands';
  static const String createBrand = '/createBrand';
  static const String editBrand = '/editBrand';

  // -- Banner Routes
  static const String banners = '/banners';
  static const String createBanner = '/createBanner';
  static const String editBanner = '/editBanner';

  // -- Product Routes
  static const String productsMenu = '/productsMenu';
  static const String products = '/products';
  static const String createProduct = '/createProduct';
  static const String editProduct = '/editProduct';
  static const String importProducts = '/importProducts';
  static const String productVariants = '/productVariants';
  static const String editProductVariant = '/editProductVariant';
  static const String bulkCreateVariants = '/bulkCreateVariants';

  // -- Customer Routes
  static const String customers = '/customers';
  static const String createCustomer = '/createCustomer';
  static const String customerDetails = '/customerDetails';

  // -- profile
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String coupons = '/coupons';

  // -- Human Resources Routes
  static const String humanResources = '/humanResources';
  static const String users = '/users';
  static const String workers = '/workers';
  static const String employees = '/employees';

  // -- Order Routes
  static const String orders = '/orders';
  static const String orderDetails = '/orderDetails';
  static const String createOrder = '/createOrder';
  static const String editOrder = '/editOrder';
  static const String orderTracking = '/orderTracking';
  static const String orderInvoice = '/orderInvoice';
  static const String orderReturn = '/orderReturn';
  static const String orderCancellation = '/orderCancellation';
  static const String orderRefund = '/orderRefund';
  static const String orderReview = '/orderReview';
  static const String orderChat = '/orderChat';
  static const String orderPayment = '/orderPayment';
  static const String orderDelivery = '/orderDelivery';
  static const String orderPickup = '/orderPickup';
  static const String orderShipment = '/orderShipment';
  static const String orderStatus = '/orderStatus';
  static const String orderHistory = '/orderHistory';
  static const String orderArchive = '/orderArchive';
  static const String orderTrash = '/orderTrash';
  static const String orderSearch = '/orderSearch';
  static const String orderFilter = '/orderFilter';
  static const String orderSort = '/orderSort';
  static const String orderExport = '/orderExport';
  static const String orderImport = '/orderImport';
  static const String orderPrint = '/orderPrint';
  static const String orderShare = '/orderShare';
  static const String orderNotification = '/orderNotification';
  static const String orderReminder = '/orderReminder';
  static const String orderReport = '/orderReport';
  static const String orderSetting = '/orderSetting';
  static const String orderHelp = '/orderHelp';
  static const String orderSupport = '/orderSupport';
  static const String orderAbout = '/orderAbout';
  static const String orderLogout = '/orderLogout';
  static const String orderProfile = '/orderProfile';
  static const String orderAddress = '/orderAddress';
  static const String orderPaymentMethod = '/orderPaymentMethod';
  static const String orderShippingMethod = '/orderShippingMethod';
  static const String orderNotificationSetting = '/orderNotificationSetting';
  static const String orderPrivacyPolicy = '/orderPrivacyPolicy';
  static const String orderTermsCondition = '/orderTermsCondition';
  static const String orderFAQ = '/orderFAQ';
  static const String orderContactUs = '/orderContactUs';
}
