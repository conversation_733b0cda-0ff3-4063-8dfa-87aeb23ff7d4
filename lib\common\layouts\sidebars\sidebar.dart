import 'package:alloy/common/layouts/sidebars/menu/menu_data.dart';
import 'package:alloy/common/layouts/sidebars/menu/menu_item.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../utils/constants/image_strings.dart';

class TSideBar extends StatelessWidget {
  const TSideBar({super.key});

  @override
  Widget build(BuildContext context) {
    final userController = UserController.instance;

    return Drawer(
      shape: BeveledRectangleBorder(),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border(right: BorderSide(color: TColors.grey, width: 1)),
        ),
        child: SingleChildScrollView(
          child: Column(
            children: [
              SizedBox(height: TSizes.spaceBtwItems),
              // Image
              Image.asset(
                TImages.bonnAppLogo, // This should be the string path to your asset
                width: 100,
                height: 100,
                fit: BoxFit.scaleDown,
              ),
              SizedBox(height: TSizes.spaceBtwItems),
              Padding(
                padding: const EdgeInsets.all(TSizes.md),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text('MENU', style: Theme.of(context).textTheme.bodySmall!.apply(letterSpacingDelta: 1.2)),
                    const SizedBox(height: TSizes.sm), // Space between heading and items
                    // Main Menu Items
                    Obx(() {
                      // <--- This Obx block is where the logic goes
                      final menuItems = MenuData.allMenuItems;
                      return Column(
                        children: menuItems.map((item) {
                          // Conditionally display certain items based on user role if needed
                          if ((item.route == TRoutes.dashboard || item.route == TRoutes.media || item.route == TRoutes.banners) &&
                              !userController.user.value.roles.contains(UserRole.Admin)) {
                            // Check if user is NOT Admin
                            return const SizedBox.shrink(); // Hide if not admin
                          }
                          // For a parent (collapsible) menu, you might also need to check its children's roles
                          // or just hide the entire parent if the user doesn't have access to any child.
                          // This check below assumes the entire parent menu (e.g., Human Resources) is hidden
                          // if the user is not an admin, but you can make it more granular.
                          if (item.type == MenuType.collapsible &&
                              item.route == TRoutes.humanResources &&
                              !userController.user.value.roles.contains(UserRole.Admin)) {
                            // Check if user is NOT Admin
                            return const SizedBox.shrink();
                          }

                          // If the item is a regular item or an admin-specific item for an admin, display it.
                          // For Human Resources, if it contains sub-menus that also have role-based visibility,
                          // you might need more complex filtering here or within the TMenuItem itself.
                          return TMenuItem(menuItem: item, isSubItem: false); // TODO submenu indentation
                        }).toList(),
                      );
                    }),

                    const SizedBox(height: TSizes.md),
                    Text('OTHER', style: Theme.of(context).textTheme.bodySmall!.apply(letterSpacingDelta: 1.2)),
                    const SizedBox(height: TSizes.sm),

                    // Other Menu Items (usually common for all roles)
                    Column(
                      children: MenuData.otherMenuItems.map((item) {
                        // You might add role-based visibility here too if 'Profile' or 'Settings'
                        // should be hidden for certain roles.
                        return TMenuItem(menuItem: item, isSubItem: false); // TODO submenu indentation
                      }).toList(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
