import 'package:alloy/common/layouts/sidebars/menu/menu_data.dart';
import 'package:alloy/common/layouts/sidebars/menu/menu_item.dart';
import 'package:alloy/common/layouts/sidebars/sidebar_controller.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../../../utils/constants/image_strings.dart';

class TSideBar extends StatelessWidget {
  const TSideBar({super.key});

  @override
  Widget build(BuildContext context) {
    final userController = UserController.instance;
    final sidebarController = Get.put(SidebarController());

    return Obx(() {
      final isCollapsed = sidebarController.isCollapsed.value;
      final isDesktop = TDeviceUtils.isDesktopScreen(context);

      return AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        width: isCollapsed && isDesktop ? 80 : 280,
        child: Drawer(
          shape: const RoundedRectangleBorder(),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(right: BorderSide(color: TColors.grey.withValues(alpha: 0.3), width: 1)),
              boxShadow: [
                BoxShadow(color: Colors.black.withValues(alpha: 0.05), blurRadius: 10, offset: const Offset(2, 0)),
              ],
            ),
            child: Column(
              children: [
                // Header with logo and collapse button
                _buildHeader(context, sidebarController, isCollapsed, isDesktop),

                // Menu content
                Expanded(
                  child: SingleChildScrollView(
                    child: _buildMenuContent(context, userController, sidebarController, isCollapsed),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Widget _buildHeader(BuildContext context, SidebarController sidebarController, bool isCollapsed, bool isDesktop) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        border: Border(bottom: BorderSide(color: TColors.grey.withValues(alpha: 0.2), width: 1)),
      ),
      child: Row(
        children: [
          // Logo
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            width: isCollapsed ? 40 : 60,
            height: isCollapsed ? 40 : 60,
            child: Image.asset(TImages.bonnAppLogo, fit: BoxFit.contain),
          ),

          if (!isCollapsed) ...[
            const SizedBox(width: TSizes.sm),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Alloy ERP',
                    style: Theme.of(
                      context,
                    ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: TColors.primary),
                  ),
                  Text(
                    'Management System',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(color: TColors.darkGrey),
                  ),
                ],
              ),
            ),
          ],

          // Collapse button (only show on desktop)
          if (isDesktop)
            IconButton(
              onPressed: sidebarController.toggleSidebar,
              icon: Icon(isCollapsed ? Iconsax.arrow_right_3 : Iconsax.arrow_left_2, color: TColors.darkGrey, size: 20),
              tooltip: isCollapsed ? 'Expand Sidebar' : 'Collapse Sidebar',
            ),
        ],
      ),
    );
  }

  Widget _buildMenuContent(
    BuildContext context,
    UserController userController,
    SidebarController sidebarController,
    bool isCollapsed,
  ) {
    return Padding(
      padding: const EdgeInsets.all(TSizes.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Main Menu Section
          if (!isCollapsed) ...[
            Text(
              'MAIN MENU',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(letterSpacing: 1.2, fontWeight: FontWeight.w600, color: TColors.darkGrey),
            ),
            const SizedBox(height: TSizes.sm),
          ],

          // Main Menu Items
          Column(
            children: MenuData.allMenuItems.map((item) {
              return TMenuItem(menuItem: item, isSubItem: false, isCollapsed: isCollapsed);
            }).toList(),
          ),

          const SizedBox(height: TSizes.lg),

          // Other Menu Section
          if (!isCollapsed) ...[
            Text(
              'OTHER',
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(letterSpacing: 1.2, fontWeight: FontWeight.w600, color: TColors.darkGrey),
            ),
            const SizedBox(height: TSizes.sm),
          ],

          // Other Menu Items
          Column(
            children: MenuData.otherMenuItems.map((item) {
              return TMenuItem(menuItem: item, isSubItem: false, isCollapsed: isCollapsed);
            }).toList(),
          ),
        ],
      ),
    );
  }
}
