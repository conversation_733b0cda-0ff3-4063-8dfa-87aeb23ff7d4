import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../features/authentication/controllers/user_controller.dart';
import '../../../features/products/models/product_model.dart';
import '../../../features/products/models/product_variant_model.dart';
import '../../../features/settings/controllers/settings_controller.dart';
import '../../../utils/constants/enums.dart';
import '../../../utils/helpers/network_manager.dart';
import '../../../utils/helpers/product_variant_calculator.dart';
import '../../../utils/popups/full_screen_loader.dart';
import '../../../utils/popups/loaders.dart';
import '../models/deal_item_model.dart';
import '../repositroy/deal_repository.dart';

/// Controller for managing Deal Items with weight calculations and pricing logic
class DealItemController extends GetxController {
  static DealItemController get instance => Get.find();

  // Dependencies
  final DealRepository _dealRepository = DealRepository.instance;
  final UserController _userController = UserController.instance;
  final SettingsController _settingsController = SettingsController.instance;

  // Form key for deal item forms
  final formKey = GlobalKey<FormState>();

  // Current deal ID being worked on
  final RxString currentDealId = ''.obs;

  // Deal items for current deal
  final RxList<DealItemModel> dealItems = <DealItemModel>[].obs;
  final RxBool isLoading = false.obs;

  // Form controllers for deal item creation/editing
  final quotedQuantity = TextEditingController();
  final unitLengthMeters = TextEditingController();
  final userProvidedUnitPrice = TextEditingController();
  final markupPercentage = TextEditingController(text: '15.0');

  // Selected product and variant
  final Rxn<ProductModel> selectedProduct = Rxn<ProductModel>();
  final Rxn<ProductVariantModel> selectedVariant = Rxn<ProductVariantModel>();
  final RxString selectedQuotedUnit = 'NOS'.obs; // Default to NOS

  // Calculated values (reactive)
  final RxDouble calculatedTotalLength = 0.0.obs;
  final RxDouble calculatedTotalWeight = 0.0.obs;
  final RxDouble calculatedAutoPrice = 0.0.obs;
  final RxDouble calculatedTotalPrice = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    
    // Listen to form changes for real-time calculations
    quotedQuantity.addListener(_recalculateValues);
    unitLengthMeters.addListener(_recalculateValues);
    userProvidedUnitPrice.addListener(_recalculateValues);
    markupPercentage.addListener(_recalculateValues);
    
    // Listen to selection changes
    selectedVariant.listen((_) => _recalculateValues());
    selectedQuotedUnit.listen((_) => _recalculateValues());
  }

  @override
  void onClose() {
    quotedQuantity.dispose();
    unitLengthMeters.dispose();
    userProvidedUnitPrice.dispose();
    markupPercentage.dispose();
    super.onClose();
  }

  /// Load deal items for a specific deal
  void loadDealItems(String dealId) {
    if (currentDealId.value != dealId) {
      currentDealId.value = dealId;
      _bindDealItemsStream();
    }
  }

  /// Bind to deal items stream
  void _bindDealItemsStream() {
    if (currentDealId.value.isNotEmpty) {
      isLoading.value = true;
      dealItems.bindStream(_dealRepository.streamDealItems(currentDealId.value));
      isLoading.value = false;
    }
  }

  /// Reset form fields
  void resetForm() {
    quotedQuantity.clear();
    unitLengthMeters.clear();
    userProvidedUnitPrice.clear();
    markupPercentage.text = '15.0';
    selectedProduct.value = null;
    selectedVariant.value = null;
    selectedQuotedUnit.value = 'NOS';
    _resetCalculatedValues();
  }

  /// Reset calculated values
  void _resetCalculatedValues() {
    calculatedTotalLength.value = 0.0;
    calculatedTotalWeight.value = 0.0;
    calculatedAutoPrice.value = 0.0;
    calculatedTotalPrice.value = 0.0;
  }

  /// Recalculate all values when inputs change
  void _recalculateValues() {
    if (selectedVariant.value == null) {
      _resetCalculatedValues();
      return;
    }

    final variant = selectedVariant.value!;
    final quantity = double.tryParse(quotedQuantity.text) ?? 0.0;
    final unitLength = double.tryParse(unitLengthMeters.text) ?? 3.0; // Default 3m
    final markup = double.tryParse(markupPercentage.text) ?? 15.0;

    // Calculate total length
    if (selectedQuotedUnit.value == 'MTR') {
      calculatedTotalLength.value = quantity; // Quantity is already in meters
    } else {
      calculatedTotalLength.value = quantity * unitLength; // NOS * length per piece
    }

    // Calculate total weight
    if (variant.weight != null) {
      if (selectedQuotedUnit.value == 'MTR') {
        // For MTR, variant.weight is per 3m piece, so calculate weight per meter
        final weightPerMeter = variant.weight! / 3.0;
        calculatedTotalWeight.value = quantity * weightPerMeter;
      } else {
        // For NOS, variant.weight is per piece
        calculatedTotalWeight.value = quantity * variant.weight!;
      }
    } else {
      calculatedTotalWeight.value = 0.0;
    }

    // Calculate auto price
    _calculateAutoPrice(markup);

    // Calculate total price (use user price if provided, otherwise auto price)
    final userPrice = double.tryParse(userProvidedUnitPrice.text);
    final unitPrice = userPrice ?? calculatedAutoPrice.value;
    calculatedTotalPrice.value = unitPrice * quantity;
  }

  /// Calculate auto price based on weight and settings
  void _calculateAutoPrice(double markup) {
    if (calculatedTotalWeight.value <= 0 || selectedVariant.value == null) {
      calculatedAutoPrice.value = 0.0;
      return;
    }

    try {
      final settings = _settingsController.globalSettings.value;
      final variant = selectedVariant.value!;
      
      // Get material from variant attributes
      final material = variant.attributes['Material'] ?? 'GI';
      final ratePerKg = settings.materialRatesPerKg[material] ?? 0.5; // Default rate
      
      final quantity = double.tryParse(quotedQuantity.text) ?? 0.0;
      if (quantity <= 0) {
        calculatedAutoPrice.value = 0.0;
        return;
      }

      // Calculate: (totalWeight * ratePerKg * (1 + markup/100)) / quantity
      final totalCost = calculatedTotalWeight.value * ratePerKg * (1 + markup / 100);
      calculatedAutoPrice.value = totalCost / quantity;
    } catch (e) {
      calculatedAutoPrice.value = 0.0;
    }
  }

  /// Create a new deal item
  Future<void> createDealItem() async {
    try {
      if (!formKey.currentState!.validate()) return;
      if (selectedProduct.value == null || selectedVariant.value == null) {
        TLoaders.errorSnackBar(
          title: 'Selection Required',
          message: 'Please select a product and variant.'
        );
        return;
      }

      TFullScreenLoader.popUpCircular();
      
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final currentUser = _userController.user.value;
      final product = selectedProduct.value!;
      final variant = selectedVariant.value!;
      final settings = _settingsController.globalSettings.value;
      
      // Get material rate for historical tracking
      final material = variant.attributes['Material'] ?? 'GI';
      final ratePerKg = settings.materialRatesPerKg[material] ?? 0.5;

      final dealItem = DealItemModel(
        id: '', // Will be generated by Firestore
        dealId: currentDealId.value,
        productId: product.id,
        productVariantId: variant.id,
        productName: product.name,
        productDescription: product.description,
        productCategoryName: product.category?.name ?? '',
        productSegment: product.segment,
        productSku: variant.sku,
        variantAttributes: variant.attributes,
        quotedUnit: selectedQuotedUnit.value,
        quotedQuantity: double.parse(quotedQuantity.text),
        unitLengthMeters: selectedQuotedUnit.value == 'NOS' 
            ? double.tryParse(unitLengthMeters.text) ?? 3.0 
            : null,
        totalLengthMeters: calculatedTotalLength.value,
        factoryWeightPerPieceKg: variant.weight,
        factoryWeightPerMeterKg: variant.weight != null ? variant.weight! / 3.0 : null,
        totalFactoryWeightKg: calculatedTotalWeight.value,
        settingsRatePerKgAtCreation: ratePerKg,
        markupPercentage: double.parse(markupPercentage.text),
        autoCalculatedUnitPrice: calculatedAutoPrice.value,
        userProvidedUnitPrice: userProvidedUnitPrice.text.isNotEmpty 
            ? double.parse(userProvidedUnitPrice.text) 
            : null,
        totalItemPrice: calculatedTotalPrice.value,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdByUserId: currentUser.id!,
        updatedByUserId: currentUser.id!,
      );

      // Calculate implied rate if user provided price
      if (dealItem.userProvidedUnitPrice != null && calculatedTotalWeight.value > 0) {
        final impliedRate = (dealItem.userProvidedUnitPrice! * dealItem.quotedQuantity) / 
                           calculatedTotalWeight.value;
        dealItem.copyWith(impliedRatePerKgFromUserPrice: impliedRate);
      }

      await _dealRepository.addDealItem(currentDealId.value, dealItem);
      
      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Success',
        message: 'Deal item added successfully.'
      );
      
      resetForm();
      Get.back();
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Update an existing deal item
  Future<void> updateDealItem(DealItemModel item) async {
    try {
      TFullScreenLoader.popUpCircular();
      
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final currentUser = _userController.user.value;
      final updatedItem = item.copyWith(
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
      );

      await _dealRepository.updateDealItem(currentDealId.value, updatedItem);
      
      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Success',
        message: 'Deal item updated successfully.'
      );
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Delete a deal item
  Future<void> deleteDealItem(DealItemModel item) async {
    try {
      TFullScreenLoader.popUpCircular();
      
      await _dealRepository.deleteDealItem(currentDealId.value, item.id);
      
      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Success',
        message: 'Deal item deleted successfully.'
      );
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Set selected product and reset variant
  void setSelectedProduct(ProductModel product) {
    selectedProduct.value = product;
    selectedVariant.value = null; // Reset variant when product changes
    _recalculateValues();
  }

  /// Set selected variant
  void setSelectedVariant(ProductVariantModel variant) {
    selectedVariant.value = variant;
    
    // Auto-set unit length for linear products
    if (variant.attributes.containsKey('Length')) {
      final lengthStr = variant.attributes['Length']!;
      final lengthValue = double.tryParse(lengthStr.replaceAll('m', ''));
      if (lengthValue != null) {
        unitLengthMeters.text = lengthValue.toString();
      }
    }
    
    _recalculateValues();
  }

  /// Set quoted unit and recalculate
  void setQuotedUnit(String unit) {
    selectedQuotedUnit.value = unit;
    _recalculateValues();
  }

  /// Get total deal amount (sum of all items)
  double get totalDealAmount {
    return dealItems.fold(0.0, (sum, item) => sum + item.totalItemPrice);
  }

  /// Get total deal weight (sum of all items)
  double get totalDealWeight {
    return dealItems.fold(0.0, (sum, item) => sum + (item.totalFactoryWeightKg ?? 0.0));
  }

  /// Validate form fields
  String? validateQuantity(String? value) {
    if (value == null || value.isEmpty) {
      return 'Quantity is required';
    }
    final quantity = double.tryParse(value);
    if (quantity == null || quantity <= 0) {
      return 'Please enter a valid quantity';
    }
    return null;
  }

  String? validateUnitLength(String? value) {
    if (selectedQuotedUnit.value == 'NOS' && (value == null || value.isEmpty)) {
      return 'Unit length is required for NOS';
    }
    if (value != null && value.isNotEmpty) {
      final length = double.tryParse(value);
      if (length == null || length <= 0) {
        return 'Please enter a valid length';
      }
    }
    return null;
  }

  String? validateMarkup(String? value) {
    if (value == null || value.isEmpty) {
      return 'Markup percentage is required';
    }
    final markup = double.tryParse(value);
    if (markup == null || markup < 0) {
      return 'Please enter a valid markup percentage';
    }
    return null;
  }

  String? validateUserPrice(String? value) {
    if (value != null && value.isNotEmpty) {
      final price = double.tryParse(value);
      if (price == null || price < 0) {
        return 'Please enter a valid price';
      }
    }
    return null;
  }
}
