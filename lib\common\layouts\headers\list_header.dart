import 'package:flutter/material.dart';

class TListHeader extends StatelessWidget {
  const TListHeader({
    super.key,
    required this.title,
    this.showButton = true,
    this.buttonTitle = 'Add New',
    this.onPressed,
    this.textColor,
  });

  final String title;
  final bool showButton;
  final String buttonTitle;
  final VoidCallback? onPressed;
  final Color? textColor;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(title, style: Theme.of(context).textTheme.headlineMedium!.apply(color: textColor)),
        if (showButton)
          SizedBox(
            height: 30, //TSizes.buttonHeight,
            child: ElevatedButton(onPressed: onPressed, child: Text(buttonTitle)),
          ),
      ],
    );
  }
}
