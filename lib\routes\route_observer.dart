import 'package:alloy/common/layouts/sidebars/sidebar_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Custom route observer that tracks navigation changes and updates sidebar state
/// This class extends GetObserver to monitor navigation events in GetX routing system

// You might need to update your RouteObserver if you strictly rely on it
// for highlighting, but the new menuOnTap and onInit in SidebarController
// might reduce its direct importance for highlighting.
// However, for browser navigation (forward/back buttons), RouteObserver is still crucial.
class RouteObserver extends GetObserver {
  /// Called when a route is popped from the navigation stack (going back)
  /// @param route - The route being popped
  /// @param previousRoute - The route that becomes visible after popping

  @override
  void didPop(Route<dynamic>? route, Route<dynamic>? previousRoute) {
    // Get or create the sidebar controller instance
    final sidebarController = Get.put(SidebarController());

    // Only proceed if there's a valid previous route to navigate to
    if (previousRoute != null && previousRoute.settings.name != null) {
      // Update the active item in the sidebar to highlight the current page
      sidebarController.updateActiveAndExpandedState(previousRoute.settings.name!);
    }
  }

  /// Called when a new route is pushed onto the navigation stack
  /// @param route - The new route being pushed
  /// @param previousRoute - The route that was visible before pushing

  @override
  void didPush(Route<dynamic>? route, Route<dynamic>? previousRoute) {
    // Get or create the sidebar controller instance
    final sidebarController = Get.put(SidebarController());

    // Only proceed if there's a valid route being pushed
    if (route != null && route.settings.name != null) {
      // When navigating to a new screen, update the sidebar to highlight
      // the corresponding menu item

      // Update the active item in the sidebar to highlight the current page
      sidebarController.updateActiveAndExpandedState(route.settings.name!);
    }
  }
}
