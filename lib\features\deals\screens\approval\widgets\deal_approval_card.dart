import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/formatters/formatter.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../models/deal_model.dart';

/// Deal Approval Card Widget
/// Displays deal information with approval actions
class DealApprovalCard extends StatelessWidget {
  const DealApprovalCard({
    super.key,
    required this.deal,
    this.showApprovalActions = true,
    this.onApprove,
    this.onReject,
    this.onRequestUnlock,
    this.onApproveUnlock,
  });

  final DealModel deal;
  final bool showApprovalActions;
  final VoidCallback? onApprove;
  final VoidCallback? onReject;
  final VoidCallback? onRequestUnlock;
  final VoidCallback? onApproveUnlock;

  @override
  Widget build(BuildContext context) {
    final dark = THelperFunctions.isDarkMode(context);

    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: dark ? TColors.darkerGrey : TColors.white,
        borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
        border: Border.all(color: TColors.borderPrimary),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with deal number and status
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      deal.dealNumber,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: TSizes.xs),
                    Row(
                      children: [
                        Icon(_getStatusIcon(deal.status), size: 16, color: _getStatusColor(deal.status)),
                        const SizedBox(width: TSizes.xs),
                        Text(
                          _getStatusText(deal.status),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: _getStatusColor(deal.status),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Priority indicator
              ...[
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
                  decoration: BoxDecoration(
                    color: _getPriorityColor(deal.priority).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                    border: Border.all(color: _getPriorityColor(deal.priority)),
                  ),
                  child: Text(
                    deal.priority.name.toUpperCase(),
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: _getPriorityColor(deal.priority),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Deal details
          _buildDealDetails(context),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Financial summary
          _buildFinancialSummary(context),

          // Action buttons
          if (showApprovalActions || onRequestUnlock != null || onApproveUnlock != null) ...[
            const SizedBox(height: TSizes.spaceBtwItems),
            _buildActionButtons(context),
          ],
        ],
      ),
    );
  }

  /// Deal details section
  Widget _buildDealDetails(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(color: TColors.light, borderRadius: BorderRadius.circular(TSizes.borderRadiusSm)),
      child: Column(
        children: [
          _buildDetailRow(context, 'Client', deal.clientName, Iconsax.user),
          const SizedBox(height: TSizes.spaceBtwItems / 2),
          _buildDetailRow(context, 'Contact', deal.contactPersonName ?? 'Not specified', Iconsax.call),
          const SizedBox(height: TSizes.spaceBtwItems / 2),
          _buildDetailRow(context, 'Sales Person', deal.salesPersonName, Iconsax.profile_2user),
          const SizedBox(height: TSizes.spaceBtwItems / 2),
          _buildDetailRow(context, 'Created', TFormatter.formatDate(deal.createdAt), Iconsax.calendar),
        ],
      ),
    );
  }

  /// Detail row helper
  Widget _buildDetailRow(BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: TColors.darkGrey),
        const SizedBox(width: TSizes.xs),
        Text('$label: ', style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500)),
        Expanded(
          child: Text(value, style: Theme.of(context).textTheme.bodySmall, overflow: TextOverflow.ellipsis),
        ),
      ],
    );
  }

  /// Financial summary section
  Widget _buildFinancialSummary(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(
        color: TColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
        border: Border.all(color: TColors.primary.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(Iconsax.money, size: 16, color: TColors.primary),
          const SizedBox(width: TSizes.xs),
          Text('Total Value: ', style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500)),
          Expanded(
            child: Text(
              '₹${TFormatter.formatNumber(deal.totalDealValue)}',
              style: Theme.of(
                context,
              ).textTheme.titleSmall?.copyWith(color: TColors.primary, fontWeight: FontWeight.bold),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  /// Action buttons section
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        // Approval actions
        if (showApprovalActions) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onApprove,
              icon: const Icon(Iconsax.tick_circle),
              label: const Text('Approve'),
              style: ElevatedButton.styleFrom(backgroundColor: TColors.success, foregroundColor: TColors.white),
            ),
          ),
          const SizedBox(width: TSizes.spaceBtwItems),

          Expanded(
            child: ElevatedButton.icon(
              onPressed: onReject,
              icon: const Icon(Iconsax.close_circle),
              label: const Text('Reject'),
              style: ElevatedButton.styleFrom(backgroundColor: TColors.error, foregroundColor: TColors.white),
            ),
          ),
        ],

        // Unlock request action
        if (onRequestUnlock != null) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onRequestUnlock,
              icon: const Icon(Iconsax.unlock),
              label: const Text('Request Unlock'),
              style: ElevatedButton.styleFrom(backgroundColor: TColors.warning, foregroundColor: TColors.white),
            ),
          ),
        ],

        // Approve unlock action
        if (onApproveUnlock != null) ...[
          Expanded(
            child: ElevatedButton.icon(
              onPressed: onApproveUnlock,
              icon: const Icon(Iconsax.unlock),
              label: const Text('Approve Unlock'),
              style: ElevatedButton.styleFrom(backgroundColor: TColors.info, foregroundColor: TColors.white),
            ),
          ),
        ],
      ],
    );
  }

  /// Get status icon
  IconData _getStatusIcon(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Iconsax.document_text;
      case DealStatus.pendingApproval:
        return Iconsax.clock;
      case DealStatus.approved:
        return Iconsax.tick_circle;
      case DealStatus.rejected:
        return Iconsax.close_circle;
      case DealStatus.unlockRequested:
        return Iconsax.unlock;
      case DealStatus.quotationGenerated:
        return Iconsax.document_download;
      case DealStatus.clientApproved:
        return Iconsax.user_tick;
      case DealStatus.clientDeclined:
        return Iconsax.user_remove;
      case DealStatus.superseded:
        return Iconsax.refresh;
      case DealStatus.closed:
        return Iconsax.archive;
    }
  }

  /// Get status color
  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return TColors.darkGrey;
      case DealStatus.pendingApproval:
        return TColors.warning;
      case DealStatus.approved:
        return TColors.success;
      case DealStatus.rejected:
        return TColors.error;
      case DealStatus.unlockRequested:
        return TColors.info;
      case DealStatus.quotationGenerated:
        return TColors.primary;
      case DealStatus.clientApproved:
        return TColors.success;
      case DealStatus.clientDeclined:
        return TColors.error;
      case DealStatus.superseded:
        return TColors.warning;
      case DealStatus.closed:
        return TColors.darkGrey;
    }
  }

  /// Get status text
  String _getStatusText(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return 'Draft';
      case DealStatus.pendingApproval:
        return 'Pending Approval';
      case DealStatus.approved:
        return 'Approved';
      case DealStatus.rejected:
        return 'Rejected';
      case DealStatus.unlockRequested:
        return 'Unlock Requested';
      case DealStatus.quotationGenerated:
        return 'Quotation Generated';
      case DealStatus.clientApproved:
        return 'Client Approved';
      case DealStatus.clientDeclined:
        return 'Client Declined';
      case DealStatus.superseded:
        return 'Superseded';
      case DealStatus.closed:
        return 'Closed';
    }
  }

  /// Get priority color
  Color _getPriorityColor(DealPriority priority) {
    switch (priority) {
      case DealPriority.low:
        return TColors.success;
      case DealPriority.medium:
        return TColors.warning;
      case DealPriority.high:
        return TColors.error;
      case DealPriority.urgent:
        return TColors.error;
    }
  }
}
