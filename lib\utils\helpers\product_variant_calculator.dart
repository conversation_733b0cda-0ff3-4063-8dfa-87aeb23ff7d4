import '../../features/products/models/product_model.dart';
import '../../features/products/models/product_variant_model.dart';
import '../constants/enums.dart';
import '../popups/loaders.dart';

/// A utility class for calculating product variant properties and generating variants.
class ProductVariantCalculator {
  // --- Material Densities (kg/m^3) - Approximations
  static const Map<String, double> _materialDensities = {
    'PG': 7850, // Pre-Galvanized Steel
    'GI': 7850, // Galvanized Iron (Mild Steel)
    'HR': 7850, // Hot Rolled Steel
    'HDG': 7850, // Hot-Dip Galvanized Steel
    'SS304': 8000, // Stainless Steel 304
    'SS316': 8000, // Stainless Steel 316
  };

  /// Helper function to calculate weight (kg) and surface area (sqm) based on product category and dimensions.
  /// This method now returns both 'factory' and 'commercial' weights.
  ///
  /// IMPORTANT: For ProductSegment.accessories, this function will primarily rely on
  /// the pre-defined weight in ProductVariantModel, as geometric calculation is complex.
  static Map<String, double> calculateWeightAndSqm({
    required String productCategoryName,
    required ProductSegment productSegment, // NEW: Pass segment to differentiate calculation
    required double widthMm,
    required double heightMm,
    required double lengthM, // Length of the piece being calculated (e.g., 3.0 for standard)
    required double thicknessMm,
    required String material,
    double? accessoryWeightPerPieceKg, // NEW: Pass pre-defined weight for accessories
  }) {
    double factoryDevelopedWidthMm = 0.0;
    double commercialDevelopedWidthMm = 0.0;
    double factorySurfaceAreaSqm = 0.0;
    double commercialSurfaceAreaSqm = 0.0;
    double factoryWeightKg = 0.0;
    double commercialWeightKg = 0.0;

    final double thicknessM = thicknessMm / 1000.0; // Convert mm to meters
    final double materialDensity = _materialDensities[material] ?? 7850.0; // Default to steel density

    // Convert dimensions to meters for calculation
    final double widthM = widthMm / 1000.0;
    final double heightM = heightMm / 1000.0;

    // Define specific flange lengths (in mm)
    const double cableTrayFactoryFlange = 12.5;
    const double trunkingFactoryFlange = 10.0;
    const double ladderSideRailFactoryFlange = 20.0;
    const double channelFactoryFlange = 3.5;

    // Define commercial/sales specific flange lengths (in mm)
    const double cableTrayCommercialFlange = 15.0;
    const double channelCommercialFlange = 7.5;
    // For trunking and ladder, assuming commercial flange is same as factory unless specified otherwise
    const double trunkingCommercialFlange = 10.0;
    const double ladderSideRailCommercialFlange = 20.0;

    // Rung dimensions for ladder (in mm)
    const double rungCrossSectionWidth = 41.0;
    const double rungCrossSectionHeight = 21.0;
    const int numberOfRungsPer3Meter = 10;
    final int actualNumberOfRungs = (lengthM / 3.0 * numberOfRungsPer3Meter).round();

    // --- Handle Accessories Separately ---
    if (productSegment == ProductSegment.accessories) {
      // For accessories, we rely on the pre-defined weight per piece.
      // Surface area calculation is typically not relevant or too complex for standard accessories.
      factoryWeightKg = accessoryWeightPerPieceKg ?? 0.0;
      commercialWeightKg = accessoryWeightPerPieceKg ?? 0.0; // Assuming same for commercial
      factorySurfaceAreaSqm = 0.0; // Or a default small value if needed for display
      commercialSurfaceAreaSqm = 0.0; // Or a default small value if needed for display
    } else {
      // --- Calculations for Linear Items (Lengths) ---
      switch (productCategoryName.toLowerCase()) {
        case 'cable tray':
          factoryDevelopedWidthMm = widthMm + (2 * heightMm) + (2 * cableTrayFactoryFlange);
          commercialDevelopedWidthMm = widthMm + (2 * heightMm) + (2 * cableTrayCommercialFlange);
          break;
        case 'trunking':
          factoryDevelopedWidthMm = widthMm + (2 * heightMm) + (2 * trunkingFactoryFlange);
          commercialDevelopedWidthMm = widthMm + (2 * heightMm) + (2 * trunkingCommercialFlange); // Same as factory
          break;
        case 'cable ladder':
          // Ladder calculations are complex and might not have a direct "flange" concept for commercial vs factory.
          // Assuming side rails are the primary contributors and their "flange" is consistent for both.
          final double sideRailFactoryDevelopedWidthMm = heightMm + (2 * ladderSideRailFactoryFlange);
          final double sideRailCommercialDevelopedWidthMm =
              heightMm + (2 * ladderSideRailCommercialFlange); // Same as factory

          final double rungPerimeterMm = 2 * (rungCrossSectionWidth + rungCrossSectionHeight);
          final double singleRungSurfaceAreaSqm = (rungPerimeterMm / 1000.0) * (widthMm / 1000.0);

          factorySurfaceAreaSqm =
              (2 * (sideRailFactoryDevelopedWidthMm / 1000.0) * lengthM) +
              (actualNumberOfRungs * singleRungSurfaceAreaSqm);
          commercialSurfaceAreaSqm =
              (2 * (sideRailCommercialDevelopedWidthMm / 1000.0) * lengthM) +
              (actualNumberOfRungs * singleRungSurfaceAreaSqm);
          break;
        case 'unistrut channel':
          factoryDevelopedWidthMm = widthMm + (2 * heightMm) + (2 * channelFactoryFlange);
          commercialDevelopedWidthMm = widthMm + (2 * heightMm) + (2 * channelCommercialFlange);
          break;
        default:
          TLoaders.warningSnackBar(
            title: 'Unknown Product Category',
            message:
                'Weight/SQM calculation for category "$productCategoryName" is an approximation. Please define specific formulas.',
          );
          factoryDevelopedWidthMm = widthMm; // Fallback: simple flat sheet
          commercialDevelopedWidthMm = widthMm; // Fallback: simple flat sheet
          break;
      }

      // Calculate Surface Area (SQM) if not already calculated (for ladder)
      if (productCategoryName.toLowerCase() != 'cable ladder') {
        factorySurfaceAreaSqm = (factoryDevelopedWidthMm / 1000.0) * lengthM;
        commercialSurfaceAreaSqm = (commercialDevelopedWidthMm / 1000.0) * lengthM;
      }

      // --- Calculate Volume and Weight for Linear Items ---
      final double factoryVolumeM3 = factorySurfaceAreaSqm * thicknessM;
      factoryWeightKg = factoryVolumeM3 * materialDensity;

      final double commercialVolumeM3 = commercialSurfaceAreaSqm * thicknessM;
      commercialWeightKg = commercialVolumeM3 * materialDensity;
    }

    // Round to a reasonable number of decimal places for display/storage
    factoryWeightKg = double.parse(factoryWeightKg.toStringAsFixed(3));
    commercialWeightKg = double.parse(commercialWeightKg.toStringAsFixed(3));
    factorySurfaceAreaSqm = double.parse(factorySurfaceAreaSqm.toStringAsFixed(3));
    commercialSurfaceAreaSqm = double.parse(commercialSurfaceAreaSqm.toStringAsFixed(3));

    return {
      'factoryWeightKg': factoryWeightKg,
      'commercialWeightKg': commercialWeightKg,
      'factorySqm': factorySurfaceAreaSqm,
      'commercialSqm': commercialSurfaceAreaSqm, // Though SQM might be the same, returning for consistency
    };
  }

  /// Helper to generate variant combinations (Cartesian product)
  /// This function now correctly handles the 'weight' in ProductVariantModel
  /// as the weight of a 3-meter piece for linear items, or weight per piece for accessories.
  static List<ProductVariantModel> generateVariants({
    required ProductModel product,
    required List<String> selectedMaterials,
    required List<String> selectedThicknesses,
    required List<String> selectedFinishes,
    required List<String> selectedLengths,
    required int quantityOnHand,
    required int quantityOnOrder,
    required int quantityInProduction,
  }) {
    final List<ProductVariantModel> variants = [];

    if (selectedMaterials.isEmpty ||
        selectedThicknesses.isEmpty ||
        selectedFinishes.isEmpty ||
        selectedLengths.isEmpty) {
      return variants;
    }

    final double baseWidth = product.width;
    final double baseHeight = product.height;
    final String productCategoryName = product.category?.name ?? '';
    final ProductSegment productSegment = product.segment; // Get segment from product

    for (final material in selectedMaterials) {
      for (final thicknessStr in selectedThicknesses) {
        for (final finish in selectedFinishes) {
          for (final length in selectedLengths) {
            final double thicknessMm = double.tryParse(thicknessStr.replaceAll('mm', '').trim()) ?? 0.0;
            final double lengthM = double.tryParse(length.replaceAll('m', '').trim()) ?? 0.0;

            double variantWeightKg = 0.0;
            double variantSqm = 0.0;

            if (productSegment == ProductSegment.accessories) {
              // For accessories, the weight is expected to be directly provided
              // or set manually during product/variant creation.
              // We cannot calculate it geometrically here.
              // For now, we'll assign a placeholder or expect it to be updated post-creation.
              // In a real scenario, this might come from an 'accessory_weights' map
              // or require manual input during the product variant creation process.
              // For the purpose of this generation, we'll set it to 0.0,
              // or if ProductModel had an 'averageAccessoryWeight', we could use that.
              // For now, let's assume it will be set to 0.0 and needs manual update.
              // Or, if the ProductVariantModel.weight is intended to be pre-filled
              // for accessories, it would be passed into this method.
              // For `generateVariants`, we cannot dynamically get `accessoryWeightPerPieceKg`
              // unless it's part of `product` or `attributes`.
              // For now, we'll leave it as 0.0 and note that it needs manual input
              // or a more sophisticated lookup for accessories.
              variantWeightKg = 0.0; // Placeholder for accessories
              variantSqm = 0.0; // Placeholder for accessories
              TLoaders.warningSnackBar(
                title: 'Accessory Weight',
                message: 'Weight for accessory variants is not auto-calculated. Please set manually.',
              );
            } else {
              // For linear items (lengths, hardware if linear), calculate weight per 3m piece
              // The calculateWeightAndSqm function returns total weight for the given length.
              // We need to convert this to weight per 3m piece for storage in ProductVariantModel.
              final Map<String, double> calculatedValuesFor3mPiece = calculateWeightAndSqm(
                productCategoryName: productCategoryName,
                productSegment: productSegment, // Pass segment
                widthMm: baseWidth,
                heightMm: baseHeight,
                lengthM: 3.0, // Calculate for a standard 3m piece
                thicknessMm: thicknessMm,
                material: material,
              );
              variantWeightKg = calculatedValuesFor3mPiece['factoryWeightKg'] ?? 0.0; // Store weight of a 3m piece
              // SQM for a 3m piece (if needed for ProductVariantModel)
              variantSqm = calculatedValuesFor3mPiece['factorySqm'] ?? 0.0;
            }

            final attributes = {'Material': material, 'Thickness': thicknessStr, 'Finish': finish, 'Length': length};

            final sku = '${product.name.replaceAll(' ', '-')}-$material-$thicknessStr-$finish-$length'
                .replaceAll('.', '_')
                .toLowerCase();

            variants.add(
              ProductVariantModel(
                id: '',
                productId: product.id,
                categoryId: product.categoryId,
                segment: product.segment,
                sku: sku,
                attributes: attributes,
                weight: variantWeightKg, // Assign calculated weight per 3m piece (for linear) or 0.0 (for accessories)
                sqm: variantSqm, // Assign calculated SQM per 3m piece (for linear) or 0.0 (for accessories)
                quantityOnHand: quantityOnHand,
                quantityOnOrder: quantityOnOrder,
                quantityInProduction: quantityInProduction,
              ),
            );
          }
        }
      }
    }
    return variants;
  }
}
