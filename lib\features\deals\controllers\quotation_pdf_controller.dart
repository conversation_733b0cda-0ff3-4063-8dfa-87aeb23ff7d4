import 'dart:io';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import '../../../utils/constants/enums.dart';
import '../../../utils/helpers/network_manager.dart';
import '../../../utils/popups/full_screen_loader.dart';
import '../../../utils/popups/loaders.dart';
import '../../authentication/controllers/user_controller.dart';
import '../../settings/controllers/settings_controller.dart';
import '../models/deal_item_model.dart';
import '../models/deal_model.dart';
import '../repositroy/deal_repository.dart';
;

/// Quotation PDF Generation Controller
/// Handles PDF generation, role-based access, and document management
class QuotationPdfController extends GetxController {
  static QuotationPdfController get instance => Get.find();

  final _dealRepository = Get.put(DealRepository());
  final _userController = Get.put(UserController());
  final _settingsController = Get.put(SettingsController());

  // Loading states
  final isGeneratingPdf = false.obs;
  final generatedPdfPath = ''.obs;

  /// Generate quotation PDF for approved deal
  Future<void> generateQuotationPdf(DealModel deal) async {
    try {
      isGeneratingPdf.value = true;
      TFullScreenLoader.popUpCircular();

      // Validate permissions
      if (!_canGeneratePdf(deal)) {
        throw 'You do not have permission to generate PDF for this deal';
      }

      // Validate deal status
      if (deal.status != DealStatus.approved) {
        throw 'PDF can only be generated for approved deals';
      }

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Get deal items
      final dealItems = await _dealRepository.getDealItems(deal.id);
      
      // Generate PDF document
      final pdfDocument = await _createPdfDocument(deal, dealItems);
      
      // Save PDF to device
      final pdfPath = await _savePdfToDevice(pdfDocument, deal.dealNumber);
      generatedPdfPath.value = pdfPath;

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'PDF Generated',
        message: 'Quotation PDF has been generated successfully.',
      );

      // Show PDF preview/share options
      _showPdfOptions(pdfDocument, deal.dealNumber);

    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'PDF Generation Failed', message: e.toString());
    } finally {
      isGeneratingPdf.value = false;
    }
  }

  /// Create PDF document
  Future<pw.Document> _createPdfDocument(DealModel deal, List<DealItemModel> items) async {
    final pdf = pw.Document();
    final settings = _settingsController.globalSettings.value;

    // Load company logo if available
    pw.ImageProvider? logoImage;
    try {
      final logoBytes = await rootBundle.load('assets/logos/company_logo.png');
      logoImage = pw.MemoryImage(logoBytes.buffer.asUint8List());
    } catch (e) {
      // Logo not found, continue without it
    }

    // Calculate totals
    final subtotal = items.fold<double>(0.0, (sum, item) => sum + item.totalItemPrice);
    final taxRate = settings.defaultTaxRate ?? 18.0;
    final taxAmount = subtotal * (taxRate / 100);
    final grandTotal = subtotal + taxAmount;

    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        margin: const pw.EdgeInsets.all(32),
        header: (context) => _buildHeader(deal, settings, logoImage),
        footer: (context) => _buildFooter(context, settings),
        build: (context) => [
          // Deal information
          _buildDealInfo(deal),
          pw.SizedBox(height: 20),
          
          // Items table
          _buildItemsTable(items),
          pw.SizedBox(height: 20),
          
          // Totals section
          _buildTotalsSection(subtotal, taxAmount, taxRate, grandTotal),
          pw.SizedBox(height: 20),
          
          // Terms and conditions
          _buildTermsAndConditions(settings),
        ],
      ),
    );

    return pdf;
  }

  /// Build PDF header
  pw.Widget _buildHeader(DealModel deal, dynamic settings, pw.ImageProvider? logoImage) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(bottom: pw.BorderSide(color: PdfColors.grey300)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          // Company info
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              if (logoImage != null) ...[
                pw.Image(logoImage, width: 100, height: 50),
                pw.SizedBox(height: 10),
              ],
              pw.Text(
                settings.companyName ?? 'Company Name',
                style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
              ),
              pw.Text(settings.companyAddress ?? 'Company Address'),
              pw.Text('Phone: ${settings.companyPhone ?? 'N/A'}'),
              pw.Text('Email: ${settings.companyEmail ?? 'N/A'}'),
            ],
          ),
          
          // Quotation info
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                'QUOTATION',
                style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
              ),
              pw.SizedBox(height: 10),
              pw.Text('Quote No: ${deal.dealNumber}'),
              pw.Text('Date: ${_formatDate(deal.createdAt)}'),
              pw.Text('Valid Until: ${_formatDate(deal.validUntil)}'),
            ],
          ),
        ],
      ),
    );
  }

  /// Build deal information section
  pw.Widget _buildDealInfo(DealModel deal) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Bill To:',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 5),
          pw.Text(deal.clientName, style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
          pw.Text('Contact: ${deal.contactPersonName}'),
          pw.Text('Phone: ${deal.contactPersonPhone}'),
          pw.Text('Email: ${deal.contactPersonEmail}'),
          
          if (deal.projectDetails.isNotEmpty) ...[
            pw.SizedBox(height: 10),
            pw.Text(
              'Project Details:',
              style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
            ),
            pw.Text(deal.projectDetails, style: const pw.TextStyle(fontSize: 11)),
          ],
        ],
      ),
    );
  }

  /// Build items table
  pw.Widget _buildItemsTable(List<DealItemModel> items) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(3),
        2: const pw.FlexColumnWidth(1.5),
        3: const pw.FlexColumnWidth(1.5),
        4: const pw.FlexColumnWidth(1.5),
        5: const pw.FlexColumnWidth(2),
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell('S.No.', isHeader: true),
            _buildTableCell('Description', isHeader: true),
            _buildTableCell('Quantity', isHeader: true),
            _buildTableCell('Unit', isHeader: true),
            _buildTableCell('Rate', isHeader: true),
            _buildTableCell('Amount', isHeader: true),
          ],
        ),
        
        // Data rows
        ...items.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          
          return pw.TableRow(
            children: [
              _buildTableCell('${index + 1}'),
              _buildTableCell(_buildItemDescription(item)),
              _buildTableCell(_formatNumber(item.quotedQuantity)),
              _buildTableCell(item.quotedUnit),
              _buildTableCell('₹${_formatNumber(item.userProvidedUnitPrice ?? item.autoCalculatedUnitPrice)}'),
              _buildTableCell('₹${_formatNumber(item.totalItemPrice)}'),
            ],
          );
        }),
      ],
    );
  }

  /// Build table cell
  pw.Widget _buildTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: isHeader ? pw.TextAlign.center : pw.TextAlign.left,
      ),
    );
  }

  /// Build item description
  String _buildItemDescription(DealItemModel item) {
    final parts = <String>[item.productName];
    
    if (item.productDescription.isNotEmpty) {
      parts.add(item.productDescription);
    }
    
    // Add variant attributes
    if (item.variantAttributes.isNotEmpty) {
      final attributes = <String>[];
      if (item.variantAttributes.containsKey('Material')) {
        attributes.add('Material: ${item.variantAttributes['Material']}');
      }
      if (item.variantAttributes.containsKey('Thickness')) {
        attributes.add('Thickness: ${item.variantAttributes['Thickness']}');
      }
      if (item.variantAttributes.containsKey('Length')) {
        attributes.add('Length: ${item.variantAttributes['Length']}');
      }
      
      if (attributes.isNotEmpty) {
        parts.add('(${attributes.join(', ')})');
      }
    }
    
    return parts.join('\n');
  }

  /// Build totals section
  pw.Widget _buildTotalsSection(double subtotal, double taxAmount, double taxRate, double grandTotal) {
    return pw.Container(
      alignment: pw.Alignment.centerRight,
      child: pw.Container(
        width: 250,
        child: pw.Column(
          children: [
            _buildTotalRow('Subtotal:', '₹${_formatNumber(subtotal)}'),
            _buildTotalRow('Tax (${taxRate.toStringAsFixed(0)}%):', '₹${_formatNumber(taxAmount)}'),
            pw.Divider(color: PdfColors.grey400),
            _buildTotalRow(
              'Grand Total:',
              '₹${_formatNumber(grandTotal)}',
              isGrandTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  /// Build total row
  pw.Widget _buildTotalRow(String label, String amount, {bool isGrandTotal = false}) {
    return pw.Padding(
      padding: const pw.EdgeInsets.symmetric(vertical: 3),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              fontSize: isGrandTotal ? 14 : 12,
              fontWeight: isGrandTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
          pw.Text(
            amount,
            style: pw.TextStyle(
              fontSize: isGrandTotal ? 14 : 12,
              fontWeight: isGrandTotal ? pw.FontWeight.bold : pw.FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  /// Build terms and conditions
  pw.Widget _buildTermsAndConditions(dynamic settings) {
    final terms = settings.quotationTerms ?? [
      '1. This quotation is valid for 30 days from the date of issue.',
      '2. Prices are subject to change without prior notice.',
      '3. Payment terms: 50% advance, 50% on delivery.',
      '4. Delivery time: 15-20 working days from order confirmation.',
      '5. All disputes are subject to local jurisdiction.',
    ];

    return pw.Container(
      padding: const pw.EdgeInsets.all(15),
      decoration: pw.BoxDecoration(
        border: pw.Border.all(color: PdfColors.grey300),
        borderRadius: pw.BorderRadius.circular(5),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Terms & Conditions:',
            style: pw.TextStyle(fontSize: 14, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          ...terms.map((term) => pw.Padding(
            padding: const pw.EdgeInsets.only(bottom: 3),
            child: pw.Text(term, style: const pw.TextStyle(fontSize: 10)),
          )).toList(),
        ],
      ),
    );
  }

  /// Build footer
  pw.Widget _buildFooter(pw.Context context, dynamic settings) {
    return pw.Container(
      alignment: pw.Alignment.center,
      margin: const pw.EdgeInsets.only(top: 20),
      padding: const pw.EdgeInsets.only(top: 10),
      decoration: const pw.BoxDecoration(
        border: pw.Border(top: pw.BorderSide(color: PdfColors.grey300)),
      ),
      child: pw.Column(
        children: [
          pw.Text(
            'Thank you for your business!',
            style: pw.TextStyle(fontSize: 12, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 5),
          pw.Text(
            'Page ${context.pageNumber} of ${context.pagesCount}',
            style: const pw.TextStyle(fontSize: 10),
          ),
        ],
      ),
    );
  }

  /// Save PDF to device
  Future<String> _savePdfToDevice(pw.Document pdf, String dealNumber) async {
    final output = await getApplicationDocumentsDirectory();
    final fileName = 'Quotation_${dealNumber}_${DateTime.now().millisecondsSinceEpoch}.pdf';
    final file = File('${output.path}/$fileName');
    await file.writeAsBytes(await pdf.save());
    return file.path;
  }

  /// Show PDF options (preview, share, etc.)
  Future<void> _showPdfOptions(pw.Document pdf, String dealNumber) async {
    await Printing.layoutPdf(
      onLayout: (format) async => pdf.save(),
      name: 'Quotation_$dealNumber',
    );
  }

  /// Check if user can generate PDF
  bool _canGeneratePdf(DealModel deal) {
    final currentUser = _userController.user.value;
    
    // Admins and managers can generate any PDF
    if (currentUser.role == UserRole.admin || currentUser.role == UserRole.manager) {
      return true;
    }
    
    // Sales users can only generate PDF for their own deals
    if (currentUser.role == UserRole.salesUser) {
      return deal.salesPersonId == currentUser.id;
    }
    
    return false;
  }

  /// Format date for PDF
  String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  /// Format number for PDF
  String _formatNumber(double number) {
    return number.toStringAsFixed(2);
  }

  /// Check if current user can generate PDFs
  bool get canGeneratePdf {
    final currentUser = _userController.user.value;
    return currentUser.role == UserRole.admin || 
           currentUser.role == UserRole.manager || 
           currentUser.role == UserRole.salesUser;
  }
}
