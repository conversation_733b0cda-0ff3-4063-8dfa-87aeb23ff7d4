import 'package:alloy/features/account/screens/all_accounts/accounts_screen.dart';
import 'package:alloy/features/account/screens/create_account/create_account_screen.dart';
import 'package:alloy/features/account/screens/import_accounts/import_accounts_screen.dart';
import 'package:alloy/features/authentication/screens/forget_password/forget_password_screen.dart';
import 'package:alloy/features/authentication/screens/signup/signup_screen.dart';
import 'package:alloy/features/category/screens/all_categories/category_screen.dart';
import 'package:alloy/features/contact/screens/all_contacts/contacts_screen.dart';
import 'package:alloy/features/contact/screens/create_contact/create_contact_screen.dart';
import 'package:alloy/features/brand/screens/all_brand/brands_screen.dart';
import 'package:alloy/features/dashboard/dashboard_screen.dart';
import 'package:alloy/features/personilization/screens/profile/profile_screen.dart';
import 'package:alloy/features/products/screens/all_products/product_screen.dart';
import 'package:alloy/features/products/screens/create_product/create_product_screen.dart';
import 'package:alloy/features/products/screens/product_variants/widget/bulk_variant_creation_widget.dart';
import 'package:alloy/features/permissions/models/module_permission_model.dart';
import 'package:alloy/routes/permission_guard.dart';
import 'package:alloy/routes/route_guard.dart';
import 'package:alloy/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../features/settings/screens/settings_screen.dart';
import '../features/deals/screens/all_deals/deals_screen.dart';

class TAppRoutes {
  static final List<GetPage> pages = [
    GetPage(name: TRoutes.forgotPassword, page: () => const ForgetPasswordScreen()),
    GetPage(name: TRoutes.signup, page: () => const SignupScreen()),
    GetPage(name: TRoutes.dashboard, page: () => const DashboardScreen(), middlewares: [AuthGuard(), DashboardGuard()]),
    GetPage(name: TRoutes.brands, page: () => const BrandsScreen(), middlewares: [AuthGuard(), BrandsGuard()]),
    GetPage(name: TRoutes.accounts, page: () => const AccountsScreen(), middlewares: [AuthGuard(), AccountsGuard()]),
    GetPage(
      name: TRoutes.createAccount,
      page: () => const CreateAccountScreen(),
      middlewares: [AuthGuard(), CreateAccountGuard()],
    ),
    GetPage(
      name: TRoutes.importAccounts,
      page: () => const ImportAccountsScreen(),
      middlewares: [AuthGuard(), ImportGuard(AppModule.accounts)],
    ),
    GetPage(name: TRoutes.contacts, page: () => const ContactsScreen(), middlewares: [AuthGuard(), ContactsGuard()]),
    GetPage(
      name: TRoutes.createContact,
      page: () => const CreateContactScreen(),
      middlewares: [AuthGuard(), CreateContactGuard()],
    ),
    GetPage(name: TRoutes.deals, page: () => const DealsScreen(), middlewares: [AuthGuard(), DealsGuard()]),
    GetPage(name: TRoutes.products, page: () => const ProductsScreen(), middlewares: [AuthGuard(), ProductsGuard()]),
    GetPage(
      name: TRoutes.createProduct,
      page: () => const CreateProductScreen(),
      middlewares: [AuthGuard(), CreateProductGuard()],
    ),
    GetPage(
      name: TRoutes.bulkCreateVariants,
      page: () => const BulkVariantCreationWidget(),
      middlewares: [AuthGuard(), ProductsGuard()],
    ),
    GetPage(
      name: TRoutes.categories,
      page: () => const CategoryScreen(),
      middlewares: [AuthGuard(), CategoriesGuard()],
    ),
    GetPage(name: TRoutes.settings, page: () => const SettingsScreen(), middlewares: [AuthGuard(), SettingsGuard()]),

    // New Human Resources routes
    // The parent HR route might lead to an overview page or just serve as a collapsible header
    // GetPage(
    //   name: TRoutes.humanResources,
    //   page: () => Container(color: Colors.red, child: Text('Human Resources Overview')),
    //   middlewares: [AuthGuard()],
    // ), // Create this screen
    GetPage(
      name: TRoutes.users,
      page: () => Container(color: Colors.teal, child: Text('Users Overview')),
      middlewares: [AuthGuard()],
    ),
    GetPage(
      name: TRoutes.workers,
      page: () => Container(color: Colors.blue, child: Text('Workers Overview')),
      middlewares: [AuthGuard()],
    ),

    GetPage(name: TRoutes.profile, page: () => const ProfileScreen(), middlewares: [AuthGuard()]),
  ];
}
