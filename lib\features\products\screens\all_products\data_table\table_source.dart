import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:data_table_2/data_table_2.dart'; // Ensure data_table2 is imported
import 'package:iconsax/iconsax.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/product_svg_icons.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controller/product_controller/product_controller.dart';
import '../../../controller/variant_controller/product_variant_controller.dart';
import '../../edit_product/edit_product_screen.dart';
import '../../product_variants/product_variants_screen.dart';
import '../widget/copy_product_dialog.dart'; // Iconsax for action icons

class ProductsRows extends DataTableSource {
  final controller = Get.find<ProductController>();
  final productVariantController = Get.find<ProductVariantController>(); // Get the variant controller

  @override
  DataRow2? getRow(int index) {
    // Return null if index is out of bounds (shouldn't happen with correct rowCount)
    if (index >= controller.filteredItems.length) {
      return null;
    }

    final product = controller.filteredItems[index];

    // Determine which SVG icon to display based on product name/category
    Widget productIcon;
    final productNameLower = product.name.toLowerCase();
    if (productNameLower.contains('tray')) {
      productIcon = ProductSvgIcons.cableTray();
    } else if (productNameLower.contains('ladder')) {
      productIcon = ProductSvgIcons.cableLadder();
    } else if (productNameLower.contains('channel')) {
      productIcon = ProductSvgIcons.channel();
    } else if (productNameLower.contains('trunking')) {
      productIcon = ProductSvgIcons.trunking();
    } else {
      productIcon = ProductSvgIcons.placeholder(); // Generic placeholder
    }

    return DataRow2(
      // Selection logic (if you implement multi-select for products)
      selected: controller.selectedRows.length > index ? controller.selectedRows[index] : false,
      onSelectChanged: (isSelected) {
        if (controller.selectedRows.length > index) {
          controller.selectedRows[index] = isSelected ?? false;
          // You might want to refresh the table if selection changes affect UI
          notifyListeners();
        }
      },
      onTap: () {
        // Load variants and navigate to ProductVariantsScreen
        productVariantController.loadVariantsForProduct(product.id);
        Get.to(() => ProductVariantsScreen(product: product));
      },
      cells: [
        DataCell(productIcon),
        DataCell(Text(product.name)),
        DataCell(Text(product.category?.name ?? 'N/A')), // Display category name, 'N/A' if null
        DataCell(Text(THelperFunctions.capitalizeFirstLetter(product.segment.name))), // Capitalize segment name
        DataCell(Text('${product.width} x ${product.height} mm')),
        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min, // Keep action buttons compact
            children: [
              // NEW: Copy Button
              IconButton(
                onPressed: () {
                  Get.dialog(
                    CopyProductDialog(
                      originalProduct: product,
                      onCopy: (newName, newWidth, newHeight) {
                        controller.copyProduct(product, newName, newWidth: newWidth, newHeight: newHeight);
                      },
                    ),
                  );
                },
                icon: const Icon(Iconsax.copy),
                tooltip: 'Copy Product',
              ),
              IconButton(
                onPressed: () {
                  // Navigate to EditProductScreen, passing the product to be edited
                  Get.to(() => EditProductScreen(product: product));
                },
                icon: const Icon(Iconsax.edit),
                tooltip: 'Edit Product',
              ),
              IconButton(
                onPressed: () => controller.confirmDeleteItem(product),
                icon: const Icon(Iconsax.trash, color: TColors.error),
                tooltip: 'Delete Product',
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false; // We know the exact row count

  @override
  int get rowCount => controller.filteredItems.length; // Number of rows based on filtered items

  @override
  int get selectedRowCount => controller.selectedRows.where((isSelected) => isSelected).length; // Count of selected rows
}
