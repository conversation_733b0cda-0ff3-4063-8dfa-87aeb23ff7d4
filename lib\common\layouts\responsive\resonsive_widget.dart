import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';

/// Widget for displaying different layouts based on screen size
class TResponsiveWidget extends StatelessWidget {
  /// Constructor for responsive widget that requires different layouts for each screen size

  const TResponsiveWidget({super.key, required this.desktop, required this.tablet, required this.mobile});

  /// Widget for desktop layout
  final Widget desktop;

  /// Widget for tablet layout
  final Widget tablet;

  /// Widget for mobile layout
  final Widget mobile;

  @override
  Widget build(BuildContext context) {
    // Use LayoutBuilder to get the constraints of the parent widget
    return LayoutBuilder(
      builder: (_, constraints) {
        // Check if width is greater than or equal to desktop screen size
        if (constraints.maxWidth >= TSizes.desktopScreenSize) {
          return desktop;
        }
        // Check if width is less than desktop but greater than or equal to tablet screen size
        else if (constraints.maxWidth < TSizes.desktopScreenSize && constraints.maxWidth >= TSizes.tabletScreenSize) {
          return tablet;
        }
        // If width is less than tablet screen size, show mobile layout
        else {
          return mobile;
        }
      },
    );
  }
}
