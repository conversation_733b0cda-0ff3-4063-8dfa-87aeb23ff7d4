import 'package:alloy/common/widgets/data_table/paginated_data_table.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class HandlerSelectionDialog extends StatefulWidget {
  // Initial list of handlers already selected
  final List<UserModel> initialSelectedHandlers;
  // Callback to return the final selected list
  final ValueChanged<List<UserModel>> onHandlersSelected;

  const HandlerSelectionDialog({super.key, required this.initialSelectedHandlers, required this.onHandlersSelected});

  @override
  State<HandlerSelectionDialog> createState() => _HandlerSelectionDialogState();
}

class _HandlerSelectionDialogState extends State<HandlerSelectionDialog> {
  final userController = Get.find<UserController>();
  late List<UserModel> _selectedHandlers; // Local state for selections in the dialog

  @override
  void initState() {
    super.initState();
    // Initialize local selected handlers with the initial ones passed in
    _selectedHandlers = List.from(widget.initialSelectedHandlers);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Select Handlers'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(hintText: 'Search Handlers', prefixIcon: Icon(Icons.search)),
              onChanged: (value) => userController.searchQuery(value),
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (userController.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              return TPaginatedDataTable(
                // Ensure TPaginatedDataTable is correctly implemented
                columns: const [
                  DataColumn2(label: Text('Name')),
                  DataColumn2(label: Text('Email')),
                  DataColumn2(label: Text('Phone')),
                ],
                source: _HandlerDataSource(
                  handlers: userController.filteredItems,
                  selectedHandlers: _selectedHandlers,
                  onRowSelected: (handler) {
                    setState(() {
                      // Toggle selection based on handler ID
                      if (_selectedHandlers.any((element) => element.id == handler.id)) {
                        _selectedHandlers.removeWhere((element) => element.id == handler.id);
                      } else {
                        _selectedHandlers.add(handler);
                      }
                    });
                  },
                ),
              );
            }),
          ],
        ),
      ),
      actions: [
        TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
        ElevatedButton(
          onPressed: () {
            // Pass the final selected list back via the callback
            widget.onHandlersSelected(_selectedHandlers);
            Get.back();
          },
          child: const Text('Done'),
        ),
      ],
    );
  }
}

// _HandlerDataSource remains the same as before
class _HandlerDataSource extends DataTableSource {
  final List<UserModel> handlers;
  final List<UserModel> selectedHandlers;
  final Function(UserModel) onRowSelected;

  _HandlerDataSource({required this.handlers, required this.selectedHandlers, required this.onRowSelected});

  @override
  DataRow? getRow(int index) {
    final handler = handlers[index];
    return DataRow(
      // Check if this handler's ID is in the selectedHandlers list
      selected: selectedHandlers.any((element) => element.id == handler.id),
      cells: [DataCell(Text(handler.fullName)), DataCell(Text(handler.email)), DataCell(Text(handler.phoneNumber))],
      onSelectChanged: (isSelected) {
        onRowSelected(handler);
      },
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => handlers.length;

  @override
  int get selectedRowCount => selectedHandlers.length;
}
