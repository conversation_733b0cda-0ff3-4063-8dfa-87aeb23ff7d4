enum ReportType {
  accounts,
  contacts,
  accountsWithContacts,
  // Add new report types here
  // salesOrders,
  // invoices,
}

extension ReportTypeExtension on ReportType {
  String get title {
    switch (this) {
      case ReportType.accounts:
        return 'Accounts Report';
      case ReportType.contacts:
        return 'Contacts Report';
      case ReportType.accountsWithContacts:
        return 'Accounts & Contacts Association Report';
      // case ReportType.salesOrders: return 'Sales Orders Report';
      default:
        return '';
    }
  }

  String get filenamePrefix {
    switch (this) {
      case ReportType.accounts:
        return 'Accounts';
      case ReportType.contacts:
        return 'Contacts';
      case ReportType.accountsWithContacts:
        return 'Accounts_Contacts';
      // case ReportType.salesOrders: return 'SalesOrders';
      default:
        return '';
    }
  }
}
