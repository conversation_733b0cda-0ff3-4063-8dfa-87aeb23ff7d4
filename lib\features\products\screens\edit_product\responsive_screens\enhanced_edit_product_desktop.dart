import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import '../../../../../common/widgets/forms/form_section.dart';
import '../../../../../common/widgets/forms/enhanced_text_field.dart';
import '../../../../../common/widgets/forms/enhanced_dropdown.dart';
import '../../../../../common/widgets/forms/enhanced_image_upload.dart';
import '../../../../../common/widgets/forms/form_feedback.dart';
import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../routes/routes.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/validators/validation.dart';
import '../../../../category/controller/category_controller.dart';
import '../../../controller/product_controller/edit_product_controller.dart';
import '../../../models/product_model.dart';

class EnhancedEditProductDesktop extends StatelessWidget {
  const EnhancedEditProductDesktop({super.key, required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EditProductController(initialProduct: product));
    final categoryController = Get.find<CategoryController>();

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              TBreadcrumbsWithHeading(
                heading: 'Edit Product: ${product.name}',
                breadcrumbItems: [
                  TBreadcrumbItem(text: 'Products', route: TRoutes.products),
                  TBreadcrumbItem(text: 'Edit Product'),
                ],
                showBackButton: true,
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Loading overlay
              Obx(() => TFormLoadingOverlay(
                isLoading: controller.isLoading.value,
                loadingMessage: 'Updating product...',
                child: _buildEditForm(controller, categoryController),
              )),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEditForm(EditProductController controller, CategoryController categoryController) {
    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status warning for active products
            Obx(() {
              if (controller.status.value == ProductStatus.active) {
                return Column(
                  children: [
                    TFormFeedback(
                      type: FormFeedbackType.warning,
                      title: 'Active Product',
                      message: 'This product is currently active. Some fields cannot be modified to maintain data integrity.',
                      showIcon: true,
                    ),
                    const SizedBox(height: TSizes.spaceBtwSections),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),

            // Validation errors summary
            Obx(() {
              if (controller.formErrors.isNotEmpty) {
                return Column(
                  children: [
                    TFormValidationSummary(
                      errors: controller.formErrors,
                      title: 'Please fix the following errors:',
                    ),
                    const SizedBox(height: TSizes.spaceBtwSections),
                  ],
                );
              }
              return const SizedBox.shrink();
            }),

            // Basic Information Section
            TFormSection(
              title: 'Product Identity',
              icon: Iconsax.box,
              isRequired: true,
              children: [
                Obx(() => TEnhancedTextField(
                  controller: controller.name,
                  labelText: 'Product Name',
                  hintText: 'Enter a descriptive product name',
                  prefixIcon: Iconsax.box,
                  isRequired: true,
                  enabled: controller.status.value != ProductStatus.active,
                  validator: (value) => TValidator.validateEmptyText('Product Name', value),
                )),
                TEnhancedTextField(
                  controller: controller.description,
                  labelText: 'Description',
                  hintText: 'Describe the product features and benefits',
                  prefixIcon: Iconsax.note,
                  maxLines: 3,
                  helperText: 'Provide a detailed description to help customers understand the product',
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Classification Section
            TFormSection(
              title: 'Classification',
              icon: Iconsax.category,
              isRequired: true,
              children: [
                Obx(() => TEnhancedDropdown<String>(
                  labelText: 'Category',
                  hintText: 'Select product category',
                  prefixIcon: Iconsax.category,
                  isRequired: true,
                  enabled: controller.status.value != ProductStatus.active,
                  items: categoryController.allItems.map((cat) => cat.id).toList(),
                  value: controller.selectedCategoryId.value.isEmpty ? null : controller.selectedCategoryId.value,
                  onChanged: (value) {
                    if (value != null) controller.selectedCategoryId.value = value;
                  },
                  displayStringForOption: (id) {
                    final category = categoryController.allItems.firstWhereOrNull((cat) => cat.id == id);
                    return category?.name ?? 'Unknown Category';
                  },
                  validator: (value) => TValidator.validateEmptyText('Category', value),
                )),
                Obx(() => TEnhancedDropdown<ProductSegment>(
                  labelText: 'Segment',
                  hintText: 'Select product segment',
                  prefixIcon: Iconsax.tag,
                  isRequired: true,
                  enabled: controller.status.value != ProductStatus.active,
                  items: ProductSegment.values,
                  value: controller.selectedSegment.value,
                  onChanged: (value) {
                    if (value != null) controller.selectedSegment.value = value;
                  },
                  displayStringForOption: (segment) => segment.name.capitalizeFirst!,
                  validator: (value) => value == null ? 'Please select a segment' : null,
                )),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Specifications Section
            TFormSection(
              title: 'Dimensions',
              icon: Iconsax.ruler,
              isRequired: true,
              subtitle: 'Specify the product dimensions in millimeters',
              children: [
                TFormRow(
                  children: [
                    Obx(() => TEnhancedNumberField(
                      controller: controller.width,
                      labelText: 'Width',
                      hintText: '0.0',
                      prefixIcon: Iconsax.ruler,
                      suffixText: 'mm',
                      isRequired: true,
                      enabled: controller.status.value != ProductStatus.active,
                      allowDecimals: true,
                      min: 0.1,
                      max: 10000,
                    )),
                    Obx(() => TEnhancedNumberField(
                      controller: controller.height,
                      labelText: 'Height',
                      hintText: '0.0',
                      prefixIcon: Iconsax.ruler,
                      suffixText: 'mm',
                      isRequired: true,
                      enabled: controller.status.value != ProductStatus.active,
                      allowDecimals: true,
                      min: 0.1,
                      max: 10000,
                    )),
                  ],
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Media Section
            TFormSection(
              title: 'Product Images',
              icon: Iconsax.image,
              subtitle: 'Upload high-quality images to showcase your product',
              children: [
                Obx(() => TEnhancedImageUpload(
                  labelText: 'Product Thumbnail',
                  helperText: 'Upload a main product image (JPG, PNG, GIF - Max 5MB)',
                  initialImageUrl: controller.thumbnailPath.value.isNotEmpty ? controller.thumbnailPath.value : null,
                  onImageSelected: (file) => controller.pickAndUploadThumbnail(),
                  onImageRemoved: () => controller.thumbnailPath.value = '',
                  maxWidth: 200,
                  maxHeight: 200,
                )),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Status Section
            TFormSection(
              title: 'Product Status',
              icon: Iconsax.status,
              children: [
                Obx(() => TEnhancedDropdown<ProductStatus>(
                  labelText: 'Status',
                  hintText: 'Select product status',
                  prefixIcon: Iconsax.status,
                  isRequired: true,
                  items: ProductStatus.values,
                  value: controller.status.value,
                  onChanged: (value) {
                    if (value != null) controller.status.value = value;
                  },
                  displayStringForOption: (status) => status.name.capitalizeFirst!,
                  validator: (value) => value == null ? 'Please select a status' : null,
                )),
                if (controller.status.value == ProductStatus.active)
                  Padding(
                    padding: const EdgeInsets.only(top: TSizes.spaceBtwItems),
                    child: TFormFeedback(
                      type: FormFeedbackType.info,
                      message: 'Active products have restricted editing to maintain data integrity.',
                      showIcon: true,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Get.back(),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwItems),
                Expanded(
                  flex: 2,
                  child: Obx(() => ElevatedButton(
                    onPressed: controller.isLoading.value ? null : () => controller.updateProduct(),
                    child: controller.isLoading.value
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Update Product'),
                  )),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
