import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';

class ProfileForm extends StatelessWidget {
  const ProfileForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        TRoundedContainer(
          padding: const EdgeInsets.symmetric(vertical: TSizes.lg, horizontal: TSizes.md),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Profile Details', style: Theme.of(context).textTheme.headlineSmall),
              const SizedBox(height: TSizes.spaceBtwSections),

              // First and Last Name
              Form(
                child: Column(
                  children: [
                    Row(
                      children: [
                        // First Name
                        Expanded(
                          child: TextFormField(
                            decoration: const InputDecoration(
                              hintText: 'First Name',
                              label: Text('First Name'),
                              // Assuming prefixIcon based on the next field
                              // prefixIcon: Icon(Iconsax.user),
                            ),
                            // Assuming validator based on the next field
                            // validator: (value) => TValidator.validateEmptyText('First Name', value),
                          ),
                        ),
                        // const SizedBox(width: TSizes.spaceBtwInputFields), // This is likely missing in the screenshot but good practice

                        // Last Name
                        Expanded(
                          child: TextFormField(
                            decoration: const InputDecoration(
                              hintText: 'Last Name',
                              label: Text('Last Name'),
                              prefixIcon: Icon(Iconsax.user),
                            ), // InputDecoration
                            validator: (value) => TValidator.validateEmptyText('Last Name', value),
                          ), // TextFormField
                        ), // Expanded
                      ],
                    ), // Row
                    const SizedBox(height: TSizes.spaceBtwInputFields),

                    // Email and Phone
                    Row(
                      children: [
                        // Email
                        Expanded(
                          child: TextFormField(
                            decoration: const InputDecoration(
                              hintText: 'Email',
                              label: Text('Email'),
                              prefixIcon: Icon(Iconsax.forward),
                              enabled: false,
                            ), // InputDecoration
                          ), // TextFormField
                        ), // Expanded
                        const SizedBox(width: TSizes.spaceBtwItems),
                        // Phone Number
                        Expanded(
                          child: TextFormField(
                            decoration: const InputDecoration(
                              hintText: 'Phone Number',
                              label: Text('Phone Number'),
                              prefixIcon: Icon(Iconsax.mobile),
                            ),
                            validator: (value) => TValidator.validateEmptyText('Phone Number', value),
                          ), // TextFormField
                        ), // Expanded
                      ],
                    ), // Row
                  ],
                ), // Column
              ), // Form
              const SizedBox(height: TSizes.spaceBtwSections),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(onPressed: () {}, child: const Text('Update Profile')),
              ), // SizedBox
            ],
          ), // Column
        ), // TRoundedContainer
      ],
    ); // Column
  }
}
