import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/data_table/table_header.dart';
import 'package:alloy/features/products/controller/variant_controller/product_variant_controller.dart';
import 'package:alloy/features/products/models/product_model.dart';
import 'package:alloy/features/products/screens/product_variants/data_table/product_variants_table.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../utils/constants/colors.dart';
import '../widget/product_variant_creation_form.dart';

class ProductVariantsDesktop extends StatelessWidget {
  const ProductVariantsDesktop({super.key, required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    // Initialize ProductVariantController (for display table)
    final productVariantController = Get.put(ProductVariantController());
    // Initialize ProductVariantManagerController (for the creation form)

    // IMPORTANT: Load variants for the specific product when this screen is opened.
    // This ensures the table displays the correct variants.
    // Corrected: Pass product.id (String) as expected by ProductVariantController
    productVariantController.loadVariantsForProduct(product.id);

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Breadcrumbs
            TBreadcrumbsWithHeading(
              heading: 'Variants for ${product.name}',
              breadcrumbItems: [
                TBreadcrumbItem(text: 'Products', route: TRoutes.products),
                TBreadcrumbItem(text: product.name),
              ],
              showBackButton: true,
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Main Content Area: Variants Table (Left) and Creation Form (Right)
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start, // Align top
                children: [
                  // Left Column: Product Variants Table
                  Expanded(
                    flex: 3, // Adjust flex as needed
                    child: TRoundedContainer(
                      child: Column(
                        children: [
                          // Table Header (Search & Show Discontinued Toggle)
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                flex: 3,
                                child: TTableHeader(
                                  hintText: 'Search Variants',
                                  searchOnChanged: (query) => productVariantController.searchQuery(query),
                                ),
                              ),
                              const SizedBox(width: TSizes.spaceBtwItems),
                              Obx(
                                () => Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Switch(
                                      value: productVariantController.showDiscontinuedVariants.value,
                                      onChanged: (value) {
                                        productVariantController.showDiscontinuedVariants.value = value;
                                        // No need to explicitly call listenToStream() here,
                                        // as the streamItems in ProductVariantController is already reactive to showDiscontinuedVariants.
                                        // However, calling it ensures an immediate re-filter if the stream doesn't react instantly.
                                        productVariantController.listenToStream();
                                      },
                                      activeColor: TColors.primary,
                                    ),
                                    Text('Show Discontinued', style: Theme.of(context).textTheme.labelLarge),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: TSizes.spaceBtwItems),

                          // Product Variants Table
                          Expanded(
                            child: Obx(() {
                              if (productVariantController.isLoading.value)
                                return const Center(child: CircularProgressIndicator());
                              return ProductVariantsTable(parentProduct: product); // Pass parent product to table
                            }),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: TSizes.defaultSpace), // Space between columns
                  // Right Column: Product Variant Creation Form
                  Expanded(
                    flex: 2, // Adjust flex as needed
                    child: TRoundedContainer(
                      padding: const EdgeInsets.all(TSizes.defaultSpace), // Add padding to the form container
                      child: ProductVariantCreationForm(
                        product: product, // Pass the product to the form
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
