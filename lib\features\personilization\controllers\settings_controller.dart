import 'package:alloy/features/personilization/repository/settings_repository.dart';
import 'package:alloy/common/media/controller/media_controller.dart';
import 'package:alloy/common/media/models/image_model.dart';
import 'package:alloy/features/personilization/models/setting_model.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TSettingsController extends GetxController {
  static TSettingsController get instance => Get.find();

  // Observable variables
  RxBool loading = false.obs;
  Rx<SettingsModel> settings = SettingsModel().obs;

  final formKey = GlobalKey<FormState>();
  final appNameController = TextEditingController();
  final taxController = TextEditingController();
  final shippingController = TextEditingController();
  final freeShippingThresholdController = TextEditingController();

  // Dependencies
  final settingRepository = Get.put(SettingsRepository());

  @override
  void onInit() {
    // Fetch setting details on controller initialization
    fetchSettingDetails();
    super.onInit();
  }

  /// Fetches setting details from the repository
  Future<SettingsModel> fetchSettingDetails() async {
    try {
      loading.value = true;
      final settings = await settingRepository.getSettings();
      this.settings.value = settings;

      appNameController.text = settings.appName;
      taxController.text = settings.taxRate.toString();
      shippingController.text = settings.shippingCost.toString();
      freeShippingThresholdController.text = settings.freeShippingThreshold == null ? '' : settings.freeShippingThreshold.toString();

      loading.value = false;
      return settings;
    } catch (e) {
      loading.value = false;
      TLoaders.errorSnackBar(title: 'Something went wrong.', message: e.toString());
      return SettingsModel();
    }
  }

  /// Pick Thumbnail Image from Media
  void updateAppLogo() async {
    try {
      loading.value = true;
      final controller = Get.put(MediaController());
      List<ImageModel>? selectedImages = await controller.selectImagesFromMedia();

      // Handle the selected images
      if (selectedImages != null && selectedImages.isNotEmpty) {
        // Set the selected image to the main image or perform any other action
        ImageModel selectedImage = selectedImages.first;

        // Update Profile in Firestore
        await settingRepository.updateSingleField({'appLogo': selectedImage.url});

        // Update the main image using the selectedImage
        settings.value.appLogo = selectedImage.url;
        // Since appLogo is not observable we need to refresh it manually
        settings.refresh();

        TLoaders.successSnackBar(title: 'Congratulations', message: 'App Logo has been updated.');
      }
      loading.value = false;
    } catch (e) {
      loading.value = false;
      TLoaders.errorSnackBar(title: 'Oh Snap', message: e.toString());
    }
  }

  /// Update Settings
  void updateSettingInformation() async {
    try {
      print('CLICKED UPDATE SETTINGS');
      loading.value = true;

      // Form Validation
      if (!formKey.currentState!.validate()) {
        loading.value = false;
        return;
      }

      // Update Settings
      settings.value.appName = appNameController.text.trim();
      settings.value.taxRate = double.parse(taxController.text.trim());
      settings.value.shippingCost = double.parse(shippingController.text.trim());
      settings.value.freeShippingThreshold = double.parse(freeShippingThresholdController.text.trim());

      await settingRepository.updateSettingDetails(settings.value);
      settings.refresh();

      TLoaders.successSnackBar(title: 'Congratulations', message: 'Settings has been updated.');
      loading.value = false;
    } catch (e) {
      loading.value = false;
      TLoaders.errorSnackBar(title: 'Oh Snap', message: e.toString());
    }
  }
}
