import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/contact/screens/all_contacts/responsive_screens/contacts_desktop.dart';
import 'package:alloy/features/contact/screens/all_contacts/responsive_screens/contacts_mobile.dart';
import 'package:alloy/features/contact/screens/all_contacts/responsive_screens/contacts_tablet.dart';
import 'package:flutter/material.dart';

class ContactsScreen extends StatelessWidget {
  const ContactsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(desktop: ContactsDesktop(), tablet: ContactsTablet(), mobile: ContactsMobile());
  }
}
