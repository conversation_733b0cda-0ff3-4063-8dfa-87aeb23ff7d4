import 'package:alloy/common/layouts/headers/header.dart';
import 'package:alloy/common/layouts/sidebars/sidebar.dart';
import 'package:flutter/material.dart';

class DesktopLayout extends StatelessWidget {
  const DesktopLayout({super.key, this.body});
  final Widget? body;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          Expanded(child: TSideBar()),
          Expanded(
            flex: 5,
            child: Column(
              children: [
                // HEADER
                THeader(),
                // Body
                Expanded(child: body ?? SizedBox()),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
