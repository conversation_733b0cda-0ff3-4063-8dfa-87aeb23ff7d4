import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../common/layouts/templates/site_template.dart';
import '../../controllers/deal_controller.dart';
import '../../models/deal_model.dart';
import 'responsive_screens/edit_deal_desktop.dart';
import 'responsive_screens/edit_deal_mobile.dart';

class EditDealScreen extends StatelessWidget {
  const EditDealScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the deal from arguments
    final DealModel deal = Get.arguments as DealModel;

    // Initialize controller with the deal data
    final controller = Get.put(DealController());
    controller.initializeEditMode(deal);

    return TSiteTemplate(
      useLayout: false,
      desktop: EditDealDesktop(deal: deal),
      mobile: EditDealMobile(deal: deal),
    );
  }
}
