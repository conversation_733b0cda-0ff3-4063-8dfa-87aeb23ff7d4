import 'package:alloy/common/widgets/loaders/animation_loader.dart';
import 'package:alloy/common/widgets/loaders/loader_animation.dart';
import 'package:alloy/common/media/models/image_model.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/images/t_rounded_image.dart';
import 'package:alloy/common/media/controller/media_controller.dart';
import 'package:alloy/common/media/widgets/folder_dropdown.dart';
import 'package:alloy/common/media/widgets/image_popup.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/sizes.dart';

class MediaContent extends StatelessWidget {
  MediaContent({super.key, required this.allowSelection, required this.allowMultipleSelection, this.alreadySelectedUrls, this.onImageSelected});

  final bool allowSelection;
  final bool allowMultipleSelection;
  final List<String>? alreadySelectedUrls;
  final List<ImageModel> selectedImages = [];
  final void Function(List<ImageModel>)? onImageSelected;

  @override
  Widget build(BuildContext context) {
    // Flag to check if the previous selection has been loaded to prevent UI rebuild everytime a checkbox is click
    bool loadedPreviousSelection = false;
    final controller = MediaController.instance;
    return TRoundedContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// Media Images Header
          SizedBox(height: TSizes.spaceBtwSections),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  /// Folder Dropdown
                  Text('Select Folder', style: Theme.of(context).textTheme.headlineSmall),
                  const SizedBox(width: TSizes.spaceBtwItems),

                  // Folder List
                  MediaFolderDropdown(
                    onChanged: (MediaCategory? newValue) {
                      if (newValue != null) {
                        controller.selectedPath.value = newValue;
                        controller.getMediaImages();
                      }
                    },
                  ),
                ],
              ),

              // Show Selected Image Button
              if (allowSelection) buildAddSelectedImagesButton(),
            ],
          ),
          SizedBox(height: TSizes.spaceBtwSections),

          /// Media Content Section
          Obx(() {
            // Get Selected Folder Media Images
            List<ImageModel> images = _getSelectedFolderImages(controller);

            // if the selection is already loaded then dont run this loop. To prevent UI rebuild looping
            if (!loadedPreviousSelection) {
              // Pre-select or De-select images in the UI (on the checkboxs) if already selected urls are provided
              if (alreadySelectedUrls != null && alreadySelectedUrls!.isNotEmpty) {
                final selectedUrlsSet = Set<String>.from(alreadySelectedUrls!);

                for (var image in images) {
                  // Check is the image already apprearing the selected images list
                  image.isSelected.value = selectedUrlsSet.contains(image.url);
                  if (image.isSelected.value) {
                    selectedImages.add(image);
                  }
                }
              } else {
                // If not already selected, remove it from the for checkboxes
                for (var image in images) {
                  image.isSelected.value = false;
                }
              }
              loadedPreviousSelection = true;
            }

            // Loader
            if (controller.loading.value && images.isEmpty) {
              return const TLoaderAnimation();
            }

            // Empty Widget
            if (images.isEmpty) return _buildEmptyAnimationWidget(context);

            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Wrap(
                  alignment: WrapAlignment.start,
                  spacing: TSizes.spaceBtwItems / 2,
                  runSpacing: TSizes.spaceBtwItems / 2,
                  children: images
                      .map(
                        (image) => InkWell(
                          onTap: () => Get.dialog(ImagePopup(image: image)),
                          child: SizedBox(
                            width: 140,
                            height: 180,
                            child: Column(
                              children: [
                                // if selection allowed then show image with checkbox
                                allowSelection ? _buildImageWithCheckbox(image) : _buildSimpleImage(image),

                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(horizontal: TSizes.sm),
                                    child: Text(image.filename, maxLines: 1, overflow: TextOverflow.ellipsis),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),

                /// Load More Media Button
                if (!controller.loading.value)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: TSizes.spaceBtwSections),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: TSizes.buttonWidth,
                          child: ElevatedButton.icon(
                            onPressed: () => controller.loadMoreImages(),
                            label: const Text('Load More'),
                            icon: const Icon(Iconsax.arrow_down),
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            );
          }),
        ],
      ),
    );
  }

  List<ImageModel> _getSelectedFolderImages(MediaController controller) {
    List<ImageModel> images = [];
    if (controller.selectedPath.value == MediaCategory.banners) {
      images = controller.allBannerImages.where((image) => image.url.isNotEmpty).toList();
    } else if (controller.selectedPath.value == MediaCategory.brands) {
      images = controller.allBrandImages.where((image) => image.url.isNotEmpty).toList();
    } else if (controller.selectedPath.value == MediaCategory.categories) {
      images = controller.allCategoryImages.where((image) => image.url.isNotEmpty).toList();
    } else if (controller.selectedPath.value == MediaCategory.products) {
      images = controller.allProductImages.where((image) => image.url.isNotEmpty).toList();
    } else if (controller.selectedPath.value == MediaCategory.users) {
      images = controller.allUserImages.where((image) => image.url.isNotEmpty).toList();
    }

    return images;
  }

  Widget _buildEmptyAnimationWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: TSizes.lg * 3),
      child: TAnimationLoaderWidget(
        width: 300,
        height: 300,
        text: 'Select your Desired Folder',
        animation: TImages.packageAnimation,
        style: Theme.of(context).textTheme.headlineMedium,
      ),
    );
  }

  Widget _buildSimpleImage(ImageModel image) {
    return TRoundedImage(
      width: 140,
      height: 140,
      padding: TSizes.sm,
      image: image.url,
      imageType: ImageType.network,
      margin: TSizes.spaceBtwItems / 2,
      backgroundColor: Colors.transparent,
    );
  }

  Widget _buildImageWithCheckbox(ImageModel image) {
    return Stack(
      children: [
        _buildSimpleImage(image),
        Positioned(
          top: TSizes.md,
          right: TSizes.md,
          child: Obx(
            () => Checkbox(
              value: image.isSelected.value,
              onChanged: (selected) {
                if (selected != null) {
                  image.isSelected.value = selected;
                  if (selected) {
                    if (!allowMultipleSelection) {
                      // If not allowing multiple selection, uncheck other checkboxes
                      for (var otherImage in selectedImages) {
                        // clear all checkboxes except the current one thats being selected now
                        // like a toggle. Select the current one and uncheck the other one/s
                        if (otherImage != image) {
                          otherImage.isSelected.value = false;
                        }
                      }
                      selectedImages.clear();
                    }
                    selectedImages.add(image);
                  } else {
                    selectedImages.remove(image);
                  }
                }
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget buildAddSelectedImagesButton() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Close Button
        SizedBox(
          width: TSizes.buttonWidth,
          child: OutlinedButton.icon(onPressed: () => Get.back(), label: const Text('Close'), icon: const Icon(Iconsax.close_circle)),
        ),
        const SizedBox(width: TSizes.spaceBtwItems),

        // Add Selected Images Button
        SizedBox(
          width: TSizes.buttonWidth,
          child: ElevatedButton.icon(
            onPressed: () => Get.back(result: selectedImages),

            label: const Text('Add Selected'),
            icon: const Icon(Iconsax.image),
          ),
        ),
      ],
    );
  }
}
