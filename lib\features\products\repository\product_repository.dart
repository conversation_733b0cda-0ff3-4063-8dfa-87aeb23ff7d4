import 'package:alloy/features/products/models/product_model.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:alloy/utils/exceptions/firebase_exceptions.dart';
import 'package:alloy/utils/exceptions/platform_exceptions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// Repository for managing product data in Firestore.
class ProductRepository extends GetxService {
  static ProductRepository get instance => Get.find(); // Singleton instance

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Collection reference with converter for ProductModel
  final CollectionReference<ProductModel> _productsCollection = FirebaseFirestore.instance
      .collection(TTexts.products) // Corrected collection name if it was TTexts.products
      .withConverter<ProductModel>(
        fromFirestore: (snapshot, options) => ProductModel.fromSnapshot(snapshot), // Use fromSnapshot
        toFirestore: (product, options) => product.toJson(),
      );

  /// Upload a single product to Firestore.
  Future<String> createProduct(ProductModel product) async {
    try {
      final docRef = await _productsCollection.add(product); // Pass the model directly
      return docRef.id; // Return the generated document ID
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again: $e';
    }
  }

  /// Fetch all products.
  Future<List<ProductModel>> getAllProducts() async {
    try {
      final snapshot = await _productsCollection.get();
      return snapshot.docs.map((doc) => doc.data()).toList(); // doc.data() now returns ProductModel
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again: $e';
    }
  }

  /// Stream all products with optional filters.
  /// [filters]: A map where keys are field names and values are the filter values.
  Stream<List<ProductModel>> streamAllProducts({Map<String, dynamic>? filters}) {
    try {
      Query<ProductModel> query = _productsCollection;

      // Apply filters
      filters?.forEach((key, value) {
        // Handle specific enum filters by their string name
        if (key == 'segment' || key == 'status') {
          query = query.where(key, isEqualTo: value);
        } else if (key == 'categoryId') {
          query = query.where(key, isEqualTo: value);
        }
        // Add more filter conditions as needed (e.g., range queries, array-contains)
      });

      // Corrected: document.data() already returns ProductModel when using withConverter
      return query.snapshots().map((snapshot) => snapshot.docs.map((document) => document.data()).toList());
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again: $e';
    }
  }

  /// Update product data in Firestore.
  Future<void> updateProduct(ProductModel product) async {
    try {
      await _productsCollection.doc(product.id).set(product); // Use set for full replacement or update
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again: $e';
    }
  }

  /// Delete a product from Firestore.
  Future<void> deleteProduct(String productId) async {
    try {
      final WriteBatch batch = _db.batch();

      // 1. Delete the main product document
      batch.delete(_db.collection(TTexts.products).doc(productId));

      // 2. Query and delete all associated variants
      final variantsSnapshot = await _db.collection(TTexts.productVariants).where('productId', isEqualTo: productId).get();
      for (final doc in variantsSnapshot.docs) {
        batch.delete(doc.reference);
      }

      // 3. Commit the batch
      await batch.commit();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'An unexpected error occurred while deleting the product and its variants: $e';
    }
  }

  /// Fetches a single product by its name.
  Future<ProductModel?> getProductByName(String name) async {
    try {
      final querySnapshot = await _productsCollection
          .where('name', isEqualTo: name) // Use 'name' as per your toJson and model
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return querySnapshot.docs.first.data(); // doc.data() now returns ProductModel
      }
      return null;
    } on FirebaseException catch (e) {
      print('Firebase Exception in getProductByName: ${e.message}');
      return null;
    } on PlatformException catch (e) {
      print('Platform Exception in getProductByName: ${e.message}');
      return null;
    } catch (e) {
      print('Something went wrong in getProductByName: $e');
      return null;
    }
  }

  /// Fetches a single product by its ID.
  Future<ProductModel?> getProductById(String id) async {
    try {
      final docSnapshot = await _productsCollection.doc(id).get();
      if (docSnapshot.exists) {
        return docSnapshot.data();
      }
      return null;
    } on FirebaseException catch (e) {
      print('Firebase Exception in getProductById: ${e.message}');
      return null;
    } on PlatformException catch (e) {
      print('Platform Exception in getProductById: ${e.message}');
      return null;
    } catch (e) {
      print('Something went wrong in getProductById: $e');
      return null;
    }
  }
}
