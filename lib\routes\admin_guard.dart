import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../utils/constants/enums.dart';

class AdminGuard extends GetMiddleware {
  // Giving a higher priority (e.g., 2) ensures it runs after the AuthGuard (default priority 1).
  @override
  int? get priority => 2;

  @override
  RouteSettings? redirect(String? route) {
    final userController = UserController.instance;

    if (!userController.user.value.roles.contains(UserRole.Admin)) {
      print("$AdminGuard : ${userController.user.value.roles.map((e) => e.name).join(', ')}"); // Log all roles
      TLoaders.warningSnackBar(title: 'Access Denied', message: 'You do not have permission to access this page.');
      return const RouteSettings(name: TRoutes.profile);
    }
    return null;
  }
}
