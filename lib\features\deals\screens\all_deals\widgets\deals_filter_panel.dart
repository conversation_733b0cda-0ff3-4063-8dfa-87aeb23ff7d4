import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_controller.dart';
import '../../../models/deal_model.dart';

class DealsFilterPanel extends StatelessWidget {
  const DealsFilterPanel({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealController>();
    final isDark = THelperFunctions.isDarkMode(context);

    return TRoundedContainer(
      backgroundColor: isDark ? TColors.dark : TColors.light,
      padding: const EdgeInsets.all(TSizes.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter Header
          Row(
            children: [
              const Icon(Iconsax.filter, size: 20),
              const SizedBox(width: TSizes.spaceBtwItems / 2),
              Text(
                'Filters',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => controller.clearAllFilters(),
                child: const Text('Clear All'),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Filter Options
          Wrap(
            spacing: TSizes.spaceBtwItems,
            runSpacing: TSizes.spaceBtwItems / 2,
            children: [
              // Status Filter
              Obx(() => _buildStatusFilter(context, controller)),
              
              // Client Filter
              Obx(() => _buildClientFilter(context, controller)),
              
              // Show Locked Deals Toggle
              Obx(() => _buildToggleFilter(
                context,
                'Show Locked Deals',
                controller.showLockedDeals.value,
                (value) => controller.showLockedDeals.value = value,
              )),
              
              // Show Only My Deals Toggle
              Obx(() => _buildToggleFilter(
                context,
                'My Deals Only',
                controller.showOnlyMyDeals.value,
                (value) => controller.showOnlyMyDeals.value = value,
              )),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilter(BuildContext context, DealController controller) {
    return Container(
      constraints: const BoxConstraints(minWidth: 150),
      child: DropdownButtonFormField<DealStatus?>(
        value: controller.selectedStatusFilter.value,
        decoration: const InputDecoration(
          labelText: 'Status',
          prefixIcon: Icon(Iconsax.status),
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: TSizes.md, vertical: TSizes.sm),
        ),
        items: [
          const DropdownMenuItem<DealStatus?>(
            value: null,
            child: Text('All Statuses'),
          ),
          ...DealStatus.values.map((status) => DropdownMenuItem<DealStatus?>(
            value: status,
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: _getStatusColor(status),
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwItems / 2),
                Text(status.name),
              ],
            ),
          )),
        ],
        onChanged: (value) => controller.selectedStatusFilter.value = value,
      ),
    );
  }

  Widget _buildClientFilter(BuildContext context, DealController controller) {
    return Container(
      constraints: const BoxConstraints(minWidth: 200),
      child: TextFormField(
        decoration: const InputDecoration(
          labelText: 'Filter by Client',
          prefixIcon: Icon(Iconsax.building),
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: TSizes.md, vertical: TSizes.sm),
        ),
        onChanged: (value) => controller.selectedClientFilter.value = value,
      ),
    );
  }

  Widget _buildToggleFilter(
    BuildContext context,
    String label,
    bool value,
    Function(bool) onChanged,
  ) {
    return Container(
      constraints: const BoxConstraints(minWidth: 150),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: TColors.primary,
          ),
          const SizedBox(width: TSizes.spaceBtwItems / 2),
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.Draft:
        return Colors.grey;
      case DealStatus.PendingApproval:
        return Colors.orange;
      case DealStatus.Approved:
        return Colors.green;
      case DealStatus.Rejected:
        return Colors.red;
      case DealStatus.UnlockRequested:
        return Colors.purple;
      case DealStatus.Superseded:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }
}
