import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_controller.dart';
import '../../../models/deal_model.dart';

class DealsFilterPanel extends StatelessWidget {
  const DealsFilterPanel({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealController>();
    final isDark = THelperFunctions.isDarkMode(context);

    return TRoundedContainer(
      backgroundColor: isDark ? TColors.dark : TColors.light,
      padding: const EdgeInsets.all(TSizes.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter Header
          Row(
            children: [
              const Icon(Iconsax.filter, size: 20),
              const SizedBox(width: TSizes.spaceBtwItems / 2),
              Text('Filters', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
              const Spacer(),
              TextButton(onPressed: () => controller.clearAllFilters(), child: const Text('Clear All')),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Filter Options
          Wrap(
            spacing: TSizes.spaceBtwItems,
            runSpacing: TSizes.spaceBtwItems / 2,
            children: [
              // Status Filter
              Obx(() => _buildStatusFilter(context, controller)),

              // Client Filter
              _buildClientFilter(context, controller),

              // Show Locked Deals Toggle
              Obx(() => _buildLockedDealsToggle(context, controller)),

              // Show Only My Deals Toggle
              Obx(() => _buildMyDealsToggle(context, controller)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusFilter(BuildContext context, DealController controller) {
    return Container(
      constraints: const BoxConstraints(minWidth: 150),
      child: DropdownButtonFormField<DealStatus?>(
        value: controller.selectedStatusFilter.value,
        decoration: const InputDecoration(
          labelText: 'Status',
          prefixIcon: Icon(Iconsax.status),
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: TSizes.md, vertical: TSizes.sm),
        ),
        items: [
          const DropdownMenuItem<DealStatus?>(value: null, child: Text('All Statuses')),
          ...DealStatus.values.map(
            (status) => DropdownMenuItem<DealStatus?>(
              value: status,
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(color: _getStatusColor(status), shape: BoxShape.circle),
                  ),
                  const SizedBox(width: TSizes.spaceBtwItems / 2),
                  Text(status.name),
                ],
              ),
            ),
          ),
        ],
        onChanged: (value) => controller.selectedStatusFilter.value = value,
      ),
    );
  }

  Widget _buildClientFilter(BuildContext context, DealController controller) {
    return Container(
      constraints: const BoxConstraints(minWidth: 200),
      child: TextFormField(
        decoration: const InputDecoration(
          labelText: 'Filter by Client',
          prefixIcon: Icon(Iconsax.building),
          border: OutlineInputBorder(),
          contentPadding: EdgeInsets.symmetric(horizontal: TSizes.md, vertical: TSizes.sm),
        ),
        onChanged: (value) => controller.selectedClientFilter.value = value,
      ),
    );
  }

  Widget _buildLockedDealsToggle(BuildContext context, DealController controller) {
    return Container(
      constraints: const BoxConstraints(minWidth: 150),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Switch(
            value: controller.showLockedDeals.value,
            onChanged: (value) => controller.showLockedDeals.value = value,
            activeColor: TColors.primary,
          ),
          const SizedBox(width: TSizes.spaceBtwItems / 2),
          Text('Show Locked Deals', style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }

  Widget _buildMyDealsToggle(BuildContext context, DealController controller) {
    return Container(
      constraints: const BoxConstraints(minWidth: 150),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Switch(
            value: controller.showOnlyMyDeals.value,
            onChanged: (value) => controller.showOnlyMyDeals.value = value,
            activeColor: TColors.primary,
          ),
          const SizedBox(width: TSizes.spaceBtwItems / 2),
          Text('My Deals Only', style: Theme.of(context).textTheme.bodyMedium),
        ],
      ),
    );
  }

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Colors.grey;
      case DealStatus.pendingApproval:
        return Colors.orange;
      case DealStatus.approved:
        return Colors.green;
      case DealStatus.rejected:
        return Colors.red;
      case DealStatus.unlockRequested:
        return Colors.purple;
      case DealStatus.superseded:
        return Colors.brown;
      case DealStatus.clientApproved:
        return Colors.teal;
      case DealStatus.clientDeclined:
        return Colors.red.shade700;
      case DealStatus.closed:
        return Colors.blue;
      case DealStatus.quotationGenerated:
        return Colors.indigo;
    }
  }
}
