import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/data_table/table_header.dart';
import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/features/category/screens/all_categories/data_table/category_table.dart';
import 'package:alloy/features/category/screens/all_categories/widgets/add_category_dialog.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CategoryDesktop extends StatelessWidget {
  const CategoryDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CategoryController());
    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Breadcrumbs
            const TBreadcrumbsWithHeading(
              heading: 'Categories Management',
              breadcrumbItems: [TBreadcrumbItem(text: 'Categories')],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Table Body
            Expanded(
              child: TRoundedContainer(
                child: Column(
                  children: [
                    // Table Header
                    TTableHeader(
                      hintText: 'Search Categories',
                      searchOnChanged: (query) => controller.searchQuery(query),
                      actions: [ElevatedButton(onPressed: () => AddCategoryDialog.show(context), child: const Text('Create New Category'))],
                    ),

                    const SizedBox(height: TSizes.spaceBtwItems),

                    // Table
                    Expanded(
                      child: Obx(() {
                        if (controller.isLoading.value) return const Center(child: CircularProgressIndicator());
                        return const CategoryTable();
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
