import 'dart:io';

import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/features/products/controller/product_controller/create_product_controller.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class CreateProductDesktop extends StatelessWidget {
  const CreateProductDesktop({super.key});
  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CreateProductController()); // Initialize the controller
    final categoryController = Get.find<CategoryController>(); // Get the CategoryController

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Form(
            key: controller.formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Breadcrumbs
                const TBreadcrumbsWithHeading(
                  heading: 'Create New Product Template', // Updated heading
                  breadcrumbItems: [
                    TBreadcrumbItem(text: 'Products', route: TRoutes.products),
                    TBreadcrumbItem(text: 'Create New'),
                  ],
                  showBackButton: true,
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Product Template Form Section
                TRoundedContainer(
                  padding: const EdgeInsets.all(TSizes.defaultSpace),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Product Details', style: Theme.of(context).textTheme.headlineSmall),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Product Name
                      TextFormField(
                        controller: controller.name,
                        validator: (value) => TValidator.validateEmptyText('Product Name', value),
                        decoration: const InputDecoration(labelText: 'Product Name'),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Description
                      TextFormField(
                        controller: controller.description,
                        maxLines: 3,
                        decoration: const InputDecoration(labelText: 'Description'),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Category Dropdown
                      Obx(
                        () => DropdownButtonFormField<String>(
                          decoration: const InputDecoration(labelText: 'Category'),
                          value: controller.selectedCategoryId.value.isEmpty
                              ? null
                              : controller.selectedCategoryId.value,
                          items: categoryController.allItems
                              .map((category) => DropdownMenuItem(value: category.id, child: Text(category.name)))
                              .toList(),
                          onChanged: (value) {
                            if (value != null) controller.selectedCategoryId.value = value;
                          },
                          validator: (value) => TValidator.validateEmptyText('Category', value),
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Segment Dropdown
                      Obx(
                        () => DropdownButtonFormField<ProductSegment>(
                          decoration: const InputDecoration(labelText: 'Segment'),
                          value: controller.selectedSegment.value,
                          items: ProductSegment.values
                              .map(
                                (segment) =>
                                    DropdownMenuItem(value: segment, child: Text(segment.name.capitalizeFirst!)),
                              ) // Use capitalizeFirst! for display
                              .toList(),
                          onChanged: (value) {
                            if (value != null) controller.selectedSegment.value = value;
                          },
                          validator: (value) =>
                              TValidator.validateEmptyText('Segment', value?.name), // Validate enum value
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Dimensions (Width x Height)
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: controller.width,
                              validator: (value) => TValidator.validateEmptyText(value, 'Width'), // Validate as number
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              decoration: const InputDecoration(labelText: 'Width (mm)'),
                            ),
                          ),
                          const SizedBox(width: TSizes.spaceBtwInputFields),
                          Expanded(
                            child: TextFormField(
                              controller: controller.height,
                              validator: (value) => TValidator.validateEmptyText(value, 'Height'), // Validate as number
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              decoration: const InputDecoration(labelText: 'Height (mm)'),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Thumbnail Upload Section
                      Text('Product Thumbnail (Optional)', style: Theme.of(context).textTheme.titleMedium),
                      const SizedBox(height: TSizes.spaceBtwItems),
                      Obx(
                        () => Row(
                          children: [
                            // Display selected image or placeholder
                            TRoundedContainer(
                              width: 100,
                              height: 100,
                              //color: Colors.grey.shade200,
                              child: controller.thumbnailPath.value.isNotEmpty
                                  ? (kIsWeb // Check if running on web
                                        ? Image.network(
                                            controller.thumbnailPath.value, // Use Image.network for web
                                            fit: BoxFit.cover,
                                            errorBuilder: (context, error, stackTrace) =>
                                                const Icon(Icons.broken_image, size: 50, color: Colors.grey),
                                          )
                                        : Image.file(
                                            File(controller.thumbnailPath.value), // Use Image.file for other platforms
                                            fit: BoxFit.cover,
                                            errorBuilder: (context, error, stackTrace) =>
                                                const Icon(Icons.broken_image, size: 50, color: Colors.grey),
                                          ))
                                  : const Icon(Iconsax.image, size: 50, color: Colors.grey),
                            ),
                            const SizedBox(width: TSizes.spaceBtwItems),
                            // Upload Button
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () => controller.pickAndUploadThumbnail(),
                                child: const Text('Upload Thumbnail'),
                              ),
                            ),
                            // Clear Button (optional, if you want to allow removing thumbnail)
                            if (controller.thumbnailPath.value.isNotEmpty) ...[
                              const SizedBox(width: TSizes.spaceBtwItems),
                              IconButton(
                                icon: const Icon(Iconsax.close_circle, color: Colors.red),
                                onPressed: () => controller.thumbnailPath.value = '', // Clear the path
                                tooltip: 'Clear Thumbnail',
                              ),
                            ],
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Action Button
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    SizedBox(
                      width: 200,
                      child: ElevatedButton(
                        onPressed: () => controller.createProduct(),
                        child: const Text('Create Product Template'), // Updated button text
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
