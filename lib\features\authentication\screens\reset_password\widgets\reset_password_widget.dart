import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ResetPasswordWidget extends StatelessWidget {
  ResetPasswordWidget({super.key});
  final email = Get.parameters['email'] ?? '';
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        /// Header
        Row(
          children: [IconButton(onPressed: () => Get.offAllNamed(TRoutes.login), icon: Icon(CupertinoIcons.clear))],
        ),
        SizedBox(height: TSizes.spaceBtwItems),

        /// Image
        Image(image: AssetImage(TImages.deliveredEmailIllustration), height: 300, width: 300),
        SizedBox(height: TSizes.spaceBtwItems),

        /// Title and Subtitle
        Text(TTexts.changeYourPasswordTitle, style: Theme.of(context).textTheme.headlineMedium),
        SizedBox(height: TSizes.spaceBtwItems),
        Text(email, style: Theme.of(context).textTheme.labelLarge),
        SizedBox(height: TSizes.spaceBtwItems),

        Text(TTexts.changeYourPasswordSubTitle, style: Theme.of(context).textTheme.labelMedium),
        SizedBox(height: TSizes.spaceBtwSections),

        /// Buttons
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(onPressed: () => Get.offAllNamed(TRoutes.login), child: Text(TTexts.done)),
        ),
        SizedBox(height: TSizes.spaceBtwItems),
        SizedBox(width: double.infinity),
        OutlinedButton(onPressed: () {}, child: Text(TTexts.resendEmail)),
      ],
    );
  }
}
