import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../utils/constants/sizes.dart'; // TSizes
import '../../../../utils/validators/validation.dart'; // TValidator
import '../../../../common/widgets/containers/rounded_container.dart'; // TRoundedContainer
import '../controller/add_edit_address_controller.dart';
import '../models/address_model.dart'; // AddressModel

class AddEditAddressPopup extends StatelessWidget {
  const AddEditAddressPopup({
    super.key,
    this.initialAddress, // Optional: Pass an address to edit
    required this.onSave, // Callback when address is saved/updated
    required this.accountId, // The ID of the account this address belongs to
    required this.createdByUserId, // The ID of the user creating/editing
  });

  final AddressModel? initialAddress;
  final Function(AddressModel) onSave;
  final String accountId;
  final String createdByUserId;

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AddEditAddressController());
    controller.initializeForm(initialAddress); // Initialize form with data or clear

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(TSizes.cardRadiusLg)),
      child: TRoundedContainer(
        width: 600, // Fixed width for the popup
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: SingleChildScrollView(
          child: Form(
            key: controller.formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      initialAddress == null ? 'Add New Address' : 'Edit Address',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    IconButton(onPressed: () => Get.back(), icon: const Icon(Iconsax.close_circle)),
                  ],
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Address Line 1
                TextFormField(
                  controller: controller.addressLine1,
                  decoration: const InputDecoration(labelText: 'Address Line 1', prefixIcon: Icon(Iconsax.location)),
                  validator: (value) => TValidator.validateEmptyText('Address Line 1', value),
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // Address Line 2 (Optional)
                TextFormField(
                  controller: controller.addressLine2,
                  decoration: const InputDecoration(
                    labelText: 'Address Line 2 (Optional)',
                    prefixIcon: Icon(Iconsax.location),
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // Street (Optional)
                TextFormField(
                  controller: controller.street,
                  decoration: const InputDecoration(labelText: 'Street (Optional)', prefixIcon: Icon(Iconsax.path)),
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // City
                TextFormField(
                  controller: controller.city,
                  decoration: const InputDecoration(labelText: 'City', prefixIcon: Icon(Iconsax.building)),
                  validator: (value) => TValidator.validateEmptyText('City', value),
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // State / Emirates (Optional)
                TextFormField(
                  controller: controller.state,
                  decoration: const InputDecoration(
                    labelText: 'State/Emirates (Optional)',
                    prefixIcon: Icon(Iconsax.map),
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // Zip Code (Optional)
                TextFormField(
                  controller: controller.zipCode,
                  decoration: const InputDecoration(labelText: 'Zip Code (Optional)', prefixIcon: Icon(Iconsax.code)),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // Country
                TextFormField(
                  controller: controller.country,
                  decoration: const InputDecoration(labelText: 'Country', prefixIcon: Icon(Iconsax.global)),
                  validator: (value) => TValidator.validateEmptyText('Country', value),
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // Address Type (Dropdown)
                Obx(
                  () => DropdownButtonFormField<String>(
                    value: controller.type.value,
                    decoration: const InputDecoration(labelText: 'Address Type', prefixIcon: Icon(Iconsax.category)),
                    items: [
                      'Shipping',
                      'Billing',
                      'Branch Office',
                      'Warehouse',
                    ].map((type) => DropdownMenuItem(value: type, child: Text(type))).toList(),
                    onChanged: (value) {
                      if (value != null) controller.type.value = value;
                    },
                    validator: (value) => TValidator.validateEmptyText('Address Type', value),
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // Is Default Checkbox
                Obx(
                  () => CheckboxListTile(
                    title: const Text('Set as Default Address for this Type'),
                    value: controller.isDefault.value,
                    onChanged: (value) {
                      controller.isDefault.value = value ?? false;
                    },
                    controlAffinity: ListTileControlAffinity.leading,
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),

                // Notes (Optional)
                TextFormField(
                  controller: controller.notes,
                  maxLines: 3,
                  decoration: const InputDecoration(
                    labelText: 'Notes/Instructions (Optional)',
                    prefixIcon: Icon(Iconsax.note),
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Save Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      final address = controller.validateAndSaveAddress(
                        accountId: accountId,
                        createdByUserId: createdByUserId, // Pass current user ID
                      );
                      if (address != null) {
                        onSave(address); // Call the callback with the new/updated address
                        Get.back(); // Close the popup
                      }
                    },
                    child: Text(initialAddress == null ? 'Add Address' : 'Save Changes'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
