import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import 'package:alloy/common/widgets/chips/rounded_choice_chips.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/images/image_uploader.dart';
import 'package:alloy/features/brand/controller/create_brand_controller.dart';
import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/utils/constants/enums.dart';

class CreateBrandForm extends StatelessWidget {
  const CreateBrandForm({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CreateBrandController());
    final isMobile = TDeviceUtils.isMobileScreen(context);
    return TRoundedContainer(
      width: 500,
      padding: EdgeInsets.all(TSizes.defaultSpace),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Heading
            SizedBox(height: TSizes.sm),

            // Band Name
            Text('Create New Brand', style: Theme.of(context).textTheme.headlineMedium),
            SizedBox(height: TSizes.spaceBtwSections),

            // Name Text Field
            TextFormField(
              controller: controller.nameController,
              validator: (value) => TValidator.validateEmptyText('Name', value),
              decoration: InputDecoration(labelText: 'Brand Name', prefixIcon: Icon(Iconsax.box)),
            ),

            SizedBox(height: TSizes.spaceBtwInputFields),

            // Categories
            Text('Select Categories', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: TSizes.spaceBtwInputFields / 2),
            Obx(
              () => Wrap(
                spacing: TSizes.xs,
                direction: isMobile ? Axis.vertical : Axis.horizontal,
                children: CategoryController.instance.allItems.map((category) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: isMobile ? 0 : TSizes.sm),
                    child: TChoiceChip(
                      color: Colors.blue,
                      text: category.name,
                      selected: controller.selectedCategories.contains(category), // Check if the category is already selected
                      onSelected: (value) => controller.toggleSelection(category),
                    ),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields * 2),
            Obx(
              () => TImageUploader(
                imageType: controller.imageUrl.value.isEmpty ? ImageType.asset : ImageType.network,
                image: controller.imageUrl.value.isEmpty ? TImages.defaultImage : controller.imageUrl.value,
                width: 80,
                height: 80,
                onIconButtonPressed: () => controller.pickImage(),
              ),
            ),

            const SizedBox(height: TSizes.spaceBtwInputFields * 2),

            Obx(
              () => CheckboxMenuButton(
                value: controller.isFeatured.value,
                onChanged: (value) => controller.isFeatured.value = value ?? false,
                child: const Text('Featured'),
              ),
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields * 2),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(onPressed: () => controller.createBrand(), child: const Text('Create Brand')),
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields * 2),
          ],
        ),
      ),
    );
  }
}
