import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/validators/validation.dart';
import '../../../models/product_model.dart';

class CopyProductDialog extends StatefulWidget {
  const CopyProductDialog({super.key, required this.originalProduct, required this.onCopy});

  final ProductModel originalProduct;
  final Function(String newName, double? newWidth, double? newHeight) onCopy;

  @override
  State<CopyProductDialog> createState() => _CopyProductDialogState();
}

class _CopyProductDialogState extends State<CopyProductDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController newNameController;
  late TextEditingController newWidthController;
  late TextEditingController newHeightController;
  bool _changeDimensions = false; // To toggle dimension input fields

  @override
  void initState() {
    super.initState();
    newNameController = TextEditingController(text: '${widget.originalProduct.name} (Copy)');
    newWidthController = TextEditingController(text: widget.originalProduct.width.toString());
    newHeightController = TextEditingController(text: widget.originalProduct.height.toString());
  }

  @override
  void dispose() {
    newNameController.dispose();
    newWidthController.dispose();
    newHeightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Copy Product'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: newNameController,
                decoration: const InputDecoration(labelText: 'New Product Name', prefixIcon: Icon(Iconsax.box)),
                validator: (value) => TValidator.validateEmptyText('New Product Name', value),
              ),
              const SizedBox(height: TSizes.spaceBtwInputFields),

              // Checkbox to toggle dimension changes
              Row(
                children: [
                  Checkbox(
                    value: _changeDimensions,
                    onChanged: (value) {
                      setState(() {
                        _changeDimensions = value ?? false;
                      });
                    },
                  ),
                  const Text('Change Dimensions for Copy'),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwInputFields),

              if (_changeDimensions) ...[
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: newWidthController,
                        decoration: const InputDecoration(labelText: 'New Width (mm)', prefixIcon: Icon(Iconsax.ruler)),
                        keyboardType: TextInputType.number,
                        validator: (value) => TValidator.validateEmptyText(value, 'New Width'),
                      ),
                    ),
                    const SizedBox(width: TSizes.spaceBtwInputFields),
                    Expanded(
                      child: TextFormField(
                        controller: newHeightController,
                        decoration: const InputDecoration(
                          labelText: 'New Height (mm)',
                          prefixIcon: Icon(Iconsax.ruler),
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) => TValidator.validateEmptyText(value, 'New Height'),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: TSizes.spaceBtwInputFields),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
        ElevatedButton(
          onPressed: () {
            if (_formKey.currentState!.validate()) {
              double? finalWidth = _changeDimensions ? (double.tryParse(newWidthController.text) ?? 0.0) : null;
              double? finalHeight = _changeDimensions ? (double.tryParse(newHeightController.text) ?? 0.0) : null;
              widget.onCopy(newNameController.text.trim(), finalWidth, finalHeight);
              // The controller.copyProduct will handle closing the dialog
            }
          },
          child: const Text('Copy'),
        ),
      ],
    );
  }
}
