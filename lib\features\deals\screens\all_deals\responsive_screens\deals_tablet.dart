import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../common/widgets/data_table/table_header.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../controllers/deal_controller.dart';
import '../data_table/deals_table.dart';
import '../widgets/deals_filter_panel.dart';

class DealsTablet extends StatelessWidget {
  const DealsTablet({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DealController());

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Page Header
              Row(
                children: [
                  Icon(Iconsax.document, size: 24),
                  const SizedBox(width: TSizes.spaceBtwItems),
                  Text(
                    'Deals',
                    style: Theme.of(context).textTheme.headlineMedium,
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Filter Panel
              const DealsFilterPanel(),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Table Body
              TRoundedContainer(
                child: Column(
                  children: [
                    // Table Header
                    TTableHeader(
                      hintText: 'Search Deals',
                      searchOnChanged: (query) => controller.searchQuery(query),
                      actions: [
                        ElevatedButton.icon(
                          onPressed: () => Get.toNamed('/create-deal'),
                          icon: const Icon(Iconsax.add),
                          label: const Text('Create Deal'),
                        ),
                      ],
                    ),
                    const SizedBox(height: TSizes.spaceBtwItems),

                    // Table
                    Obx(() {
                      if (controller.isLoading.value) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(TSizes.defaultSpace),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }

                      if (controller.filteredItems.isEmpty) {
                        return Container(
                          height: 300,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Iconsax.document,
                                  size: 48,
                                  color: Colors.grey[400],
                                ),
                                const SizedBox(height: TSizes.spaceBtwItems),
                                Text(
                                  'No deals found',
                                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                    color: Colors.grey[600],
                                  ),
                                ),
                                const SizedBox(height: TSizes.spaceBtwItems),
                                ElevatedButton.icon(
                                  onPressed: () => Get.toNamed('/create-deal'),
                                  icon: const Icon(Iconsax.add),
                                  label: const Text('Create Deal'),
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      return const DealsTable();
                    }),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
