import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/helpers/helper_functions.dart';
import 'package:flutter/material.dart';

/// Enhanced template for the login page layout with modern styling
class TLoginTemplate extends StatelessWidget {
  const TLoginTemplate({super.key, required this.child});

  /// The widget to be displayed inside the login template
  final Widget child;

  @override
  Widget build(BuildContext context) {
    final isDark = THelperFunctions.isDarkMode(context);

    return Center(
      child: SizedBox(
        width: 600,
        child: SingleChildScrollView(
          child: Container(
            margin: const EdgeInsets.all(TSizes.defaultSpace),
            padding: const EdgeInsets.all(TSizes.defaultSpace * 1.5),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
              color: isDark ? TColors.dark : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: isDark ? Colors.black.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                  spreadRadius: 0,
                ),
                BoxShadow(
                  color: isDark ? Colors.black.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.05),
                  blurRadius: 40,
                  offset: const Offset(0, 16),
                  spreadRadius: 0,
                ),
              ],
              border: Border.all(
                color: isDark ? TColors.darkGrey.withValues(alpha: 0.3) : TColors.grey.withValues(alpha: 0.1),
                width: 1,
              ),
            ),
            child: child,
          ),
        ),
      ),
    );
  }
}
