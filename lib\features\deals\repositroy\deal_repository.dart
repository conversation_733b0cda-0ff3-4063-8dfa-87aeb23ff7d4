import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart'; // For PlatformException
import 'package:get/get.dart';
import '../../../utils/constants/text_strings.dart'; // TTexts (for collection names)
import '../../../utils/constants/enums.dart'; // UserRole, DealStatus (renamed from QuotationStatus)
import '../../../utils/exceptions/firebase_exceptions.dart';
import '../../../utils/exceptions/platform_exceptions.dart';
import '../models/deal_item_model.dart';
import '../models/deal_model.dart'; // DealModel (renamed from QuotationModel)

/// Repository for managing Deals (which encompass Quotations) in Firestore.
class DealRepository extends GetxController {
  // Renamed from QuotationRepository
  static DealRepository get instance => Get.find();

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Main collection reference for Deals
  final CollectionReference<DealModel> _dealsCollection = FirebaseFirestore.instance
      .collection(TTexts.deals)
      .withConverter<DealModel>(
        fromFirestore: (snapshot, options) => DealModel.fromSnapshot(snapshot),
        toFirestore: (deal, options) => deal.toJson(),
      );

  /// --- Deal Operations ---

  /// Creates a new Deal document in Firestore.
  Future<String> createDeal(DealModel deal) async {
    // Renamed from createQuotation
    try {
      final docRef = await _dealsCollection.add(deal);
      return docRef.id;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while creating deal: $e';
    }
  }

  /// Fetches a single Deal by its ID.
  Future<DealModel> getDealById(String dealId) async {
    // Renamed from getQuotationById
    try {
      final documentSnapshot = await _dealsCollection.doc(dealId).get();
      if (documentSnapshot.exists) {
        return documentSnapshot.data()!;
      } else {
        return DealModel.empty(); // Return empty model if not found
      }
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while fetching deal: $e';
    }
  }

  /// Streams all Deals, with filtering based on user roles (Point 1).
  /// [currentUserId]: The ID of the currently logged-in user.
  /// [currentUserRoles]: The roles of the currently logged-in user.
  Stream<List<DealModel>> streamAllDeals({
    // Renamed from streamAllQuotations
    required String currentUserId,
    required List<UserRole> currentUserRoles,
    String? searchQuery,
    DealStatus? statusFilter, // Renamed enum
    String? clientIdFilter,
    bool showLocked = true, // Option to show/hide locked deals
  }) {
    Query<DealModel> query = _dealsCollection;

    // Apply role-based filtering (Point 1)
    if (!currentUserRoles.contains(UserRole.Admin) && !currentUserRoles.contains(UserRole.Manager)) {
      // If not Admin or Manager, filter by ownerId
      query = query.where('ownerId', isEqualTo: currentUserId);
    }

    // Apply status filter
    if (statusFilter != null) {
      query = query.where('status', isEqualTo: statusFilter.name);
    }

    // Apply client filter
    if (clientIdFilter != null && clientIdFilter.isNotEmpty) {
      query = query.where('clientId', isEqualTo: clientIdFilter);
    }

    // Apply locked status filter
    if (!showLocked) {
      query = query.where('isLocked', isEqualTo: false);
    }

    // Order by creation date (or deal number for consistency)
    query = query.orderBy('createdAt', descending: true);

    return query.snapshots().map((snapshot) {
      List<DealModel> deals = snapshot.docs.map((doc) => doc.data()).toList();

      // Apply client-side search query if present (Firestore doesn't support full-text search)
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        deals = deals.where((deal) {
          return deal.dealNumber.toLowerCase().contains(searchLower) ||
              deal.clientName.toLowerCase().contains(searchLower) ||
              deal.projectDetails.toLowerCase().contains(searchLower) ||
              deal.salesPersonName.toLowerCase().contains(searchLower);
        }).toList();
      }
      return deals;
    });
  }

  /// Streams deals by specific status for approval workflow
  Stream<List<DealModel>> streamDealsByStatus(DealStatus status) {
    try {
      return _dealsCollection
          .where('status', isEqualTo: status.name)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) => snapshot.docs.map((doc) => doc.data()).toList());
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while streaming deals by status: $e';
    }
  }

  /// Get all deal items for a specific deal
  Future<List<DealItemModel>> getDealItems(String dealId) async {
    try {
      final snapshot = await _dealsCollection
          .doc(dealId)
          .collection(TTexts.dealItems)
          .withConverter<DealItemModel>(
            fromFirestore: (snapshot, options) => DealItemModel.fromSnapshot(snapshot),
            toFirestore: (item, options) => item.toJson(),
          )
          .get();

      return snapshot.docs.map((doc) => doc.data()).toList();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while fetching deal items: $e';
    }
  }

  /// Updates an existing Deal document in Firestore.
  Future<void> updateDeal(DealModel deal) async {
    // Renamed from updateQuotation
    try {
      await _dealsCollection.doc(deal.id).update(deal.toJson());
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while updating deal: $e';
    }
  }

  /// Deletes a Deal document and its subcollection items.
  Future<void> deleteDeal(String dealId) async {
    // Renamed from deleteQuotation
    try {
      // First, delete all items in the subcollection
      final itemsSnapshot = await _dealsCollection.doc(dealId).collection(TTexts.dealItems).get();
      for (final doc in itemsSnapshot.docs) {
        await doc.reference.delete();
      }
      // Then, delete the main deal document
      await _dealsCollection.doc(dealId).delete();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while deleting deal: $e';
    }
  }

  /// --- Deal Item Operations (Subcollection) ---

  /// Adds a new item to a specific deal.
  Future<String> addDealItem(String dealId, DealItemModel item) async {
    // Renamed from addQuotationItem
    try {
      final docRef = await _dealsCollection.doc(dealId).collection(TTexts.dealItems).add(item.toJson());
      return docRef.id;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while adding deal item: $e';
    }
  }

  /// Streams all items for a specific deal.
  Stream<List<DealItemModel>> streamDealItems(String dealId) {
    // Renamed from streamQuotationItems
    return _dealsCollection
        .doc(dealId)
        .collection(TTexts.dealItems)
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => DealItemModel.fromSnapshot(doc)).toList());
  }

  /// Updates an existing item within a deal.
  Future<void> updateDealItem(String dealId, DealItemModel item) async {
    // Renamed from updateQuotationItem
    try {
      await _dealsCollection.doc(dealId).collection(TTexts.dealItems).doc(item.id).update(item.toJson());
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while updating deal item: $e';
    }
  }

  /// Deletes an item from a specific deal.
  Future<void> deleteDealItem(String dealId, String itemId) async {
    // Renamed from deleteQuotationItem
    try {
      await _dealsCollection.doc(dealId).collection(TTexts.dealItems).doc(itemId).delete();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while deleting deal item: $e';
    }
  }
}
