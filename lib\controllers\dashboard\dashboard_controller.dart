import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/helpers/helper_functions.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../features/products/models/old_order_model.dart';

class DashboardController extends GetxController {
  static DashboardController get instance => Get.find();

  // For Bar Chart
  final RxList<double> weeklySales = <double>[].obs;

  // For Pie Chart
  final RxMap<OrderStatus, int> orderStatusData = <OrderStatus, int>{}.obs;
  final RxMap<OrderStatus, double> totalOrderAmount = <OrderStatus, double>{}.obs;

  /// -- Order
  static final List<OldOrderModel> orders = [
    // --- NEW ORDERS FOR THE CURRENT WEEK (June 2 - June 8, 2025) ---
    OldOrderModel(
      id: 'CURW001',
      status: OrderStatus.processing,
      totalAmount: 50.0,
      orderDate: DateTime(2025, 6, 2), // Monday, June 2nd
      deliveryDate: DateTime(2025, 6, 3),
      userId: '',
      docId: '',
      shippingCost: 1,
      taxCost: 1,
    ),
    OldOrderModel(
      id: 'CURW002',
      status: OrderStatus.shipped,
      totalAmount: 120.50,
      orderDate: DateTime(2025, 6, 3), // Tuesday, June 3rd
      deliveryDate: DateTime(2025, 6, 4),
      userId: '',
      docId: '',
      shippingCost: 1,
      taxCost: 1,
    ),
    OldOrderModel(
      id: 'CURW003',
      status: OrderStatus.delivered,
      totalAmount: 275.25,
      orderDate: DateTime(2025, 6, 5), // Thursday, June 5th
      deliveryDate: DateTime(2025, 6, 5),
      userId: '',
      docId: '',
      shippingCost: 1,
      taxCost: 1,
    ),
    OldOrderModel(
      id: 'CURW004',
      status: OrderStatus.processing,
      totalAmount: 346.00,
      orderDate: DateTime(2025, 6, 6), // Friday, June 6th (Today)
      deliveryDate: DateTime(2025, 6, 7),
      userId: '',
      docId: '',
      shippingCost: 1,
      taxCost: 1,
    ),
    OldOrderModel(
      id: 'CURW005',
      status: OrderStatus.delivered,
      totalAmount: 30.00,
      orderDate: DateTime(2025, 6, 2), // Another order on Monday, June 2nd
      deliveryDate: DateTime(2025, 6, 2),
      userId: '',
      docId: '',
      shippingCost: 1,
      taxCost: 1,
    ),
  ];

  @override
  onInit() {
    super.onInit();
    _calculateWeeklySales();
    _calculateOrderStatusData();
  }

  /// Calculate weekly sales
  void _calculateWeeklySales() {
    // Reset weeklySales to zeros
    // This line initializes a list of 7 doubles, all set to 0.0, representing sales for 7 days.
    weeklySales.value = List<double>.filled(7, 0.0);

    // Iterate through each order in the 'orders' list
    for (var order in orders) {
      // Calculate the start of the week for the order's orderDate.
      // It's crucial that this function correctly identifies the start of the week
      // (e.g., Monday or Sunday) consistently with how DateTime.weekday behaves.
      final DateTime orderWeekStart = THelperFunctions.getStartOfWeek(order.orderDate);

      // Check if the order is within the current week
      // This condition checks if the order's week starts on or before the current day,
      // AND if the order's week ends on or after the current day.
      // DateTime.now() should ideally be consistent (e.g., use a fixed 'today' if needed for testing).
      if (orderWeekStart.isBefore(DateTime.now()) &&
          orderWeekStart.add(const Duration(days: 7)).isAfter(DateTime.now())) {
        // Calculate the index for the day of the week (0 for Monday, 6 for Sunday, or similar).
        // (order.orderDate.weekday - 1) will give 0 for Monday (if weekday is 1),
        // 1 for Tuesday (if weekday is 2), ..., 6 for Sunday (if weekday is 7).
        // The % 7 ensures the index wraps around, though for 1-7, it's mostly straightforward.
        int index = (order.orderDate.weekday - 1) % 7;

        // Ensure the index is non-negative
        // This is a common pattern to handle cases where Dart's `weekday` (1-7 for Mon-Sun)
        // might lead to negative results if not careful with (weekday - 1) when weekday is 0 or less (which it isn't).
        // For standard Dart `weekday` (1-7), `(weekday - 1) % 7` will already yield 0-6.
        // This line `index = index < 0 ? index + 7 : index;` is a safeguard, but
        // might be redundant if `weekday` is always 1-7 and `getStartOfWeek` is consistent.
        index = index < 0
            ? index + 7
            : index; // This line seems redundant given `(weekday - 1) % 7` for positive weekdays.

        // Add the order's total amount to the corresponding day's sales
        weeklySales[index] += order.totalAmount;
        // print('OrderDate: ${order.orderDate}, CurrentWeekDay: $orderWeekStart, Index: $index');
      }
    }

    // Print the final weekly sales list
    // print('Weekly Sales: $weeklySales');
  }

  void _calculateOrderStatusData() {
    // Reset the order status data
    orderStatusData.clear();

    // Map to store total amounts for each status
    // Using OrderStatus ENUMS eg. pending, processing, shipped, etc
    // this will create a statuses map add 0.0 to all the status
    totalOrderAmount.value = {for (var status in OrderStatus.values) status: 0.0};

    // Calculate total order amount for each status
    for (var order in orders) {
      // Count Orders
      final status = order.status;
      orderStatusData[status] = (orderStatusData[status] ?? 0) + 1;

      // Calculate total amounts for each status
      totalOrderAmount[status] = (totalOrderAmount[status] ?? 0) + order.totalAmount;
    }
  }

  /// A utility function to get a human-readable display name for an OrderStatus.
  String getDisplayStatusName(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.shipped:
        return 'Shipped';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  /// ***************************************************************************************** ///
  var dataList = <Map<String, dynamic>>[].obs;
  var filteredDataList = <Map<String, dynamic>>[].obs;
  RxList<bool> selectedRows = <bool>[].obs;

  RxInt sortColumnIndex = 1.obs; // Rx for tracking the index of the column for sorting
  RxBool sortAscending = true.obs; // Rx for tracking the sorting order (asscending or descending)

  final searchTextController = TextEditingController();

  // @override
  // void onInit() {
  //   super.onInit();
  //   fetchData();
  // }

  // void sortById(int sortColumnIndex, bool isAscending) {
  //   sortAscending.value = isAscending;
  //   dataList.sort((a, b) {
  //     if (isAscending) {
  //       return a['ID'].toString().toLowerCase().compareTo(b['ID'].toString().toLowerCase());
  //     } else {
  //       return b['ID'].toString().toLowerCase().compareTo(a['ID'].toString().toLowerCase());
  //     }
  //   });
  //   this.sortColumnIndex.value = sortColumnIndex;
  // }

  void sortById(int sortColumnIndex, bool isAscending) {
    sortAscending.value = isAscending;
    filteredDataList.sort((a, b) {
      if (isAscending) {
        return filteredDataList[0]['ID'].toString().toLowerCase().compareTo(
          filteredDataList[0]['ID'].toString().toLowerCase(),
        );
      } else {
        return filteredDataList[0]['ID'].toString().toLowerCase().compareTo(
          filteredDataList[0]['ID'].toString().toLowerCase(),
        );
      }
    });
    this.sortColumnIndex.value = sortColumnIndex;
  }

  void searchQuery(String query) {
    filteredDataList.assignAll(dataList.where((item) => item['NAME']!.contains(query.toLowerCase())));
  }

  void fetchData() async {
    // Simulate fetching data from a server // await Future.delayed(Duration(seconds: 2));

    // Rx variable
    selectedRows.assignAll(List.generate(40, (index) => false));

    // Replace this with your actual data fetching logic
    dataList.addAll(
      List.generate(36, (index) => {'ID': index, 'NAME': 'Item $index', 'DESCRIPTION': 'Description for Item $index'}),
    );
    filteredDataList.addAll(
      List.generate(36, (index) => {'ID': index, 'NAME': 'Item $index', 'DESCRIPTION': 'Description for Item $index'}),
    );
  }
}
