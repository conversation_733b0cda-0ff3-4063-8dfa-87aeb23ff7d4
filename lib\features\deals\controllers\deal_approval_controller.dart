import 'package:get/get.dart';

import '../../../utils/constants/enums.dart';

import '../../../utils/helpers/network_manager.dart';
import '../../../utils/popups/full_screen_loader.dart';
import '../../../utils/popups/loaders.dart';
import '../../authentication/controllers/user_controller.dart';
import '../../settings/controllers/settings_controller.dart';
import '../models/deal_model.dart';
import '../repositroy/deal_repository.dart';

/// Deal Approval Workflow Controller
/// Manages deal approval processes, role-based permissions, and sequential workflow stages
class DealApprovalController extends GetxController {
  static DealApprovalController get instance => Get.find();

  final _dealRepository = Get.put(DealRepository());
  final _userController = Get.put(UserController());
  final _settingsController = Get.put(SettingsController());

  // Observable lists for different approval queues
  final pendingApprovals = <DealModel>[].obs;
  final approvedDeals = <DealModel>[].obs;
  final rejectedDeals = <DealModel>[].obs;
  final unlockRequests = <DealModel>[].obs;

  // Loading states
  final isLoading = false.obs;
  final isProcessingApproval = false.obs;

  @override
  void onInit() {
    super.onInit();
    _loadApprovalQueues();
  }

  /// Load all approval queues based on user role
  void _loadApprovalQueues() {
    final currentUser = _userController.user.value;

    // Only load approval queues for authorized roles
    if (_canAccessApprovalSystem(currentUser.roles)) {
      _bindPendingApprovalsStream();
      _bindApprovedDealsStream();
      _bindRejectedDealsStream();
      _bindUnlockRequestsStream();
    }
  }

  /// Check if user can access approval system
  bool _canAccessApprovalSystem(List<UserRole> roles) {
    return roles.contains(UserRole.Admin) || roles.contains(UserRole.Manager) || _isApprovedAuthority(roles);
  }

  /// Check if user is an approved authority (from settings)
  bool _isApprovedAuthority(List<UserRole> roles) {
    // TODO: Implement approved authority check from settings
    // For now, include ProductionReviewer as they might have approval rights
    return roles.contains(UserRole.ProductionReviewer);
  }

  /// Bind pending approvals stream
  void _bindPendingApprovalsStream() {
    try {
      final stream = _dealRepository.streamDealsByStatus(DealStatus.pendingApproval);
      pendingApprovals.bindStream(stream);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Failed to load pending approvals: $e');
    }
  }

  /// Bind approved deals stream
  void _bindApprovedDealsStream() {
    try {
      final stream = _dealRepository.streamDealsByStatus(DealStatus.approved);
      approvedDeals.bindStream(stream);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Failed to load approved deals: $e');
    }
  }

  /// Bind rejected deals stream
  void _bindRejectedDealsStream() {
    try {
      final stream = _dealRepository.streamDealsByStatus(DealStatus.rejected);
      rejectedDeals.bindStream(stream);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Failed to load rejected deals: $e');
    }
  }

  /// Bind unlock requests stream
  void _bindUnlockRequestsStream() {
    try {
      final stream = _dealRepository.streamDealsByStatus(DealStatus.unlockRequested);
      unlockRequests.bindStream(stream);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Failed to load unlock requests: $e');
    }
  }

  /// Approve a deal
  Future<void> approveDeal(DealModel deal, {String? approvalComments}) async {
    try {
      isProcessingApproval.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final currentUser = _userController.user.value;

      // Validate approval permissions
      if (!_canApproveDeal(currentUser.roles)) {
        throw 'You do not have permission to approve deals';
      }

      // Update deal with approval information
      final approvedDeal = deal.copyWith(
        status: DealStatus.approved,
        approvedByUserId: currentUser.id!,
        approvalDate: DateTime.now(),
        isLocked: true,
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
        // Add approval comments if provided
        dealInstructions: approvalComments != null
            ? '${deal.dealInstructions ?? ''}\n\nApproval Comments: $approvalComments'.trim()
            : deal.dealInstructions,
      );

      await _dealRepository.updateDeal(approvedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Deal Approved',
        message: 'Deal ${deal.dealNumber} has been approved successfully.',
      );

      // TODO: Send notification to sales person
      _sendApprovalNotification(deal, currentUser.id!, true);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Approval Failed', message: e.toString());
    } finally {
      isProcessingApproval.value = false;
    }
  }

  /// Reject a deal
  Future<void> rejectDeal(DealModel deal, String rejectionReason) async {
    try {
      isProcessingApproval.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final currentUser = _userController.user.value;

      // Validate rejection permissions
      if (!_canApproveDeal(currentUser.roles)) {
        throw 'You do not have permission to reject deals';
      }

      // Update deal with rejection information
      final rejectedDeal = deal.copyWith(
        status: DealStatus.rejected,
        isLocked: false, // Allow editing after rejection
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
        // Add rejection reason to deal instructions
        dealInstructions: '${deal.dealInstructions ?? ''}\n\nRejection Reason: $rejectionReason'.trim(),
      );

      await _dealRepository.updateDeal(rejectedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Deal Rejected', message: 'Deal ${deal.dealNumber} has been rejected.');

      // TODO: Send notification to sales person
      _sendApprovalNotification(deal, currentUser.id!, false);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Rejection Failed', message: e.toString());
    } finally {
      isProcessingApproval.value = false;
    }
  }

  /// Request unlock for an approved deal
  Future<void> requestUnlock(DealModel deal, String unlockReason) async {
    try {
      isProcessingApproval.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final currentUser = _userController.user.value;

      // Update deal status to unlock requested
      final unlockRequestedDeal = deal.copyWith(
        status: DealStatus.unlockRequested,
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
        // Add unlock reason to deal instructions
        dealInstructions: '${deal.dealInstructions ?? ''}\n\nUnlock Request: $unlockReason'.trim(),
      );

      await _dealRepository.updateDeal(unlockRequestedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Unlock Requested',
        message: 'Unlock request for deal ${deal.dealNumber} has been submitted.',
      );

      // TODO: Send notification to managers/admins
      _sendUnlockRequestNotification(deal, currentUser.id!, unlockReason);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Unlock Request Failed', message: e.toString());
    } finally {
      isProcessingApproval.value = false;
    }
  }

  /// Approve unlock request
  Future<void> approveUnlockRequest(DealModel deal) async {
    try {
      isProcessingApproval.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final currentUser = _userController.user.value;

      // Validate unlock approval permissions (only admins and managers)
      if (!currentUser.roles.contains(UserRole.Admin) && !currentUser.roles.contains(UserRole.Manager)) {
        throw 'You do not have permission to approve unlock requests';
      }

      // Update deal status back to draft for editing
      final unlockedDeal = deal.copyWith(
        status: DealStatus.draft,
        isLocked: false,
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
        // Add unlock approval to deal instructions
        dealInstructions:
            '${deal.dealInstructions ?? ''}\n\nUnlock Approved by: ${currentUser.firstName} ${currentUser.lastName}'
                .trim(),
      );

      await _dealRepository.updateDeal(unlockedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Unlock Approved',
        message: 'Deal ${deal.dealNumber} has been unlocked for editing.',
      );
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Unlock Approval Failed', message: e.toString());
    } finally {
      isProcessingApproval.value = false;
    }
  }

  /// Check if user can approve deals
  bool _canApproveDeal(List<UserRole> roles) {
    return roles.contains(UserRole.Admin) || roles.contains(UserRole.Manager) || _isApprovedAuthority(roles);
  }

  /// Send approval notification (placeholder)
  void _sendApprovalNotification(DealModel deal, String approverId, bool isApproved) {
    // TODO: Implement notification system
    // This could send email, push notification, or in-app notification
    print('Sending ${isApproved ? 'approval' : 'rejection'} notification for deal ${deal.dealNumber}');
  }

  /// Send unlock request notification (placeholder)
  void _sendUnlockRequestNotification(DealModel deal, String requesterId, String reason) {
    // TODO: Implement notification system
    print('Sending unlock request notification for deal ${deal.dealNumber}');
  }

  /// Get approval queue counts for dashboard
  Map<String, int> getApprovalCounts() {
    return {
      'pending': pendingApprovals.length,
      'approved': approvedDeals.length,
      'rejected': rejectedDeals.length,
      'unlockRequests': unlockRequests.length,
    };
  }

  /// Check if current user can access approval features
  bool get canAccessApprovalSystem {
    final currentUser = _userController.user.value;
    return _canAccessApprovalSystem(currentUser.roles);
  }

  /// Check if current user can approve deals
  bool get canApproveDeal {
    final currentUser = _userController.user.value;
    return _canApproveDeal(currentUser.roles);
  }
}
