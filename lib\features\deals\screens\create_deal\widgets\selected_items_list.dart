import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/formatters/formatter.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_item_controller.dart';
import '../../../models/deal_item_model.dart';

/// Selected Items List for Stage 2 Deal Creation
/// Shows all selected deal items with editing and removal capabilities
class SelectedItemsList extends StatelessWidget {
  const SelectedItemsList({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DealItemController());
    final dark = THelperFunctions.isDarkMode(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Selected Items', style: Theme.of(context).textTheme.titleLarge),
            Obx(
              () => Container(
                padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
                decoration: BoxDecoration(
                  color: TColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  border: Border.all(color: TColors.primary),
                ),
                child: Text(
                  '${controller.dealItems.length} items',
                  style: Theme.of(
                    context,
                  ).textTheme.labelMedium?.copyWith(color: TColors.primary, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: TSizes.spaceBtwItems),

        // Items list
        Expanded(
          child: Obx(() {
            final items = controller.dealItems;

            if (items.isEmpty) {
              return _buildEmptyState(context);
            }

            return ListView.separated(
              itemCount: items.length,
              separatorBuilder: (context, index) => const SizedBox(height: TSizes.spaceBtwItems),
              itemBuilder: (context, index) {
                final item = items[index];
                return _buildItemCard(context, item, controller, dark);
              },
            );
          }),
        ),
      ],
    );
  }

  /// Empty state when no items are selected
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Iconsax.shopping_cart, size: 64, color: TColors.darkGrey),
          const SizedBox(height: TSizes.spaceBtwItems),
          Text('No items selected', style: Theme.of(context).textTheme.titleMedium),
          const SizedBox(height: TSizes.spaceBtwItems / 2),
          Text(
            'Browse the product catalog to add items',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: TColors.darkGrey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Individual item card
  Widget _buildItemCard(BuildContext context, DealItemModel item, DealItemController controller, bool dark) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: dark ? TColors.darkerGrey : TColors.white,
        borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
        border: Border.all(color: TColors.borderPrimary),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item header with actions
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      item.productName,
                      style: Theme.of(context).textTheme.titleMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (item.productDescription.isNotEmpty) ...[
                      const SizedBox(height: TSizes.xs),
                      Text(
                        item.productDescription,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),

              // Action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    onPressed: () => _editItem(context, item, controller),
                    icon: const Icon(Iconsax.edit),
                    iconSize: 20,
                    tooltip: 'Edit Item',
                  ),
                  IconButton(
                    onPressed: () => _removeItem(context, item, controller),
                    icon: const Icon(Iconsax.trash),
                    iconSize: 20,
                    color: TColors.error,
                    tooltip: 'Remove Item',
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Item details
          _buildItemDetails(context, item),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Pricing information
          _buildPricingInfo(context, item),
        ],
      ),
    );
  }

  /// Item details section
  Widget _buildItemDetails(BuildContext context, DealItemModel item) {
    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(color: TColors.light, borderRadius: BorderRadius.circular(TSizes.borderRadiusSm)),
      child: Column(
        children: [
          // Quantity and unit
          Row(
            children: [
              Expanded(
                child: _buildDetailRow(
                  context,
                  'Quantity',
                  '${TFormatter.formatNumber(item.quotedQuantity)} ${item.quotedUnit}',
                  Iconsax.box,
                ),
              ),
              if (item.totalFactoryWeightKg != null) ...[
                const SizedBox(width: TSizes.spaceBtwItems),
                Expanded(
                  child: _buildDetailRow(
                    context,
                    'Weight',
                    '${TFormatter.formatNumber(item.totalFactoryWeightKg!)} kg',
                    Iconsax.weight,
                  ),
                ),
              ],
            ],
          ),

          // Variant attributes
          if (item.variantAttributes.isNotEmpty) ...[
            const SizedBox(height: TSizes.spaceBtwItems / 2),
            Row(
              children: [
                Icon(Iconsax.setting_2, size: 16, color: TColors.darkGrey),
                const SizedBox(width: TSizes.xs),
                Expanded(
                  child: Text(
                    _buildVariantAttributesText(item.variantAttributes),
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Detail row helper
  Widget _buildDetailRow(BuildContext context, String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: TColors.darkGrey),
        const SizedBox(width: TSizes.xs),
        Text('$label: ', style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500)),
        Expanded(
          child: Text(value, style: Theme.of(context).textTheme.bodySmall, overflow: TextOverflow.ellipsis),
        ),
      ],
    );
  }

  /// Pricing information section
  Widget _buildPricingInfo(BuildContext context, DealItemModel item) {
    final hasUserPrice = item.userProvidedUnitPrice != null;
    final unitPrice = hasUserPrice ? item.userProvidedUnitPrice! : item.autoCalculatedUnitPrice;

    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(
        color: TColors.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
        border: Border.all(color: TColors.primary.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          // Unit price
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    hasUserPrice ? Iconsax.edit : Iconsax.calculator,
                    size: 16,
                    color: hasUserPrice ? TColors.warning : TColors.primary,
                  ),
                  const SizedBox(width: TSizes.xs),
                  Text(
                    hasUserPrice ? 'Custom Price' : 'Auto Price',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ],
              ),
              Text(
                '₹${TFormatter.formatNumber(unitPrice)}/${item.quotedUnit}',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: hasUserPrice ? TColors.warning : TColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems / 2),

          // Total price
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total Amount',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
              ),
              Text(
                '₹${TFormatter.formatNumber(item.totalItemPrice)}',
                style: Theme.of(
                  context,
                ).textTheme.titleMedium?.copyWith(color: TColors.primary, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build variant attributes text
  String _buildVariantAttributesText(Map<String, String> attributes) {
    final parts = <String>[];

    if (attributes.containsKey('Material')) {
      parts.add(attributes['Material']!);
    }
    if (attributes.containsKey('Thickness')) {
      parts.add(attributes['Thickness']!);
    }
    if (attributes.containsKey('Length')) {
      parts.add(attributes['Length']!);
    }

    return parts.isNotEmpty ? parts.join(' • ') : 'Standard variant';
  }

  /// Edit item dialog
  void _editItem(BuildContext context, DealItemModel item, DealItemController controller) {
    final quantityController = TextEditingController(text: item.quotedQuantity.toString());
    final priceController = TextEditingController(text: item.userProvidedUnitPrice?.toString() ?? '');

    Get.dialog(
      AlertDialog(
        title: Text('Edit ${item.productName}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: quantityController,
              decoration: const InputDecoration(labelText: 'Quantity', border: OutlineInputBorder()),
              keyboardType: TextInputType.number,
            ),
            const SizedBox(height: TSizes.spaceBtwItems),

            TextFormField(
              controller: priceController,
              decoration: const InputDecoration(
                labelText: 'Custom Unit Price (Optional)',
                border: OutlineInputBorder(),
                hintText: 'Leave empty for auto-calculated price',
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement item update
              Get.back();
            },
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  /// Remove item confirmation
  void _removeItem(BuildContext context, DealItemModel item, DealItemController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('Remove Item'),
        content: Text('Are you sure you want to remove "${item.productName}" from this deal?'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              controller.deleteDealItem(item);
              Get.back();
            },
            style: ElevatedButton.styleFrom(backgroundColor: TColors.error, foregroundColor: TColors.white),
            child: const Text('Remove'),
          ),
        ],
      ),
    );
  }
}
