import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/personilization/screens/profile/responsive_screens/profile_desktop.dart';
import 'package:alloy/features/personilization/screens/profile/responsive_screens/profile_mobile.dart';
import 'package:alloy/features/personilization/screens/profile/responsive_screens/profile_tablet.dart';
import 'package:flutter/material.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(
      useLayout: true,
      desktop: ProfileDesktop(),
      tablet: ProfileTablet(),
      mobile: ProfileMobile(),
    );
  }
}
