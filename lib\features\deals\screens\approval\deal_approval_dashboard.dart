import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../common/layouts/templates/site_template.dart';
import '../../../../common/widgets/appbar/appbar.dart';
import '../../../../utils/constants/colors.dart';
import '../../../../utils/constants/sizes.dart';
import '../../../../utils/helpers/helper_functions.dart';
import '../../controllers/deal_approval_controller.dart';
import '../../models/deal_model.dart';
import 'widgets/approval_queue_card.dart';
import 'widgets/deal_approval_card.dart';

/// Deal Approval Dashboard
/// Central hub for managing deal approvals, rejections, and unlock requests
class DealApprovalDashboard extends StatelessWidget {
  const DealApprovalDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DealApprovalController());

    return TSiteTemplate(
      desktop: _buildDesktopLayout(context, controller),
      mobile: _buildMobileLayout(context, controller),
    );
  }

  /// Desktop layout with side-by-side panels
  Widget _buildDesktopLayout(BuildContext context, DealApprovalController controller) {
    return Scaffold(
      appBar: TAppBar(title: Text('Deal Approval Dashboard'), showBackArrow: true),
      body: Padding(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Column(
          children: [
            // Overview cards
            _buildOverviewCards(context, controller),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Main content area
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Left panel: Approval queues
                  Expanded(flex: 1, child: _buildApprovalQueues(context, controller)),
                  const SizedBox(width: TSizes.spaceBtwSections),

                  // Right panel: Selected queue details
                  Expanded(flex: 2, child: _buildQueueDetails(context, controller)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Mobile layout with tabbed interface
  Widget _buildMobileLayout(BuildContext context, DealApprovalController controller) {
    return DefaultTabController(
      length: 4,
      child: Scaffold(
        appBar: TAppBar(title: Text('Deal Approvals'), showBackArrow: true),
        body: Column(
          children: [
            TabBar(
              isScrollable: true,
              tabs: [
                Tab(text: 'Pending', icon: Icon(Iconsax.clock)),
                Tab(text: 'Approved', icon: Icon(Iconsax.tick_circle)),
                Tab(text: 'Rejected', icon: Icon(Iconsax.close_circle)),
                Tab(text: 'Unlock Requests', icon: Icon(Iconsax.unlock)),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [
                  _buildPendingApprovalsTab(context, controller),
                  _buildApprovedDealsTab(context, controller),
                  _buildRejectedDealsTab(context, controller),
                  _buildUnlockRequestsTab(context, controller),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Overview cards showing approval statistics
  Widget _buildOverviewCards(BuildContext context, DealApprovalController controller) {
    return Obx(() {
      final counts = controller.getApprovalCounts();

      return Row(
        children: [
          Expanded(
            child: _buildOverviewCard(context, 'Pending Approvals', counts['pending']!, Iconsax.clock, TColors.warning),
          ),
          const SizedBox(width: TSizes.spaceBtwItems),

          Expanded(
            child: _buildOverviewCard(context, 'Approved', counts['approved']!, Iconsax.tick_circle, TColors.success),
          ),
          const SizedBox(width: TSizes.spaceBtwItems),

          Expanded(
            child: _buildOverviewCard(context, 'Rejected', counts['rejected']!, Iconsax.close_circle, TColors.error),
          ),
          const SizedBox(width: TSizes.spaceBtwItems),

          Expanded(
            child: _buildOverviewCard(
              context,
              'Unlock Requests',
              counts['unlockRequests']!,
              Iconsax.unlock,
              TColors.info,
            ),
          ),
        ],
      );
    });
  }

  /// Individual overview card
  Widget _buildOverviewCard(BuildContext context, String title, int count, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: TSizes.xs),
              Expanded(
                child: Text(title, style: Theme.of(context).textTheme.titleSmall, overflow: TextOverflow.ellipsis),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          Align(
            alignment: Alignment.centerRight,
            child: Text(
              count.toString(),
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(color: color, fontWeight: FontWeight.bold),
            ),
          ),
        ],
      ),
    );
  }

  /// Approval queues panel
  Widget _buildApprovalQueues(BuildContext context, DealApprovalController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Approval Queues', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: TSizes.spaceBtwItems),

        Expanded(
          child: ListView(
            children: [
              ApprovalQueueCard(
                title: 'Pending Approvals',
                icon: Iconsax.clock,
                color: TColors.warning,
                count: controller.pendingApprovals.length,
                onTap: () => _selectQueue(context, 'pending'),
              ),
              const SizedBox(height: TSizes.spaceBtwItems),

              ApprovalQueueCard(
                title: 'Approved Deals',
                icon: Iconsax.tick_circle,
                color: TColors.success,
                count: controller.approvedDeals.length,
                onTap: () => _selectQueue(context, 'approved'),
              ),
              const SizedBox(height: TSizes.spaceBtwItems),

              ApprovalQueueCard(
                title: 'Rejected Deals',
                icon: Iconsax.close_circle,
                color: TColors.error,
                count: controller.rejectedDeals.length,
                onTap: () => _selectQueue(context, 'rejected'),
              ),
              const SizedBox(height: TSizes.spaceBtwItems),

              ApprovalQueueCard(
                title: 'Unlock Requests',
                icon: Iconsax.unlock,
                color: TColors.info,
                count: controller.unlockRequests.length,
                onTap: () => _selectQueue(context, 'unlock'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Queue details panel
  Widget _buildQueueDetails(BuildContext context, DealApprovalController controller) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: THelperFunctions.isDarkMode(context) ? TColors.darkerGrey : TColors.white,
        borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
        border: Border.all(color: TColors.borderPrimary),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Queue Details', style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: TSizes.spaceBtwItems),

          Expanded(
            child: Center(
              child: Text(
                'Select a queue to view details',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: TColors.darkGrey),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Mobile tab for pending approvals
  Widget _buildPendingApprovalsTab(BuildContext context, DealApprovalController controller) {
    return Obx(() {
      final deals = controller.pendingApprovals;

      if (deals.isEmpty) {
        return _buildEmptyState(context, 'No pending approvals', Iconsax.clock);
      }

      return ListView.separated(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        itemCount: deals.length,
        separatorBuilder: (context, index) => const SizedBox(height: TSizes.spaceBtwItems),
        itemBuilder: (context, index) {
          final deal = deals[index];
          return DealApprovalCard(
            deal: deal,
            onApprove: () => _showApprovalDialog(context, deal, controller),
            onReject: () => _showRejectionDialog(context, deal, controller),
          );
        },
      );
    });
  }

  /// Mobile tab for approved deals
  Widget _buildApprovedDealsTab(BuildContext context, DealApprovalController controller) {
    return Obx(() {
      final deals = controller.approvedDeals;

      if (deals.isEmpty) {
        return _buildEmptyState(context, 'No approved deals', Iconsax.tick_circle);
      }

      return ListView.separated(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        itemCount: deals.length,
        separatorBuilder: (context, index) => const SizedBox(height: TSizes.spaceBtwItems),
        itemBuilder: (context, index) {
          final deal = deals[index];
          return DealApprovalCard(
            deal: deal,
            showApprovalActions: false,
            onRequestUnlock: () => _showUnlockRequestDialog(context, deal, controller),
          );
        },
      );
    });
  }

  /// Mobile tab for rejected deals
  Widget _buildRejectedDealsTab(BuildContext context, DealApprovalController controller) {
    return Obx(() {
      final deals = controller.rejectedDeals;

      if (deals.isEmpty) {
        return _buildEmptyState(context, 'No rejected deals', Iconsax.close_circle);
      }

      return ListView.separated(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        itemCount: deals.length,
        separatorBuilder: (context, index) => const SizedBox(height: TSizes.spaceBtwItems),
        itemBuilder: (context, index) {
          final deal = deals[index];
          return DealApprovalCard(deal: deal, showApprovalActions: false);
        },
      );
    });
  }

  /// Mobile tab for unlock requests
  Widget _buildUnlockRequestsTab(BuildContext context, DealApprovalController controller) {
    return Obx(() {
      final deals = controller.unlockRequests;

      if (deals.isEmpty) {
        return _buildEmptyState(context, 'No unlock requests', Iconsax.unlock);
      }

      return ListView.separated(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        itemCount: deals.length,
        separatorBuilder: (context, index) => const SizedBox(height: TSizes.spaceBtwItems),
        itemBuilder: (context, index) {
          final deal = deals[index];
          return DealApprovalCard(
            deal: deal,
            showApprovalActions: false,
            onApproveUnlock: () => _approveUnlockRequest(context, deal, controller),
          );
        },
      );
    });
  }

  /// Empty state widget
  Widget _buildEmptyState(BuildContext context, String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: TColors.darkGrey),
          const SizedBox(height: TSizes.spaceBtwItems),
          Text(message, style: Theme.of(context).textTheme.titleMedium),
        ],
      ),
    );
  }

  /// Select queue for desktop view
  void _selectQueue(BuildContext context, String queueType) {
    // TODO: Implement queue selection logic for desktop view
    print('Selected queue: $queueType');
  }

  /// Show approval dialog
  void _showApprovalDialog(BuildContext context, DealModel deal, DealApprovalController controller) {
    final commentsController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Text('Approve Deal ${deal.dealNumber}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Are you sure you want to approve this deal?'),
            const SizedBox(height: TSizes.spaceBtwItems),

            TextFormField(
              controller: commentsController,
              decoration: const InputDecoration(
                labelText: 'Approval Comments (Optional)',
                border: OutlineInputBorder(),
                hintText: 'Add any comments for the approval...',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              controller.approveDeal(deal, approvalComments: commentsController.text);
              Get.back();
            },
            child: const Text('Approve'),
          ),
        ],
      ),
    );
  }

  /// Show rejection dialog
  void _showRejectionDialog(BuildContext context, DealModel deal, DealApprovalController controller) {
    final reasonController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Text('Reject Deal ${deal.dealNumber}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Please provide a reason for rejecting this deal:'),
            const SizedBox(height: TSizes.spaceBtwItems),

            TextFormField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Rejection Reason *',
                border: OutlineInputBorder(),
                hintText: 'Explain why this deal is being rejected...',
              ),
              maxLines: 3,
              validator: (value) => value?.isEmpty ?? true ? 'Rejection reason is required' : null,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isNotEmpty) {
                controller.rejectDeal(deal, reasonController.text);
                Get.back();
              }
            },
            style: ElevatedButton.styleFrom(backgroundColor: TColors.error, foregroundColor: TColors.white),
            child: const Text('Reject'),
          ),
        ],
      ),
    );
  }

  /// Show unlock request dialog
  void _showUnlockRequestDialog(BuildContext context, DealModel deal, DealApprovalController controller) {
    final reasonController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: Text('Request Unlock for ${deal.dealNumber}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Please provide a reason for requesting unlock:'),
            const SizedBox(height: TSizes.spaceBtwItems),

            TextFormField(
              controller: reasonController,
              decoration: const InputDecoration(
                labelText: 'Unlock Reason *',
                border: OutlineInputBorder(),
                hintText: 'Explain why this deal needs to be unlocked...',
              ),
              maxLines: 3,
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              if (reasonController.text.isNotEmpty) {
                controller.requestUnlock(deal, reasonController.text);
                Get.back();
              }
            },
            child: const Text('Request Unlock'),
          ),
        ],
      ),
    );
  }

  /// Approve unlock request
  void _approveUnlockRequest(BuildContext context, DealModel deal, DealApprovalController controller) {
    Get.dialog(
      AlertDialog(
        title: Text('Approve Unlock Request'),
        content: Text('Are you sure you want to unlock deal ${deal.dealNumber} for editing?'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              controller.approveUnlockRequest(deal);
              Get.back();
            },
            child: const Text('Approve Unlock'),
          ),
        ],
      ),
    );
  }
}
