import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/personilization/screens/settings/responsive_screens/settings_desktop.dart';
import 'package:alloy/features/personilization/screens/settings/responsive_screens/settings_mobile.dart';
import 'package:alloy/features/personilization/screens/settings/responsive_screens/settings_tablet.dart';
import 'package:flutter/material.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(
      useLayout: true,
      desktop: SettingsDesktop(),
      tablet: SettingsTablet(),
      mobile: SettingsMobile(),
    );
  }
}
