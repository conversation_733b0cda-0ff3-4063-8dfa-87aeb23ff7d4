import 'package:flutter/material.dart';
import '../../../utils/constants/sizes.dart';

/// Enhanced dropdown with better styling, validation, and UX features
class TEnhancedDropdown<T> extends StatefulWidget {
  const TEnhancedDropdown({
    super.key,
    required this.labelText,
    required this.items,
    required this.value,
    required this.onChanged,
    this.hintText,
    this.helperText,
    this.prefixIcon,
    this.validator,
    this.enabled = true,
    this.isRequired = false,
    this.enableSearch = false,
    this.customErrorColor,
    this.itemBuilder,
    this.displayStringForOption,
  });

  final String labelText;
  final List<T> items;
  final T? value;
  final void Function(T?) onChanged;
  final String? hintText;
  final String? helperText;
  final IconData? prefixIcon;
  final String? Function(T?)? validator;
  final bool enabled;
  final bool isRequired;
  final bool enableSearch;
  final Color? customErrorColor;
  final Widget Function(T item)? itemBuilder;
  final String Function(T)? displayStringForOption;

  @override
  State<TEnhancedDropdown<T>> createState() => _TEnhancedDropdownState<T>();
}

class _TEnhancedDropdownState<T> extends State<TEnhancedDropdown<T>> {
  String? _errorText;
  bool _hasBeenTouched = false;

  void _validateField() {
    if (widget.validator != null) {
      final error = widget.validator!(widget.value);
      setState(() {
        _errorText = error;
      });
    }
  }

  String _getDisplayString(T item) {
    if (widget.displayStringForOption != null) {
      return widget.displayStringForOption!(item);
    }
    return item.toString();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator
        if (widget.labelText.isNotEmpty) ...[
          Row(
            children: [
              Text(
                widget.labelText,
                style: theme.textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (widget.isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: TSizes.xs),
        ],

        // Dropdown Field
        DropdownButtonFormField<T>(
          value: widget.value,
          onChanged: widget.enabled
              ? (value) {
                  setState(() {
                    _hasBeenTouched = true;
                  });
                  widget.onChanged(value);
                  if (_hasBeenTouched) {
                    _validateField();
                  }
                }
              : null,
          validator: widget.validator,
          items: widget.items.map((item) {
            return DropdownMenuItem<T>(
              value: item,
              child: widget.itemBuilder?.call(item) ?? 
                     Text(_getDisplayString(item)),
            );
          }).toList(),
          decoration: InputDecoration(
            hintText: widget.hintText,
            helperText: widget.helperText,
            prefixIcon: widget.prefixIcon != null ? Icon(widget.prefixIcon) : null,
            errorText: _errorText,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
              borderSide: BorderSide(
                color: theme.dividerColor,
                width: 1,
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
              borderSide: BorderSide(
                color: theme.primaryColor,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
              borderSide: BorderSide(
                color: widget.customErrorColor ?? theme.colorScheme.error,
                width: 1,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
              borderSide: BorderSide(
                color: widget.customErrorColor ?? theme.colorScheme.error,
                width: 2,
              ),
            ),
          ),
          isExpanded: true,
          icon: const Icon(Icons.keyboard_arrow_down),
        ),
      ],
    );
  }
}

/// Enhanced multi-select dropdown with chips display
class TEnhancedMultiSelectDropdown<T> extends StatefulWidget {
  const TEnhancedMultiSelectDropdown({
    super.key,
    required this.labelText,
    required this.items,
    required this.selectedItems,
    required this.onChanged,
    this.hintText,
    this.helperText,
    this.prefixIcon,
    this.validator,
    this.enabled = true,
    this.isRequired = false,
    this.maxHeight = 200,
    this.displayStringForOption,
    this.chipColor,
  });

  final String labelText;
  final List<T> items;
  final List<T> selectedItems;
  final void Function(List<T>) onChanged;
  final String? hintText;
  final String? helperText;
  final IconData? prefixIcon;
  final String? Function(List<T>?)? validator;
  final bool enabled;
  final bool isRequired;
  final double maxHeight;
  final String Function(T)? displayStringForOption;
  final Color? chipColor;

  @override
  State<TEnhancedMultiSelectDropdown<T>> createState() => 
      _TEnhancedMultiSelectDropdownState<T>();
}

class _TEnhancedMultiSelectDropdownState<T> extends State<TEnhancedMultiSelectDropdown<T>> {
  bool _isExpanded = false;
  String? _errorText;

  String _getDisplayString(T item) {
    if (widget.displayStringForOption != null) {
      return widget.displayStringForOption!(item);
    }
    return item.toString();
  }

  void _toggleSelection(T item) {
    if (!widget.enabled) return;

    final newSelection = List<T>.from(widget.selectedItems);
    if (newSelection.contains(item)) {
      newSelection.remove(item);
    } else {
      newSelection.add(item);
    }
    widget.onChanged(newSelection);
  }

  void _validateField() {
    if (widget.validator != null) {
      final error = widget.validator!(widget.selectedItems);
      setState(() {
        _errorText = error;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator
        if (widget.labelText.isNotEmpty) ...[
          Row(
            children: [
              Text(
                widget.labelText,
                style: theme.textTheme.labelLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (widget.isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: TextStyle(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: TSizes.xs),
        ],

        // Multi-select field
        GestureDetector(
          onTap: widget.enabled ? () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          } : null,
          child: Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(
                color: _errorText != null 
                    ? theme.colorScheme.error 
                    : theme.dividerColor,
              ),
              borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    if (widget.prefixIcon != null) ...[
                      Icon(widget.prefixIcon),
                      const SizedBox(width: 8),
                    ],
                    Expanded(
                      child: widget.selectedItems.isEmpty
                          ? Text(
                              widget.hintText ?? 'Select items...',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.hintColor,
                              ),
                            )
                          : Wrap(
                              spacing: 4,
                              runSpacing: 4,
                              children: widget.selectedItems.map((item) {
                                return Chip(
                                  label: Text(
                                    _getDisplayString(item),
                                    style: theme.textTheme.bodySmall,
                                  ),
                                  backgroundColor: widget.chipColor ?? 
                                      theme.primaryColor.withOpacity(0.1),
                                  deleteIcon: const Icon(Icons.close, size: 16),
                                  onDeleted: widget.enabled 
                                      ? () => _toggleSelection(item)
                                      : null,
                                );
                              }).toList(),
                            ),
                    ),
                    Icon(
                      _isExpanded 
                          ? Icons.keyboard_arrow_up 
                          : Icons.keyboard_arrow_down,
                    ),
                  ],
                ),
                
                // Dropdown items
                if (_isExpanded) ...[
                  const SizedBox(height: 8),
                  Container(
                    constraints: BoxConstraints(maxHeight: widget.maxHeight),
                    child: SingleChildScrollView(
                      child: Column(
                        children: widget.items.map((item) {
                          final isSelected = widget.selectedItems.contains(item);
                          return CheckboxListTile(
                            title: Text(_getDisplayString(item)),
                            value: isSelected,
                            onChanged: widget.enabled 
                                ? (bool? value) => _toggleSelection(item)
                                : null,
                            dense: true,
                            contentPadding: EdgeInsets.zero,
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),

        // Error text
        if (_errorText != null) ...[
          const SizedBox(height: 4),
          Text(
            _errorText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.error,
            ),
          ),
        ],

        // Helper text
        if (widget.helperText != null) ...[
          const SizedBox(height: 4),
          Text(
            widget.helperText!,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.hintColor,
            ),
          ),
        ],
      ],
    );
  }
}
