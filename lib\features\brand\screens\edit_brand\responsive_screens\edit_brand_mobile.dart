import 'package:flutter/material.dart';
import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/features/brand/screens/edit_brand/widgets/edit_brand_form.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/features/brand/models/brand_model.dart';

class EditBrandMobile extends StatelessWidget {
  const EditBrandMobile({super.key, required this.brand});
  final BrandModel brand;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Update Brand',
                breadcrumbItems: [
                  TBreadcrumbItem(text: 'Brands', route: TRoutes.brands),
                  TBreadcrumbItem(text: 'Update'),
                ],
                showBackButton: true,
              ),
              SizedBox(height: TSizes.spaceBtwSections),

              // Form
              EditBrandForm(brand: brand),
            ],
          ),
        ), // Padding
      ), // SingleChildScrollView
    ); // Scaffold
  }
}
