import 'package:alloy/features/account/models/account_model.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ContactModel {
  String id;
  String? title;
  String name;
  String? email;
  String phone; // Corresponds to mobile
  String? landline;
  String? designation;
  bool active;
  DateTime? createdAt;
  DateTime? updatedAt;

  // --- Denormalized Data (Stored in Firestore) ---
  // These should be lists of simplified maps or direct models
  List<AccountModel>? accountDetails; // Updated to be List<AccountModel> for consistency with AccountModel
  List<UserModel>? handlerDetails; // Updated to be List<UserModel> for consistency with AccountModel

  ContactModel({
    required this.id,
    this.title,
    required this.name,
    this.email,
    required this.phone,
    this.landline,
    this.designation,
    required this.active,
    this.createdAt,
    this.updatedAt,
    this.accountDetails,
    this.handlerDetails,
  });

  /// Empty Helper Function
  static ContactModel empty() => ContactModel(id: '', name: '', phone: '', active: true, createdAt: DateTime.now());

  /// Convert model to JSON object
  Map<String, dynamic> toJson() {
    return {
      'title': title?.trim(),
      'name': name,
      'designation': designation?.trim(),
      'phone': phone.trim(),
      'landline': landline?.trim(),
      'email': email?.trim(),
      'active': active,
      'accountDetails': accountDetails?.map((e) => e.toSubJson()).toList(), // Use toSubJson
      'handlerDetails': handlerDetails?.map((e) => e.toSubJson()).toList(), // Use toSubJson
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  // toSubJson method for ContactModel (used when ContactModel is embedded)
  Map<String, dynamic> toSubJson() {
    return {
      'id': id,
      'name': name,
      // Include other essential fields for sub-JSON if needed, e.g., 'email': email, 'phone': phone
    };
  }

  /// Factory method to create a ContactModel from a Firestore document snapshot
  factory ContactModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (document.data() == null) return ContactModel.empty();
    return ContactModel.fromMap(document.data()!, id: document.id);
  }

  /// Factory method to create a ContactModel from a map.
  factory ContactModel.fromMap(Map<String, dynamic> data, {String? id}) {
    return ContactModel(
      id: id ?? data['id'] ?? '',
      title: data['title'],
      name: data['name'] ?? '',
      designation: data['designation'],
      phone: data['phone'] ?? '',
      landline: data['landline'],
      email: data['email'],
      active: data['active'] ?? false,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      accountDetails: data['accountDetails'] is List
          ? (data['accountDetails'] as List<dynamic>)
                .map(
                  (e) => AccountModel.fromMap(e as Map<String, dynamic>, id: e['id'] as String?),
                ) // Make sure AccountModel also correctly takes its ID from 'e'
                .toList()
          : [],
      handlerDetails: data['handlerDetails'] is List
          ? (data['handlerDetails'] as List<dynamic>)
                .map(
                  (e) => UserModel.fromMap(e as Map<String, dynamic>, id: e['id'] as String?),
                ) // <-- CRITICAL FIX HERE!
                .toList()
          : [],
    );
  }

  ContactModel copyWith({
    String? id,
    String? title,
    String? name,
    String? email,
    String? phone,
    String? landline,
    String? designation,
    bool? active,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<AccountModel>? accountDetails,
    List<UserModel>? handlerDetails,
  }) {
    return ContactModel(
      id: id ?? this.id,
      title: title ?? this.title,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      landline: landline ?? this.landline,
      designation: designation ?? this.designation,
      active: active ?? this.active,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      accountDetails: accountDetails ?? this.accountDetails,
      handlerDetails: handlerDetails ?? this.handlerDetails,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is ContactModel && runtimeType == other.runtimeType && id == other.id; // Compare by unique ID

  @override
  int get hashCode => id.hashCode; // Use ID for hash code
}
