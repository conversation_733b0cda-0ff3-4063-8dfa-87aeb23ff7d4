import 'package:alloy/common/media/controller/media_controller.dart';
import 'package:alloy/common/media/models/image_model.dart';
import 'package:get/get.dart';

class ProductImagesController extends GetxController {
  static ProductImagesController get instance => Get.find();

  // Rx Observables for the selected thumbnail image
  Rx<String?> selectedThumbnailImageUrl = Rx<String?>(null);

  // Lists to store additional product images
  final RxList<String> additionalProductImagesUrls = <String>[].obs;

  // Pick Thumbnail Image from Media
  void selectThumbnailImage() async {
    final controller = Get.put(MediaController());
    List<ImageModel>? selectedImages = await controller.selectImagesFromMedia();

    // Handle the selected images
    if (selectedImages != null && selectedImages.isNotEmpty) {
      // Select the first image from the list if selected images (to make a thumbnail)
      ImageModel selectedImage = selectedImages.first;
      // Update the selected thumbnail image URL
      selectedThumbnailImageUrl.value = selectedImage.url;
    }
  }

  // Pick Multiple Images from Media
  void selectMultipleProductImages() async {
    // made a new instance of MediaController just in case its not already initialized
    final controller = Get.put(MediaController());
    // Also Pass the already selected images (additionalProductImagesUrls) if any to the media controller
    List<ImageModel>? selectedImages = await controller.selectImagesFromMedia(multipleSelection: true, selectedUrls: additionalProductImagesUrls);

    // Assign new images to the productimages list
    if (selectedImages != null && selectedImages.isNotEmpty) {
      additionalProductImagesUrls.assignAll(selectedImages.map((image) => image.url));
    }
  }

  // Select Variation Image from Media
  // void selectVariationImage(ProductVariationModel variation) async {
  //   final controller = Get.put(MediaController());
  //   List<ImageModel>? selectedImages = await controller.selectImagesFromMedia();

  //   // Assign new images to the productimages list
  //   if (selectedImages != null && selectedImages.isNotEmpty) {
  //     // Select the first image from the list if selected images (to make a thumbnail)
  //     ImageModel selectedImage = selectedImages.first;
  //     // Update the selected thumbnail image URL
  //     variation.image.value = selectedImage.url;
  //   }
  // }

  /// Function to remove Product image
  Future<void> removeImage(int index) async {
    additionalProductImagesUrls.removeAt(index);
  }
}
