import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/deals/screens/all_deals/responsive_screens/deals_desktop.dart';
import 'package:alloy/features/deals/screens/all_deals/responsive_screens/deals_mobile.dart';
import 'package:alloy/features/deals/screens/all_deals/responsive_screens/deals_tablet.dart';
import 'package:flutter/material.dart';

class DealsScreen extends StatelessWidget {
  const DealsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(
      desktop: DealsDesktop(), 
      tablet: DealsTablet(), 
      mobile: DealsMobile()
    );
  }
}
