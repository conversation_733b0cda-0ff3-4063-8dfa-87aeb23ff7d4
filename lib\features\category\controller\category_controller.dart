import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:alloy/data/abstract/base_data_table_controller.dart';
import 'package:alloy/features/category/models/category_model.dart';
import 'package:alloy/features/category/repository/category_repository.dart';

class CategoryController extends TBaseController<CategoryModel> {
  static CategoryController get instance => Get.find();

  final _categoryRepository = Get.put(CategoryRepository());

  final categoryName = TextEditingController();

  Future<void> createCategory() async {
    try {
      if (categoryName.text.trim().isEmpty) {
        TLoaders.warningSnackBar(title: 'Name is required', message: 'Please enter a category name.');
        return;
      }

      final newCategory = CategoryModel(id: '', name: categoryName.text.trim());
      await _categoryRepository.create(newCategory);

      categoryName.clear();
      TLoaders.successSnackBar(title: 'Success', message: 'Category has been created.');
      if (Get.isDialogOpen!) Get.back();
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  @override
  bool containsSearchQuery(CategoryModel item, String searchText) {
    return item.name.toLowerCase().contains(searchText.toLowerCase());
  }

  @override
  Future<void> deleteItem(CategoryModel item) {
    return _categoryRepository.deleteCategory(item.id);
  }

  @override
  Future<List<CategoryModel>> fetchItems() async {
    return await _categoryRepository.getAllCategories();
  }

  /// Sorting related code
  void sortByName(int sortColumnIndex, bool isAscending) {
    sortByProperty(sortColumnIndex, isAscending, (item) => item.name.toLowerCase());
  }

  @override
  Stream<List<CategoryModel>> streamItems() {
    return _categoryRepository.streamAllCategories();
  }

  @override
  Comparable getComparableProperty(CategoryModel item, int columnIndex) {
    // Implement this method to return a Comparable value for sorting based on column index
    // Ensure you always return a String, int, double, or DateTime.
    // Handle null values by providing a default comparable value (e.g., empty string, 0).
    switch (columnIndex) {
      case 0:
        return item.name.toLowerCase(); // Name
      case 1:
        return item.description?.toLowerCase() ?? ''; // Description (nullable)
      case 2:
        return ''; // Action column is not sortable
      default:
        return ''; // Fallback for undefined columns
    }
  }
}
