import 'package:alloy/features/authentication/repository/authentication_repository.dart';
import 'package:alloy/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class LogoutButton extends StatelessWidget {
  const LogoutButton({super.key});

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: 'Logout',
      child: IconB<PERSON>on(
        icon: const Icon(Iconsax.logout),
        onPressed: () async {
          // Perform logout
          await AuthenticationRepository.instance.logout();
          // Redirect to login screen after logout
          Get.offAllNamed(TRoutes.login);
        },
      ),
    );
  }
}
