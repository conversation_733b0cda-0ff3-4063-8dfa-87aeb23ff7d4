import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/utils/constants/enums.dart';

class AccountModel {
  String id;
  String? parentId; // From your original model
  String name;
  String? alsoKnownAs; // From your original model
  BusinessType businessType; // From your original model
  PriorityLevel priority; // From your original model

  // These fields will be denormalized from the default billing address
  // and will be automatically updated when the default billing address changes.
  String? country;
  String? emirates;

  // NEW: Email field for the account
  String? email;

  List<String>? industry; // From your original model
  String? phone; // From your original model
  String? paymentTerms; // From your original model
  AccountStatus status; // From your original model

  // NEW: Flag to indicate if this account can be a parent to other accounts
  bool isParent;

  // List of IDs referencing AddressModel documents in the 'Addresses' collection
  List<String> addressIds;

  DateTime? createdAt;
  DateTime? updatedAt;
  String createdByUserId;
  String? updatedByUserId;

  // --- Mapped in Firestore (Denormalized) ---
  // These will be stored as sub-JSON objects within the account document
  List<UserModel>? handlerDetails; // Denormalized handler user details
  List<ContactModel>? contactDetails; // Denormalized contact details

  AccountModel({
    required this.id,
    required this.name,
    this.parentId,
    this.alsoKnownAs,
    this.businessType = BusinessType.customer, // Default value
    this.priority = PriorityLevel.medium, // Default value
    this.country, // Now nullable and not required
    this.emirates, // Now nullable and not required
    this.email, // NEW: Add to constructor
    this.industry,
    this.phone,
    this.paymentTerms,
    this.status = AccountStatus.active, // Default value
    this.isParent = false, // NEW: Default to false
    this.addressIds = const [], // Initialize as empty list of IDs
    this.createdAt,
    this.updatedAt,
    required this.createdByUserId,
    this.updatedByUserId,
    this.handlerDetails, // Now part of the constructor for mapping
    this.contactDetails, // Now part of the constructor for mapping
  });

  /// Empty Helper Function
  static AccountModel empty() => AccountModel(
    id: '',
    name: '',
    country: null, // Default to null
    emirates: null, // Default to null
    email: null, // NEW: Default to null
    createdByUserId: '',
    isParent: false, // Ensure default is set for empty
    addressIds: const [], // Ensure empty list
    handlerDetails: [], // Initialize as empty list
    contactDetails: [], // Initialize as empty list
  );

  /// NEW: Getter to provide a formatted phone number.
  /// You can customize the formatting logic here.
  String get formattedPhoneNo {
    if (phone == null || phone!.isEmpty) {
      return '';
    }
    // Example: Basic formatting for a 10-digit number (e.g., (*************)
    // This is a placeholder; real-world scenarios need robust internationalization.
    String cleanedPhone = phone!.replaceAll(RegExp(r'\D'), ''); // Remove non-digits
    if (cleanedPhone.length == 10) {
      return '(${cleanedPhone.substring(0, 3)}) ${cleanedPhone.substring(3, 6)}-${cleanedPhone.substring(6)}';
    } else if (cleanedPhone.length > 10) {
      // For longer numbers, maybe add a '+' for international, or just return as is
      return '+${cleanedPhone.substring(0, 3)} ${cleanedPhone.substring(3)}';
    }
    return phone!; // Return as is if no specific format matches
  }

  /// Convert AccountModel to JSON format for Firestore.
  Map<String, dynamic> toJson() {
    return {
      'parentId': parentId,
      'name': name,
      'alsoKnownAs': alsoKnownAs,
      'businessType': businessType.name, // Convert enum to string
      'priority': priority.name, // Convert enum to string
      'country': country, // Store nullable country
      'emirates': emirates, // Store nullable emirates
      'email': email, // NEW: Store email
      'industry': industry,
      'phone': phone,
      'paymentTerms': paymentTerms,
      'status': status.name, // Convert enum to string
      'isParent': isParent, // Store isParent flag
      'addressIds': addressIds, // Store the list of address IDs
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
      'createdByUserId': createdByUserId,
      'updatedByUserId': updatedByUserId,
      'handlerDetails': handlerDetails?.map((e) => e.toSubJson()).toList(),
      'contactDetails': contactDetails?.map((e) => e.toSubJson()).toList(),
    };
  }

  /// Factory method to create an AccountModel from a Firestore DocumentSnapshot.
  factory AccountModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (!document.exists || document.data() == null) {
      return AccountModel.empty();
    }

    final data = document.data()!;
    return AccountModel.fromMap(data, id: document.id); // Use the fromMap constructor
  }

  /// NEW: Factory method to create an AccountModel from a map (used for denormalized data).
  factory AccountModel.fromMap(Map<String, dynamic> data, {String? id}) {
    // Parse enums safely
    BusinessType parsedBusinessType = BusinessType.customer;
    if (data['businessType'] != null) {
      parsedBusinessType = BusinessType.values.firstWhere(
        (e) => e.name == data['businessType'],
        orElse: () => BusinessType.customer,
      );
    }

    PriorityLevel parsedPriority = PriorityLevel.medium;
    if (data['priority'] != null) {
      parsedPriority = PriorityLevel.values.firstWhere(
        (e) => e.name == data['priority'],
        orElse: () => PriorityLevel.medium,
      );
    }

    AccountStatus parsedStatus = AccountStatus.active;
    if (data['status'] != null) {
      parsedStatus = AccountStatus.values.firstWhere(
        (e) => e.name == data['status'],
        orElse: () => AccountStatus.active,
      );
    }

    return AccountModel(
      id: id ?? data['id'] ?? '', // Use provided ID or from map, fallback to empty
      parentId: data['parentId'],
      name: data['name'] ?? '',
      alsoKnownAs: data['alsoKnownAs'],
      businessType: parsedBusinessType,
      priority: parsedPriority,
      country: data['country'],
      emirates: data['emirates'],
      email: data['email'],
      industry: (data['industry'] as List<dynamic>?)?.map((e) => e.toString()).toList(),
      phone: data['phone'],
      paymentTerms: data['paymentTerms'],
      status: parsedStatus,
      isParent: data['isParent'] ?? false,
      addressIds: (data['addressIds'] as List<dynamic>?)?.map((id) => id.toString()).toList() ?? const [],
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      createdByUserId: data['createdByUserId'] ?? '',
      updatedByUserId: data['updatedByUserId'],
      handlerDetails: data['handlerDetails'] is List
          ? (data['handlerDetails'] as List<dynamic>)
                .whereType<Map<String, dynamic>>()
                .map((e) => UserModel.fromMap(e, id: e['id'] as String?)) // <--- Apply the fix here!
                .toList()
          : [],
      contactDetails: data['contactDetails'] is List
          ? (data['contactDetails'] as List<dynamic>)
                .whereType<Map<String, dynamic>>()
                .map(
                  (e) => ContactModel.fromMap(e, id: e['id'] as String?),
                ) // <--- And potentially here for ContactModel if 'id' is important for its subJson
                .toList()
          : [],
    );
  }

  /// CopyWith method for immutability and easy updates
  AccountModel copyWith({
    String? id,
    String? parentId,
    String? name,
    String? alsoKnownAs,
    BusinessType? businessType,
    PriorityLevel? priority,
    String? country,
    String? emirates,
    String? email, // NEW: Add to copyWith
    List<String>? industry,
    String? phone,
    String? paymentTerms,
    AccountStatus? status,
    bool? isParent,
    List<String>? addressIds,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByUserId,
    String? updatedByUserId,
    List<UserModel>? handlerDetails,
    List<ContactModel>? contactDetails,
  }) {
    return AccountModel(
      id: id ?? this.id,
      parentId: parentId ?? this.parentId,
      name: name ?? this.name,
      alsoKnownAs: alsoKnownAs ?? this.alsoKnownAs,
      businessType: businessType ?? this.businessType,
      priority: priority ?? this.priority,
      country: country ?? this.country,
      emirates: emirates ?? this.emirates,
      email: email ?? this.email, // NEW: Copy email
      industry: industry ?? this.industry,
      phone: phone ?? this.phone,
      paymentTerms: paymentTerms ?? this.paymentTerms,
      status: status ?? this.status,
      isParent: isParent ?? this.isParent,
      addressIds: addressIds ?? this.addressIds,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByUserId: createdByUserId ?? this.createdByUserId,
      updatedByUserId: updatedByUserId ?? this.updatedByUserId,
      handlerDetails: handlerDetails ?? this.handlerDetails,
      contactDetails: contactDetails ?? this.contactDetails,
    );
  }

  /// Converts a subset of AccountModel to JSON for denormalization into ContactModel.
  /// This is used when linking accounts to contacts.
  Map<String, dynamic> toSubJson() {
    return {
      'id': id,
      'name': name,
      'businessType': businessType.name,
      'status': status.name,
      // Add any other fields you want to denormalize into the contact's accountDetails
    };
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) || other is AccountModel && runtimeType == other.runtimeType && id == other.id; // Compare by ID

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AccountModel(id: $id, name: $name, businessType: ${businessType.name}, country: $country, emirates: $emirates, isParent: $isParent, email: $email)';
  }
}
