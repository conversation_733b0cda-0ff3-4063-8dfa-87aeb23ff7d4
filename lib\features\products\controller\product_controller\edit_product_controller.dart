import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../data/repositories/storage_repository.dart';
import '../../../../utils/constants/enums.dart';
import '../../../../utils/helpers/network_manager.dart';
import '../../../../utils/popups/full_screen_loader.dart';
import '../../../../utils/popups/loaders.dart';
import '../../../category/controller/category_controller.dart';
import '../../models/product_model.dart';
import '../../repository/product_repository.dart';
import 'product_controller.dart';

/// Controller for editing an existing ProductModel.
class EditProductController extends GetxController {
  final _productRepository = Get.put(ProductRepository());
  final _categoryController = Get.put(CategoryController());
  final _storageRepository = Get.put(StorageRepository()); // StorageRepository
  final ProductController _productController = Get.find<ProductController>(); // To refresh product list

  // --- Form State
  final formKey = GlobalKey<FormState>();
  final isLoading = false.obs;

  // --- Product Model being edited
  final ProductModel initialProduct; // The product passed to the controller

  // --- TextEditingControllers for form fields
  late TextEditingController name;
  late TextEditingController description;
  late TextEditingController width;
  late TextEditingController height;

  // --- Rx variables for dropdowns
  late RxString selectedCategoryId;
  late Rx<ProductSegment> selectedSegment;
  late Rx<ProductStatus> selectedStatus; // NEW: Reactive variable for product status

  // Thumbnail (for image upload)
  final RxString thumbnailPath = ''.obs; // Stores local path of selected image or uploaded URL
  final Rxn<XFile> _selectedXFile = Rxn<XFile>(); // NEW: Store the XFile for later upload

  EditProductController({required this.initialProduct});

  @override
  void onInit() {
    super.onInit();
    // Initialize controllers and Rx variables with the initial product's data
    name = TextEditingController(text: initialProduct.name);
    description = TextEditingController(text: initialProduct.description);
    width = TextEditingController(text: initialProduct.width.toString());
    height = TextEditingController(text: initialProduct.height.toString());
    selectedCategoryId = initialProduct.categoryId.obs;
    selectedSegment = initialProduct.segment.obs;
    selectedStatus = initialProduct.status.obs; // NEW: Initialize status
    thumbnailPath.value = initialProduct.thumbnail ?? ''; // Initialize thumbnail path

    // Ensure categories are loaded for the dropdown
    if (_categoryController.allItems.isEmpty) {
      _categoryController.fetchItems();
    }
  }

  @override
  void onClose() {
    name.dispose();
    description.dispose();
    width.dispose();
    height.dispose();
    super.onClose();
  }

  /// --- Update Product ---
  Future<void> updateProduct() async {
    try {
      // Start Loading
      TFullScreenLoader.popUpCircular();

      // Check Internet Connectivity
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Form Validation
      if (!formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'Validation Error', message: 'Please correct the errors in the form.');
        return;
      }

      // Basic validation for dropdowns
      if (selectedCategoryId.value.isEmpty) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'Category Required', message: 'Please select a category for the product.');
        return;
      }

      // Corrected: Handle Thumbnail Image Upload/Update
      String? updatedThumbnailUrl;
      if (_selectedXFile.value != null) {
        // A new image was picked
        // Delete old thumbnail if a new one is being uploaded and an old one exists
        if (initialProduct.thumbnail != null && initialProduct.thumbnail!.isNotEmpty) {
          await _storageRepository.deleteFileByUrl(initialProduct.thumbnail!);
        }

        if (kIsWeb) {
          final bytes = await _selectedXFile.value!.readAsBytes();
          updatedThumbnailUrl = await _storageRepository.uploadFile(
            'Products/Thumbnails/${initialProduct.id}_${DateTime.now().millisecondsSinceEpoch}_${_selectedXFile.value!.name}',
            bytes, // Pass bytes for web
          );
        } else {
          final file = File(_selectedXFile.value!.path);
          if (await file.exists()) {
            updatedThumbnailUrl = await _storageRepository.uploadFile(
              'Products/Thumbnails/${initialProduct.id}_${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}',
              file, // Pass File for non-web
            );
          } else {
            TLoaders.warningSnackBar(title: 'Invalid Image', message: 'Selected image file not found.');
            TFullScreenLoader.stopLoading();
            return;
          }
        }
      } else {
        // No new image picked, check if existing one was cleared
        if (initialProduct.thumbnail != null && initialProduct.thumbnail!.isNotEmpty && thumbnailPath.value.isEmpty) {
          // Thumbnail was cleared by user
          await _storageRepository.deleteFileByUrl(initialProduct.thumbnail!);
          updatedThumbnailUrl = null;
        } else {
          // No new image, and existing one was not cleared, so keep the old URL
          updatedThumbnailUrl = initialProduct.thumbnail;
        }
      }

      // Create the updated Product Model
      final updatedProduct = initialProduct.copyWith(
        name: name.text.trim(),
        description: description.text.trim(),
        categoryId: selectedCategoryId.value,
        segment: selectedSegment.value,
        status: selectedStatus.value,
        width: double.tryParse(width.text) ?? 0.0,
        height: double.tryParse(height.text) ?? 0.0,
        thumbnail: updatedThumbnailUrl, // Assign the uploaded URL or null
      );

      // Update product in Firestore via ProductRepository
      await _productRepository.updateProduct(updatedProduct);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Product "${updatedProduct.name}" updated successfully!');

      // Refresh the main product list (if on the main products screen)
      _productController.listenToStream();
      Get.back(); // Go back to the previous screen (e.g., ProductsDesktop)
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  /// Picks an image from gallery or camera and updates thumbnailPath.
  Future<void> pickAndUploadThumbnail() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(source: ImageSource.gallery, imageQuality: 70);

      if (pickedFile != null) {
        thumbnailPath.value = pickedFile.path; // Store local path temporarily
        TLoaders.successSnackBar(title: 'Image Selected', message: 'Thumbnail image selected successfully.');
      } else {
        TLoaders.warningSnackBar(title: 'No Image Selected', message: 'No image was selected for the thumbnail.');
      }
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: 'Failed to pick image: $e');
      TLoaders.warningSnackBar(title: 'Feature Coming Soon', message: 'Image upload for product thumbnail is not yet implemented.');
    }
  }
}
