import 'package:alloy/features/authentication/repository/authentication_repository.dart';
import 'package:alloy/routes/routes.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class TRouteMiddleWare extends GetMiddleware {
  @override
  RouteSettings? redirect(String? route) {
    // Don't redirect if already on login page to avoid redirect loop
    if (route == TRoutes.login) {
      return null;
    }

    return AuthenticationRepository.instance.isAuthenticated ? null : const RouteSettings(name: TRoutes.login);
  }
}
