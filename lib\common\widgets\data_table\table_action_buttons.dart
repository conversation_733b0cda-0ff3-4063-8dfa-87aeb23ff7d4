import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';

class TTableActionButtons extends StatelessWidget {
  const TTableActionButtons({super.key, this.onEditPressed, this.onDeletePressed});

  final VoidCallback? onEditPressed;
  final VoidCallback? onDeletePressed;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        IconButton(onPressed: onEditPressed, icon: const Icon(Iconsax.edit)),
        IconButton(onPressed: onDeletePressed, icon: const Icon(Iconsax.trash)),
      ],
    );
  }
}
