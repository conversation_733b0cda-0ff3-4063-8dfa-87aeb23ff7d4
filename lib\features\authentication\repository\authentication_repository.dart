import 'package:alloy/common/layouts/sidebars/sidebar_controller.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:alloy/features/authentication/repository/user_repository.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/exceptions/firebase_auth_exceptions.dart';
import 'package:alloy/utils/exceptions/firebase_exceptions.dart';
import 'package:alloy/utils/exceptions/format_exceptions.dart';
import 'package:alloy/utils/exceptions/platform_exceptions.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class AuthenticationRepository extends GetxController {
  static AuthenticationRepository get instance => Get.find();

  // Firebase Auth Instance
  final _auth = FirebaseAuth.instance;

  // Get Authenticated User
  User? get authUser => _auth.currentUser;

  // Get IsAuthenticated
  bool get isAuthenticated => authUser != null;

  @override
  void onReady() {
    // Set persistence will ensure that the user is logged in even if the app is closed and opened again
    _auth.setPersistence(Persistence.LOCAL);

    // in case menu item is not highlighted
    // screenRedirect();
  }

  // Function to determine the relavent screen is redirected accordinly.
  void screenRedirect() async {
    // Start Loader
    // TFullScreenLoader.openLoadingDialog('Logging In', TImages.defaultLoaderAnimation);
    TFullScreenLoader.popUpCircular();

    final user = _auth.currentUser;
    // in case menu item is not highlighted
    final SidebarController sidebarController = Get.find<SidebarController>(); // Get the controller
    if (user != null) {
      // Fetch User Data
      final user = await UserController.instance.getUserData();

      // Stop Loader
      TFullScreenLoader.stopLoading();

      // Redirect based on user role
      if (user.roles.contains(UserRole.Admin) || user.roles == AppRole.admin) {
        sidebarController.changeActiveItemTo(TRoutes.dashboard); // Set active item
        Get.offAllNamed(TRoutes.dashboard); // Redirect admin to Dashboard
      } else {
        sidebarController.changeActiveItemTo(TRoutes.profile); // Set active item
        Get.offAllNamed(TRoutes.profile); // Redirect non-admin to Profile Screen
      }
    } else {
      //  If redirecting to login, ensure sidebar doesn't show a highlighted item
      //  sidebarController.changeActiveItemTo(TRoutes.login);
      Get.offAllNamed(TRoutes.login);
    }
  }

  // Login
  Future<UserCredential> loginWithEmailAndPassword(String email, String password) async {
    try {
      return await _auth.signInWithEmailAndPassword(email: email, password: password);
    } on FirebaseAuthException catch (e) {
      throw TFirebaseAuthException(e.code).message;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  ///  - REGISTER
  Future<UserCredential> registerWithEmailAndPassword(String email, String password, UserModel userModel) async {
    try {
      // 1. Create user in Firebase Authentication
      UserCredential userCredential = await _auth.createUserWithEmailAndPassword(email: email, password: password);

      // 2. Save additional user data to Firestore
      if (userCredential.user != null) {
        // Populate the ID for the userModel from the auth user
        final newUserModel = UserModel(
          id: userCredential.user!.uid,
          email: userModel.email,
          firstName: userModel.firstName,
          lastName: userModel.lastName,
          userName: userModel.userName,
          phoneNumber: userModel.phoneNumber,
          profilePicture: userModel.profilePicture, // Can be empty or a default
          department: userModel.department,
          roles: userModel.roles,
          // createdAt and updatedAt will be handled by FieldValue.serverTimestamp()
        );
        //  await saveUserRecord(newUserModel, userCredential.user!.uid);
        await UserRepository.instance.createUser(newUserModel);
      }
      return userCredential;
    } on FirebaseAuthException catch (e) {
      throw TFirebaseAuthException(e.code).message;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  // /// Helper to save user data to Firestore
  // Future<void> saveUserRecord(UserModel user, String userId) async {
  //   try {
  //     final userJson = user.toJson();
  //     // Ensure timestamps are set for Firestore
  //     userJson['createdAt'] = FieldValue.serverTimestamp();
  //     userJson['updatedAt'] = FieldValue.serverTimestamp();

  //     await _db.collection(TTexts.users).doc(userId).set(userJson);
  //   } on FirebaseException catch (e) {
  //     throw TFirebaseException(e.code).message;
  //   } catch (e) {
  //     throw 'Something went wrong while saving user information. Please try again.';
  //   }
  // }

  // Logout
  Future<void> logout() async {
    try {
      await _auth.signOut();
      Get.offAllNamed(TRoutes.login);
    } on FirebaseAuthException catch (e) {
      throw TFirebaseAuthException(e.code).message;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }
}
