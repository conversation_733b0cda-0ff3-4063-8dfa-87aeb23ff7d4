import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/authentication/repository/authentication_repository.dart';
import 'package:alloy/features/authentication/controllers/login_controller.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/features/authentication/screens/login/responsive_screens/login_screen_desktop_tablet.dart';
import 'package:alloy/features/authentication/screens/login/responsive_screens/login_screen_mobile.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class LoginScreen extends GetView<LoginController> {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Check if user is already authenticated and is admin
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _checkAuthAndRedirect();
    // });

    return const TSiteTemplate(useLayout: false, desktop: LoginScreenDesktopTablet(), mobile: LoginScreenMobile());
  }

  void _checkAuthAndRedirect() async {
    final authRepo = AuthenticationRepository.instance;

    // If user is already authenticated
    if (authRepo.isAuthenticated) {
      try {
        final userController = UserController.instance;
        final user = await userController.getUserData();

        // If user is admin, redirect to dashboard
        if (user.roles.contains(UserRole.Admin)) {
          Get.offAllNamed(TRoutes.dashboard);
        }
      } catch (e) {
        // Handle error silently - stay on login page
        print('Error checking admin status: $e');
      }
    }
  }
}
