import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

// Assuming these imports are correct for your project
import '../../../../common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import '../../../../common/widgets/containers/rounded_container.dart'; // TRoundedContainer
import '../../../../utils/constants/enums.dart';
import '../../../../utils/constants/sizes.dart'; // TSizes
import '../../../../utils/validators/validation.dart'; // TValidator
import '../../../../routes/routes.dart'; // TRoutes
import '../../controller/variant_controller/edit_product_variant_controller.dart';
import '../../controller/variant_controller/product_variant_manager_controller.dart';
import '../../models/product_model.dart'; // ProductModel
import '../../models/product_variant_model.dart'; // ProductVariantModel

class EditProductVariantScreen extends StatelessWidget {
  const EditProductVariantScreen({super.key, required this.variant, required this.parentProduct});

  final ProductVariantModel variant;
  final ProductModel parentProduct;

  @override
  Widget build(BuildContext context) {
    // Initialize the EditProductVariantController with the variant and its parent product
    final controller = Get.put(EditProductVariantController(initialVariant: variant, parentProduct: parentProduct));
    // We need ProductVariantManagerController for the list of available attribute options (e.g., all materials)
    final managerController = Get.find<ProductVariantManagerController>();

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              TBreadcrumbsWithHeading(
                heading: 'Edit Variant: ${variant.sku}',
                breadcrumbItems: [
                  TBreadcrumbItem(text: 'Products', route: TRoutes.products),
                  TBreadcrumbItem(
                    text: parentProduct.name,
                    route: '${TRoutes.products}/${parentProduct.id}',
                  ), // Link back to parent product
                  TBreadcrumbItem(text: 'Edit Variant'),
                ],
                showBackButton: true,
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              TRoundedContainer(
                padding: const EdgeInsets.all(TSizes.defaultSpace),
                child: Form(
                  key: controller.formKey, // Use the formKey from EditProductVariantController
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Variant Details', style: Theme.of(context).textTheme.headlineSmall),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // SKU
                      TextFormField(
                        controller: controller.sku,
                        decoration: const InputDecoration(labelText: 'SKU', prefixIcon: Icon(Iconsax.barcode)),
                        validator: (value) => TValidator.validateEmptyText('SKU', value),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // NEW: Variant Status Dropdown
                      Obx(
                        () => DropdownButtonFormField<ProductStatus>(
                          value: controller.selectedStatus.value,
                          decoration: const InputDecoration(labelText: 'Status', prefixIcon: Icon(Iconsax.activity)),
                          items: ProductStatus.values.map((status) {
                            return DropdownMenuItem(value: status, child: Text(status.name));
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedStatus.value = value;
                            }
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'Please select a status.';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Attributes Selection (using AttributeSelectionWidget, but as single select dropdowns)
                      Text('Attributes', style: Theme.of(context).textTheme.titleMedium),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Material Dropdown
                      Obx(
                        () => DropdownButtonFormField<String>(
                          value: controller.selectedMaterial.value.isEmpty ? null : controller.selectedMaterial.value,
                          decoration: const InputDecoration(labelText: 'Material', prefixIcon: Icon(Iconsax.box)),
                          items: managerController.materialOptions.map((option) {
                            return DropdownMenuItem(value: option, child: Text(option));
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) controller.selectedMaterial.value = value;
                          },
                          validator: (value) => TValidator.validateEmptyText('Material', value),
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Thickness Dropdown
                      Obx(
                        () => DropdownButtonFormField<String>(
                          value: controller.selectedThickness.value.isEmpty ? null : controller.selectedThickness.value,
                          decoration: const InputDecoration(labelText: 'Thickness', prefixIcon: Icon(Iconsax.ruler)),
                          items: managerController.thicknessOptions.map((option) {
                            return DropdownMenuItem(value: option, child: Text(option));
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) controller.selectedThickness.value = value;
                          },
                          validator: (value) => TValidator.validateEmptyText('Thickness', value),
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Finish Dropdown
                      Obx(
                        () => DropdownButtonFormField<String>(
                          value: controller.selectedFinish.value.isEmpty ? null : controller.selectedFinish.value,
                          decoration: const InputDecoration(
                            labelText: 'Finish',
                            prefixIcon: Icon(Iconsax.color_swatch),
                          ),
                          items: managerController.finishOptions.map((option) {
                            return DropdownMenuItem(value: option, child: Text(option));
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) controller.selectedFinish.value = value;
                          },
                          validator: (value) => TValidator.validateEmptyText('Finish', value),
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Length Dropdown
                      Obx(
                        () => DropdownButtonFormField<String>(
                          value: controller.selectedLength.value.isEmpty ? null : controller.selectedLength.value,
                          decoration: const InputDecoration(labelText: 'Length', prefixIcon: Icon(Iconsax.ruler)),
                          items: managerController.lengthOptions.map((option) {
                            return DropdownMenuItem(value: option, child: Text(option));
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) controller.selectedLength.value = value;
                          },
                          validator: (value) => TValidator.validateEmptyText('Length', value),
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Quantities
                      Text('Quantities', style: Theme.of(context).textTheme.titleMedium),
                      const SizedBox(height: TSizes.spaceBtwInputFields),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: controller.quantityOnHand,
                              decoration: const InputDecoration(labelText: 'On Hand'),
                              keyboardType: TextInputType.number,
                              validator: TValidator.validateNumber,
                            ),
                          ),
                          const SizedBox(width: TSizes.spaceBtwInputFields),
                          Expanded(
                            child: TextFormField(
                              controller: controller.quantityOnOrder,
                              decoration: const InputDecoration(labelText: 'On Order'),
                              keyboardType: TextInputType.number,
                              validator: TValidator.validateNumber,
                            ),
                          ),
                          const SizedBox(width: TSizes.spaceBtwInputFields),
                          Expanded(
                            child: TextFormField(
                              controller: controller.quantityInProduction,
                              decoration: const InputDecoration(labelText: 'In Production'),
                              keyboardType: TextInputType.number,
                              validator: TValidator.validateNumber,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Save Changes Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => controller.updateProductVariant(),
                          child: const Text('Save Changes'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
