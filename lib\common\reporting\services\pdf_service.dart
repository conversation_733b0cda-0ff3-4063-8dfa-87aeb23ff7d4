import 'dart:typed_data';
import 'package:alloy/features/account/models/account_model.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:get/get.dart'; // For .capitalizeFirst!

// Import your models if needed for helper functions (like _buildAccountWithContactsEntry)
import '../../../features/authentication/models/user_model.dart';
import '../../../features/contact/models/contact_model.dart';
import '../models/report_config.dart';

class PdfService {
  final String companyName = "Bonn Metals";
  final String companyWebsite = "www.bonn-group.com";

  /// Generates a PDF report using provided configuration and raw data models.
  Future<Uint8List> generatePdfReport(ReportConfig config, List<dynamic> models) async {
    final pdf = pw.Document();
    final font = await PdfGoogleFonts.poppinsMedium();

    pdf.addPage(
      pw.MultiPage(
        pageFormat: config.pdfPageFormat ?? PdfPageFormat.a4.landscape,
        build: (pw.Context context) {
          List<pw.Widget> content = [_buildReportHeader(config.title, font), pw.SizedBox(height: 20)];

          //############## Special handling for nested reports like AccountsWithContacts ########### //
          if (config.isNestedReport) {
            List<AccountModel> accounts = List<AccountModel>.from(models);
            content.addAll(accounts.map((account) => _buildAccountWithContactsEntry(account, font)).toList());
          } else {
            // For simple tabular reports (regular reports)
            final List<String> headers = config.columns.map((c) => c.headerText).toList();
            final List<List<dynamic>> dataRows = models.map((model) {
              final modelMap = _modelToMap(model); // Convert model to map for generic access
              return config.columns.map((column) {
                dynamic value = modelMap[column.fieldName];
                return column.formatter != null ? column.formatter!(value, model) : value?.toString() ?? 'N/A';
              }).toList();
            }).toList();
            content.add(_buildGenericTable(headers, dataRows, font, config));
          }
          return [pw.Column(crossAxisAlignment: pw.CrossAxisAlignment.start, children: content)];
        },
      ),
    );

    return pdf.save();
  }

  /// Helper to convert a dynamic model object into a Map<String, dynamic>.
  /// Assumes models have a toJson() method that returns a map of their properties.
  Map<String, dynamic> _modelToMap(dynamic model) {
    if (model is AccountModel) return model.toJson();
    if (model is ContactModel) return model.toJson();
    if (model is UserModel) return model.toJson();
    // Add other models here as needed for generic reporting
    return {}; // Fallback for unsupported types
  }

  // --- Helper Widgets for PDF Content ---

  pw.Widget _buildReportHeader(String title, pw.Font font) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          companyName,
          style: pw.TextStyle(font: font, fontSize: 24, fontWeight: pw.FontWeight.bold, color: PdfColors.blue700),
        ),
        pw.Text(
          companyWebsite,
          style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.grey700),
        ),
        pw.Divider(),
        pw.Text(
          title,
          style: pw.TextStyle(font: font, fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.Text('Generated on: ${DateTime.now().toLocal().toString().split('.')[0]}', style: pw.TextStyle(font: font, fontSize: 10)),
      ],
    );
  }

  /// Builds a generic PDF table using headers and data rows derived from ReportConfig.
  pw.Table _buildGenericTable(List<String> headers, List<List<dynamic>> dataRows, pw.Font font, ReportConfig config) {
    Map<int, pw.TableColumnWidth> columnWidths = {};
    for (int i = 0; i < config.columns.length; i++) {
      if (config.columns[i].pdfColumnWidth != null) {
        columnWidths[i] = config.columns[i].pdfColumnWidth!;
      }
    }

    return pw.TableHelper.fromTextArray(
      headers: headers,
      data: dataRows,
      border: pw.TableBorder.all(color: PdfColors.grey400),
      headerStyle: pw.TextStyle(font: font, fontWeight: pw.FontWeight.bold, fontSize: 8),
      cellStyle: pw.TextStyle(font: font, fontSize: 7),
      headerDecoration: const pw.BoxDecoration(color: PdfColors.blueGrey100),
      cellAlignment: pw.Alignment.centerLeft,
      columnWidths: columnWidths.isNotEmpty ? columnWidths : null,
    );
  }

  /// Specific method for Accounts with Contacts, as it's a nested structure.
  /// This method still directly accesses AccountModel and ContactModel properties.
  pw.Widget _buildAccountWithContactsEntry(AccountModel account, pw.Font font) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 15),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Account: ${account.name} (${account.businessType.name.capitalizeFirst})',
            style: pw.TextStyle(font: font, fontSize: 12, fontWeight: pw.FontWeight.bold, color: PdfColors.blue800),
          ),
          if (account.alsoKnownAs != null && account.alsoKnownAs!.isNotEmpty)
            pw.Text(
              'Also Known As: ${account.alsoKnownAs}',
              style: pw.TextStyle(font: font, fontSize: 10, color: PdfColors.grey700),
            ),
          pw.Text('Location: ${account.emirates}, ${account.country}', style: pw.TextStyle(font: font, fontSize: 10)),
          if (account.phone != null && account.phone!.isNotEmpty) pw.Text('Phone: ${account.phone}', style: pw.TextStyle(font: font, fontSize: 10)),

          pw.SizedBox(height: 8),
          if (account.contactDetails != null && account.contactDetails!.isNotEmpty)
            pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Text(
                  'Associated Contacts:',
                  style: pw.TextStyle(font: font, fontSize: 11, fontWeight: pw.FontWeight.bold),
                ),
                pw.TableHelper.fromTextArray(
                  headers: ['Contact Name', 'Designation', 'Phone', 'Email'],
                  data: account.contactDetails!.map((contact) {
                    return [contact.name, contact.designation ?? 'N/A', contact.phone, contact.email ?? 'N/A'];
                  }).toList(),
                  border: pw.TableBorder.all(color: PdfColors.grey300),
                  headerStyle: pw.TextStyle(font: font, fontWeight: pw.FontWeight.bold, fontSize: 7),
                  cellStyle: pw.TextStyle(font: font, fontSize: 6),
                  headerDecoration: const pw.BoxDecoration(color: PdfColors.blueGrey50),
                  cellAlignment: pw.Alignment.centerLeft,
                  columnWidths: {
                    0: const pw.FlexColumnWidth(2),
                    1: const pw.FlexColumnWidth(1.5),
                    2: const pw.FlexColumnWidth(1.2),
                    3: const pw.FlexColumnWidth(1.8),
                  },
                ),
              ],
            )
          else
            pw.Text(
              'No associated contacts.',
              style: pw.TextStyle(font: font, fontSize: 9, color: PdfColors.grey500),
            ),
          pw.SizedBox(height: 15),
        ],
      ),
    );
  }
}
