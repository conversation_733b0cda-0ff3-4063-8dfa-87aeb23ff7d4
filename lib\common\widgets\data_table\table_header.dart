import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';

class TTableHeader extends StatelessWidget {
  const TTableHeader({super.key, this.actions, this.searchController, this.searchOnChanged, required this.hintText});

  final List<Widget>? actions;
  final String hintText;
  final TextEditingController? searchController;
  final Function(String)? searchOnChanged;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (actions != null && actions!.isNotEmpty)
          Expanded(
            flex: 2,
            child: Row(mainAxisAlignment: MainAxisAlignment.end, children: actions!),
          ),
        Spacer(),
        Expanded(
          flex: 3,
          child: TextFormField(
            controller: searchController,
            onChanged: searchOnChanged,
            decoration: InputDecoration(prefixIcon: Icon(Iconsax.search_normal), hintText: hintText),
          ),
        ),
      ],
    );
  }
}
