import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../../products/controller/product_controller/product_controller.dart';
import '../../../../products/models/product_model.dart';
import '../../../../products/models/product_variant_model.dart';
import '../../../../products/repository/product_variant_repository.dart';
import '../../../controllers/deal_item_controller.dart';

/// Product Catalog Browser for Stage 2 Deal Creation
/// Allows browsing, searching, and selecting products with variants
class ProductCatalogBrowser extends StatelessWidget {
  const ProductCatalogBrowser({super.key});

  @override
  Widget build(BuildContext context) {
    final productController = Get.put(ProductController());
    final itemController = Get.put(DealItemController());
    final dark = THelperFunctions.isDarkMode(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with search and filters
        _buildHeader(context, productController),
        const SizedBox(height: TSizes.spaceBtwItems),

        // Product type filter chips
        _buildProductTypeFilters(context, productController),
        const SizedBox(height: TSizes.spaceBtwItems),

        // Product list
        Expanded(child: _buildProductList(context, productController, itemController, dark)),
      ],
    );
  }

  /// Header with search and category filter
  Widget _buildHeader(BuildContext context, ProductController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text('Product Catalog', style: Theme.of(context).textTheme.titleLarge),
            TextButton.icon(
              onPressed: () => controller.listenToStream(),
              icon: const Icon(Iconsax.refresh),
              label: const Text('Refresh'),
            ),
          ],
        ),
        const SizedBox(height: TSizes.spaceBtwItems),

        // Search bar
        TextFormField(
          decoration: const InputDecoration(
            hintText: 'Search products...',
            prefixIcon: Icon(Iconsax.search_normal),
            border: OutlineInputBorder(),
          ),
          onChanged: (value) {
            // TODO: Implement search functionality
            // controller.searchQuery.value = value;
          },
        ),
      ],
    );
  }

  /// Product type filter chips
  Widget _buildProductTypeFilters(BuildContext context, ProductController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Product Categories', style: Theme.of(context).textTheme.titleSmall),
        const SizedBox(height: TSizes.spaceBtwItems / 2),

        Wrap(
          spacing: TSizes.xs,
          runSpacing: TSizes.xs,
          children: ProductType.values.map((type) {
            return FilterChip(
              label: Text(_getProductTypeLabel(type)),
              selected: false, // TODO: Implement filter state
              onSelected: (selected) {
                // TODO: Implement filter functionality
              },
              avatar: Icon(_getProductTypeIcon(type), size: 16, color: TColors.darkGrey),
            );
          }).toList(),
        ),
      ],
    );
  }

  /// Product list with variants
  Widget _buildProductList(
    BuildContext context,
    ProductController productController,
    DealItemController itemController,
    bool dark,
  ) {
    return Obx(() {
      final products = productController.allItems;

      if (products.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Iconsax.box, size: 64, color: TColors.darkGrey),
              const SizedBox(height: TSizes.spaceBtwItems),
              Text('No products found', style: Theme.of(context).textTheme.titleMedium),
              Text('Try adjusting your filters', style: Theme.of(context).textTheme.bodyMedium),
            ],
          ),
        );
      }

      return ListView.separated(
        itemCount: products.length,
        separatorBuilder: (context, index) => const SizedBox(height: TSizes.spaceBtwItems),
        itemBuilder: (context, index) {
          final product = products[index];
          return _buildProductCard(context, product, itemController, dark);
        },
      );
    });
  }

  /// Individual product card with variants
  Widget _buildProductCard(BuildContext context, ProductModel product, DealItemController itemController, bool dark) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: dark ? TColors.darkerGrey : TColors.white,
        borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
        border: Border.all(color: TColors.borderPrimary),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Product header
          Row(
            children: [
              // Product thumbnail
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: TColors.light,
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  border: Border.all(color: TColors.borderPrimary),
                ),
                child: product.thumbnail != null
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                        child: Image.network(
                          product.thumbnail!,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) => Icon(Iconsax.box, color: TColors.darkGrey),
                        ),
                      )
                    : Icon(Iconsax.box, color: TColors.darkGrey),
              ),
              const SizedBox(width: TSizes.spaceBtwItems),

              // Product info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      product.name,
                      style: Theme.of(context).textTheme.titleMedium,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (product.description.isNotEmpty) ...[
                      const SizedBox(height: TSizes.xs),
                      Text(
                        product.description,
                        style: Theme.of(context).textTheme.bodySmall,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                    const SizedBox(height: TSizes.xs),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: TSizes.xs, vertical: 2),
                          decoration: BoxDecoration(
                            color: TColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                          ),
                          child: Text(
                            product.segment.name.toUpperCase(),
                            style: Theme.of(
                              context,
                            ).textTheme.labelSmall?.copyWith(color: TColors.primary, fontWeight: FontWeight.w600),
                          ),
                        ),
                        const SizedBox(width: TSizes.xs),
                        Text('${product.width}×${product.height}mm', style: Theme.of(context).textTheme.labelSmall),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Variants section
          _buildVariantsSection(context, product, itemController),
        ],
      ),
    );
  }

  /// Variants section for each product
  Widget _buildVariantsSection(BuildContext context, ProductModel product, DealItemController itemController) {
    return StreamBuilder<List<ProductVariantModel>>(
      stream: ProductVariantRepository.instance.streamVariantsForProduct(product.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }

        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Text(
            'No variants available',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(color: TColors.darkGrey),
          );
        }

        final variants = snapshot.data!;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Available Variants', style: Theme.of(context).textTheme.titleSmall),
            const SizedBox(height: TSizes.spaceBtwItems / 2),

            Wrap(
              spacing: TSizes.xs,
              runSpacing: TSizes.xs,
              children: variants.map((variant) {
                return _buildVariantChip(context, product, variant, itemController);
              }).toList(),
            ),
          ],
        );
      },
    );
  }

  /// Individual variant chip
  Widget _buildVariantChip(
    BuildContext context,
    ProductModel product,
    ProductVariantModel variant,
    DealItemController itemController,
  ) {
    final variantLabel = _buildVariantLabel(variant);

    return ActionChip(
      label: Text(variantLabel),
      avatar: Icon(Iconsax.add_circle, size: 16),
      onPressed: () => _selectVariant(context, product, variant, itemController),
      backgroundColor: TColors.primary.withValues(alpha: 0.1),
      side: BorderSide(color: TColors.primary),
    );
  }

  /// Build variant label from attributes
  String _buildVariantLabel(ProductVariantModel variant) {
    final attributes = <String>[];

    if (variant.attributes.containsKey('Material')) {
      attributes.add(variant.attributes['Material']!);
    }
    if (variant.attributes.containsKey('Thickness')) {
      attributes.add(variant.attributes['Thickness']!);
    }
    if (variant.attributes.containsKey('Length')) {
      attributes.add(variant.attributes['Length']!);
    }

    return attributes.isNotEmpty ? attributes.join(' • ') : variant.sku;
  }

  /// Select variant and show quantity dialog
  void _selectVariant(
    BuildContext context,
    ProductModel product,
    ProductVariantModel variant,
    DealItemController itemController,
  ) {
    // Show quantity selection dialog
    _showQuantityDialog(context, product, variant, itemController);
  }

  /// Show quantity selection dialog
  void _showQuantityDialog(
    BuildContext context,
    ProductModel product,
    ProductVariantModel variant,
    DealItemController itemController,
  ) {
    final quantityController = TextEditingController();
    String selectedUnit = 'MTR';

    Get.dialog(
      AlertDialog(
        title: Text('Add ${product.name}'),
        content: StatefulBuilder(
          builder: (context, setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Variant: ${_buildVariantLabel(variant)}'),
                const SizedBox(height: TSizes.spaceBtwItems),

                TextFormField(
                  controller: quantityController,
                  decoration: const InputDecoration(labelText: 'Quantity', border: OutlineInputBorder()),
                  keyboardType: TextInputType.number,
                ),
                const SizedBox(height: TSizes.spaceBtwItems),

                DropdownButtonFormField<String>(
                  value: selectedUnit,
                  decoration: const InputDecoration(labelText: 'Unit', border: OutlineInputBorder()),
                  items: ['MTR', 'NOS', 'SET', 'KG'].map((unit) {
                    return DropdownMenuItem(value: unit, child: Text(unit));
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      selectedUnit = value ?? 'MTR';
                    });
                  },
                ),
              ],
            );
          },
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              if (quantityController.text.isNotEmpty) {
                // Set the values in the controller
                itemController.quotedQuantity.text = quantityController.text;
                itemController.selectedQuotedUnit.value = selectedUnit;
                itemController.selectedProduct.value = product;
                itemController.selectedVariant.value = variant;

                // Call the existing createDealItem method
                itemController.createDealItem();
                Get.back();
              }
            },
            child: const Text('Add Item'),
          ),
        ],
      ),
    );
  }

  /// Get product type label
  String _getProductTypeLabel(ProductType type) {
    switch (type) {
      case ProductType.mainItem:
        return 'Main Items';
      case ProductType.accessory:
        return 'Accessories';
      case ProductType.coupler:
        return 'Couplers';
      case ProductType.hardware:
        return 'Hardware';
      case ProductType.consumable:
        return 'Consumables';
      case ProductType.cover:
        return 'Covers';
    }
  }

  /// Get product type icon
  IconData _getProductTypeIcon(ProductType type) {
    switch (type) {
      case ProductType.mainItem:
        return Iconsax.box;
      case ProductType.accessory:
        return Iconsax.component;
      case ProductType.coupler:
        return Iconsax.link;
      case ProductType.hardware:
        return Iconsax.setting_2;
      case ProductType.consumable:
        return Iconsax.drop;
      case ProductType.cover:
        return Iconsax.shield;
    }
  }
}
