import 'package:alloy/common/widgets/data_table/paginated_data_table.dart';
import 'package:alloy/features/products/controller/product_controller/product_controller.dart';
import 'package:alloy/features/products/models/product_model.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'table_source.dart';

class ProductsTable extends StatelessWidget {
  const ProductsTable({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProductController>();
    final isMobile = TDeviceUtils.isMobileScreen(context);

    return Obx(() {
      // OPTIMIZED: Direct reactive listening without unnecessary widgets
      // Obx automatically rebuilds when reactive properties change
      // This is just to trigger the Obx.
      Text(controller.filteredItems.length.toString());
      Text(controller.selectedRows.length.toString());
      return TPaginatedDataTable(
        // minWidth: 1200,
        sortAscending: controller.sortAscending.value,
        sortColumnIndex: controller.sortColumnIndex.value,
        columns: [
          DataColumn2(
            label: const Text('Thumbnail'),
            fixedWidth: isMobile ? null : 80, // Adjust width as needed
          ),
          DataColumn2(
            label: const Text('Name'),
            size: ColumnSize.S,
            // fixedWidth: isMobile ? null : 400, // Adjust fixedWidth as needed
            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (ProductModel product) => product.name.toLowerCase()),
          ),
          DataColumn2(
            label: const Text('Category'),
            fixedWidth: isMobile ? null : 150, // Adjust fixedWidth as needed
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductModel product) => product.category?.name.toLowerCase() ?? '', // Handle null category
            ),
          ),
          DataColumn2(
            label: const Text('Segment'),
            fixedWidth: isMobile ? null : 150, // Adjust fixedWidth as needed
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductModel product) => product.segment.name.toLowerCase(),
            ),
          ),
          DataColumn2(
            label: const Text('Dimensions (WxH)'),
            fixedWidth: isMobile ? null : 180, // Adjust fixedWidth as needed
          ),
          DataColumn2(label: const Text('Actions'), fixedWidth: isMobile ? null : 150),
        ],
        source: ProductsRows(), // Use the new ProductsRows data source
      );
    });
  }
}
