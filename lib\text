  @override
  Widget build(BuildContext context) {
    // Initialize Controllers
    final controller = Get.put(ProductController()); // Assuming ProductController
    final reportController = Get.put(ReportController());

    return Scaffold(
      body: Padding(
        // Removed SingleChildScrollView
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Column(
          // This Column will now get a bounded height from Scaffold
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Breadcrumbs
            const TBreadcrumbsWithHeading(
              heading: 'Products Management', // Updated heading
              breadcrumbItems: [TBreadcrumbItem(text: 'Products')], // Updated breadcrumb
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Table Body - Wrapped in Expanded to fill remaining vertical space
            Expanded(
              // <--- Added Expanded here
              child: TRoundedContainer(
                child: Column(
                  children: [
                    // --- Header Row for All Actions and Search ---
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start, // Align items to the start
                      crossAxisAlignment: CrossAxisAlignment.center, // Vertically center all items
                      children: [
                        // ################# Printing Menu (Popup Button)
                        popUpMenuButton(reportController),

                        const SizedBox(width: TSizes.spaceBtwItems), // Space after popup menu
                        // ################# Create New Account Button
                        ElevatedButton.icon(
                          icon: const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8.0),
                            child: Icon(Iconsax.save_add),
                          ),
                          onPressed: () => Get.toNamed(TRoutes.createProduct),
                          label: const Text('   Create New Product   '),
                        ),
                        const SizedBox(width: TSizes.spaceBtwItems), // Space after create button
                        // ################## Bulk Create Variants Button
                        ElevatedButton.icon(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext dialogContext) {
                                return Dialog(
                                  // Using Dialog for a simple modal, or AlertDialog for more structure
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
                                  ),
                                  elevation: 0,
                                  backgroundColor: Colors
                                      .transparent, // Make background transparent to show TRoundedContainer's color
                                  child: ConstrainedBox(
                                    // Constrain the size of the dialog content
                                    constraints: BoxConstraints(
                                      maxWidth: 600, // Max width for the dialog
                                      maxHeight: MediaQuery.of(dialogContext).size.height * 0.8, // Max height
                                    ),
                                    child: BulkVariantCreationWidget(), // Navigate to bulk create screen
                                  ),
                                );
                              },
                            );
                          },
                          icon: const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8.0),
                            child: Icon(Iconsax.magicpen),
                          ),
                          label: const Text('Bulk Add Variants   '),
                        ),
                        const SizedBox(width: TSizes.spaceBtwItems),

                        // ############### Import Products Button
                        ElevatedButton.icon(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (BuildContext dialogContext) {
                                return Dialog(
                                  // Using Dialog for a simple modal, or AlertDialog for more structure
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
                                  ),
                                  elevation: 0,
                                  backgroundColor: Colors
                                      .transparent, // Make background transparent to show TRoundedContainer's color
                                  child: ConstrainedBox(
                                    // Constrain the size of the dialog content
                                    constraints: BoxConstraints(
                                      maxWidth: 600, // Max width for the dialog
                                      maxHeight: MediaQuery.of(dialogContext).size.height * 0.8, // Max height
                                    ),
                                    child: ImportProductsWidget(), // Your import screen as the dialog content
                                  ),
                                );
                              },
                            );
                          },
                          icon: const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8.0),
                            child: Icon(Iconsax.import),
                          ),
                          label: const Text('Excel Import Products  '),
                        ),
                        const SizedBox(width: TSizes.spaceBtwItems),

                        // NEW: Show Discontinued Products Toggle
                        Obx(
                          () => Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Switch(
                                value: controller.showDiscontinuedProducts.value,
                                onChanged: (value) {
                                  controller.showDiscontinuedProducts.value = value;
                                  controller.listenToStream(); // Re-fetch/filter the stream
                                },
                                activeColor: TColors.primary,
                              ),
                              Text('Show Discontinued', style: Theme.of(context).textTheme.labelLarge),
                            ],
                          ),
                        ),

                        const Spacer(), // Pushes the search bar to the far right
                        // Search Bar
                        Expanded(
                          flex: 3, // Adjust flex as needed for search bar width
                          child: TTableHeader(
                            hintText: 'Search Products', // Updated hint text
                            searchOnChanged: (query) => controller.searchQuery(query),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: TSizes.spaceBtwItems),

                    // Table - Wrapped in Expanded to fill remaining vertical space within TRoundedContainer
                    Expanded(
                      // <--- Added Expanded here for the table
                      child: Obx(() {
                        if (controller.isLoading.value) return const Center(child: CircularProgressIndicator());
                        return const ProductsTable(); // Assuming ProductsTable now
                      }),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}