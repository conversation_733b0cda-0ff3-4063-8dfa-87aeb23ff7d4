import 'package:alloy/bindings/general_bindings.dart';
import 'package:alloy/features/authentication/screens/login/login_screen.dart';
import 'package:alloy/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:alloy/routes/app_routes.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:alloy/utils/theme/theme.dart';

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      title: TTexts.appName,
      themeMode: ThemeMode.light,
      theme: TAppTheme.lightTheme,
      darkTheme: TAppTheme.darkTheme,

      // Routing: Initial route is handled by AuthenticationRepository.screenRedirect()
      initialRoute: TRoutes.login,
      getPages: TAppRoutes.pages,
      initialBinding: GeneralBindings(),
      unknownRoute: GetPage(name: '/login', page: () => LoginScreen()),
      // unknownRoute: GetPage(
      //   name: '/page-not-found',
      //   page: () => Scaffold(body: Center(child: Text('Page Not Found'))),
      // ),
      // The initial auth check will handle redirection from here.
      home: const Scaffold(body: Center(child: CircularProgressIndicator())),
    );
  }
}
