import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/features/account/models/account_model.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:alloy/features/contact/repository/contact_repository.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CreateContactController extends GetxController {
  static CreateContactController get instance => Get.find();

  /// Variables
  final formKey = GlobalKey<FormState>();
  final name = TextEditingController();
  final email = TextEditingController();
  final phone = TextEditingController();
  final landline = TextEditingController();
  final designation = TextEditingController();
  final title = TextEditingController();
  final active = true.obs;

  final RxList<AccountModel> selectedAccounts = <AccountModel>[].obs;
  final RxList<UserModel> selectedHandlers = <UserModel>[].obs;

  /// Toggle Account Selection
  void toggleAccountSelection(AccountModel account) {
    if (selectedAccounts.contains(account)) {
      selectedAccounts.remove(account);
    } else {
      selectedAccounts.add(account);
    }
  }

  /// Toggle Handler Selection
  void toggleHandlerSelection(UserModel handler) {
    if (selectedHandlers.contains(handler)) {
      selectedHandlers.remove(handler);
    } else {
      selectedHandlers.add(handler);
    }
  }

  /// Create Contact
  Future<void> createContact() async {
    try {
      TFullScreenLoader.popUpCircular();

      if (!await NetworkManager.instance.isConnected() || !formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final newContact = ContactModel(
        id: '',
        name: name.text.trim(),
        email: email.text.trim(),
        phone: phone.text.trim(),
        landline: landline.text.trim(),
        designation: designation.text.trim(),
        title: title.text.trim(),
        active: active.value,
        // accountIds: selectedAccounts.map((e) => e.id).toList(),
        // handlerIds: selectedHandlers.map((e) => e.id!).toList(),
        createdAt: DateTime.now(),
      );

      newContact.accountDetails = selectedAccounts.toList();
      newContact.handlerDetails = selectedHandlers.toList();
      await ContactRepository.instance.createContact(newContact);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Contact Created', message: 'Contact ${newContact.name} created successfully.');
      Get.offAllNamed(TRoutes.contacts);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }
}
