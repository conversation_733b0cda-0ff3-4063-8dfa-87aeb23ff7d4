import 'package:alloy/common/layouts/sidebars/menu/menu_data.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:get/get.dart';

class SidebarController extends GetxController {
  final RxString activeItem = Get.currentRoute.obs;
  final RxString hoverItem = ''.obs;
  // Map to store the expanded state of collapsible menus
  // Key: parent menu route, Value: true if expanded, false if collapsed
  final RxMap<String, bool> expandedMenus = <String, bool>{}.obs;

  // Sidebar collapse state
  final RxBool isCollapsed = false.obs;

  void changeActiveItemTo(String route) {
    activeItem.value = route;
  }

  void changeHoverItemTo(String route) {
    if (!isActive(route)) hoverItem.value = route;
  }

  bool isActive(String itemName) => activeItem.value == itemName;
  bool isHovering(String itemName) => hoverItem.value == itemName;
  bool isMenuExpanded(String route) => expandedMenus[route] ?? false;

  void toggleMenuExpansion(String route) {
    expandedMenus[route] = !(expandedMenus[route] ?? false);
  }

  /// Toggle sidebar collapse state
  void toggleSidebar() {
    isCollapsed.value = !isCollapsed.value;
    // When collapsed, close all expanded menus
    if (isCollapsed.value) {
      expandedMenus.clear();
    }
  }

  /// Collapse sidebar
  void collapseSidebar() {
    isCollapsed.value = true;
    expandedMenus.clear();
  }

  /// Expand sidebar
  void expandSidebar() {
    isCollapsed.value = false;
  }

  void clearHoverItem() {
    hoverItem.value = '';
  }

  void menuOnTap(MenuData menuItem) {
    if (menuItem.type == MenuType.collapsible) {
      toggleMenuExpansion(menuItem.route);
    } else {
      // Update the active item in the sidebar to highlight the current page
      if (!isActive(menuItem.route)) {
        changeActiveItemTo(menuItem.route);
      }

      // Close the drawer on mobile/tablet automatically after selecting a menu item
      if (!TDeviceUtils.isDesktopScreen(Get.context!)) Get.back();

      // Navigate to the selected route
      Get.toNamed(menuItem.route);
    }
  }

  // Override didPush and didPop to handle sub-menu activation correctly
  // When navigating to a sub-route, ensure its parent is expanded and active.
  @override
  void onInit() {
    super.onInit();
    // Initialize expanded state for all collapsible menus to false
    for (var menuItem in MenuData.allMenuItems) {
      if (menuItem.type == MenuType.collapsible) {
        expandedMenus[menuItem.route] = true;
      }
    }
    // Set initial active item based on current route
    updateActiveAndExpandedState(Get.currentRoute);
  }

  void updateActiveAndExpandedState(String currentRoute) {
    activeItem.value = currentRoute;
    // Find if the current route is a child of any collapsible menu
    for (var parentMenu in MenuData.allMenuItems) {
      if (parentMenu.type == MenuType.collapsible && parentMenu.children != null) {
        for (var childMenu in parentMenu.children!) {
          if (childMenu.route == currentRoute) {
            // If a child is active, ensure its parent is expanded
            expandedMenus[parentMenu.route] = true;
            return; // Found and handled
          }
        }
      }
    }
  }
}
