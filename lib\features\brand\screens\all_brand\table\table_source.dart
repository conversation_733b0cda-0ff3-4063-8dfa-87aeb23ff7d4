import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import 'package:alloy/common/widgets/icons/table_action_icon_buttons.dart';
import 'package:alloy/common/widgets/images/t_rounded_image.dart';
import 'package:alloy/features/brand/controller/brand_controller.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/sizes.dart';

class BrandsRows extends DataTableSource {
  final controller = BrandController.instance;
  final isMobile = TDeviceUtils.isMobileScreen(Get.context!);
  @override
  DataRow getRow(int index) {
    final brand = controller.filteredItems[index];

    return DataRow2(
      selected: controller.selectedRows[index],
      onSelectChanged: (value) {
        controller.selectedRows[index] = value!;
      },
      cells: [
        DataCell(
          Row(
            children: [
              TRoundedImage(
                width: 50,
                height: 50,
                padding: TSizes.sm,
                image: brand.image,
                imageType: ImageType.network,
                borderRadius: TSizes.borderRadiusMd,
                backgroundColor: TColors.primaryBackground,
              ),
              const SizedBox(width: TSizes.spaceBtwItems),
              Expanded(
                child: Text(
                  brand.name,
                  style: Theme.of(Get.context!).textTheme.bodyLarge!.apply(color: TColors.primary),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),

        DataCell(
          Padding(
            padding: const EdgeInsets.symmetric(vertical: TSizes.sm),
            child: SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Wrap(
                spacing: TSizes.xs,
                direction: isMobile ? Axis.vertical : Axis.horizontal,
                children: brand.brandCategories != null
                    ? brand.brandCategories!
                          .map(
                            (category) => Padding(
                              padding: EdgeInsets.only(bottom: isMobile ? 0 : TSizes.xs),
                              child: Chip(label: Text(category.name), padding: EdgeInsets.all(TSizes.xs)),
                            ),
                          )
                          .toList()
                    : [],
              ),
            ),
          ),
        ),
        DataCell(brand.isFeatured ? const Icon(Iconsax.heart5, color: TColors.primary) : const Icon(Iconsax.heart)),
        DataCell(Text(brand.createdAt == null ? '-' : brand.formattedCreatedAtDate)),
        DataCell(
          TTableActionButtons(
            onEditPressed: () => Get.toNamed(TRoutes.editBrand, arguments: brand),
            onDeletePressed: () => controller.confirmDeleteItem(brand),
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false;
  @override
  int get rowCount => controller.filteredItems.length;
  @override
  int get selectedRowCount => controller.selectedRows.where((selected) => selected).length;
}
