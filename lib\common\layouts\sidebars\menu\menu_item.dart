import 'package:alloy/common/layouts/sidebars/sidebar_controller.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import 'menu_data.dart';

class TMenuItem extends StatelessWidget {
  const TMenuItem({
    super.key,
    required this.menuItem,
    this.isSubItem = false, // New parameter to indicate if it's a child menu item
    this.isCollapsed = false, // New parameter for collapsed sidebar state
  });

  final MenuData menuItem;
  final bool isSubItem; // Indicates if this is a child in a sub-menu
  final bool isCollapsed; // Indicates if sidebar is collapsed

  @override
  Widget build(BuildContext context) {
    final menuController = Get.put(SidebarController());

    // Helper to determine if a regular menu item is currently active
    bool isRegularItemActive() {
      return menuController.isActive(menuItem.route);
    }

    // Helper functions for common styling
    Color? getIconColor() {
      // Regular item: highlighted if active or hovering
      if (menuItem.type == MenuType.item) {
        return isRegularItemActive() || menuController.isHovering(menuItem.route) ? TColors.white : TColors.darkGrey;
      }
      // Collapsible item (parent): highlighted only if hovering
      return menuController.isHovering(menuItem.route) ? TColors.white : TColors.darkGrey;
    }

    Color? getTextColor() {
      // Regular item: highlighted if active or hovering
      if (menuItem.type == MenuType.item) {
        return isRegularItemActive() || menuController.isHovering(menuItem.route) ? TColors.white : TColors.darkGrey;
      }
      // Collapsible item (parent): highlighted only if hovering
      return menuController.isHovering(menuItem.route) ? TColors.white : TColors.darkGrey;
    }

    BoxDecoration getBoxDecoration() {
      // Regular item: active color if active, grey if hovering
      if (menuItem.type == MenuType.item) {
        return BoxDecoration(
          color: menuController.isHovering(menuItem.route)
              ? TColors.grey
              : (isRegularItemActive() ? TColors.primary : Colors.transparent),
          borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
          boxShadow: [
            if (isRegularItemActive()) // Only show shadow if truly active
              BoxShadow(
                color: TColors.primary.withValues(alpha: 0.2),
                spreadRadius: 3,
                blurRadius: 10,
                offset: const Offset(1, 2),
              ),
          ],
        );
      }
      // Collapsible item (parent): grey only if hovering, transparent otherwise
      return BoxDecoration(
        color: menuController.isHovering(menuItem.route) ? TColors.grey : Colors.transparent,
        borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
      );
    }

    // Common padding for menu items
    Widget buildMenuItemContent({required Widget child, bool isSub = false}) {
      return Padding(
        padding: EdgeInsets.only(
          left: isCollapsed ? TSizes.sm : (isSub ? TSizes.lg * 2 : TSizes.lg), // Reduced padding when collapsed
          top: TSizes.md,
          bottom: TSizes.md,
          right: isCollapsed ? TSizes.sm : TSizes.md,
        ),
        child: child,
      );
    }

    // Determine line color based on active state (can be refined)
    // Line color could be primary if any child is active, or grey if not.
    final lineColor =
        (menuItem.type == MenuType.collapsible &&
            menuItem.children!.any((child) => menuController.isActive(child.route)))
        ? TColors.primary.withValues(alpha: 0.5)
        : TColors.grey.withValues(alpha: 0.5);

    if (menuItem.type == MenuType.collapsible) {
      return Obx(
        () => Column(
          children: [
            Tooltip(
              message: isCollapsed ? menuItem.title : '',
              child: InkWell(
                onTap: () => menuController.menuOnTap(menuItem), // Toggle expansion
                onHover: (hovering) =>
                    hovering ? menuController.changeHoverItemTo(menuItem.route) : menuController.changeHoverItemTo(''),
                child: Container(
                  decoration: getBoxDecoration(), // Use the common function
                  child: buildMenuItemContent(
                    isSub: isSubItem,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(menuItem.icon, size: 22, color: getIconColor()),
                        if (!isCollapsed) ...[
                          const SizedBox(width: TSizes.md),
                          Flexible(
                            child: Text(
                              menuItem.title,
                              style: Theme.of(context).textTheme.bodyMedium!.apply(
                                color: getTextColor(),
                                fontWeightDelta: 2, // Make parent menu text bold
                              ),
                            ),
                          ),
                          const SizedBox(width: TSizes.sm), // Added space before arrow
                          Icon(
                            menuController.isMenuExpanded(menuItem.route) ? Iconsax.arrow_up_2 : Iconsax.arrow_down_1,
                            size: 16,
                            color: getIconColor(),
                          ),
                        ],
                      ],
                    ),
                  ),
                ),
              ),
            ),
            if (menuController.isMenuExpanded(menuItem.route) && !isCollapsed)
              Column(
                children: menuItem.children!
                    .map(
                      (childItem) => Stack(
                        children: [
                          Positioned(
                            top: 0,
                            bottom: 0,
                            left: (TSizes.xl), // Align with the child's icon
                            child: CustomPaint(
                              painter: _SubMenuLinePainter(
                                lineColor: lineColor,
                                isLast: childItem == menuItem.children!.last,
                              ),
                              child: const SizedBox(width: 20),
                            ),
                          ),
                          TMenuItem(menuItem: childItem, isSubItem: true),
                        ],
                      ),
                    )
                    .toList(),
              ),
          ],
        ),
      );
    } else {
      // Regular menu item
      return Tooltip(
        message: isCollapsed ? menuItem.title : '',
        child: InkWell(
          onTap: () => menuController.menuOnTap(menuItem),
          onHover: (hovering) =>
              hovering ? menuController.changeHoverItemTo(menuItem.route) : menuController.changeHoverItemTo(''),
          child: Obx(
            () => Container(
              decoration: getBoxDecoration(),
              child: buildMenuItemContent(
                isSub: isSubItem,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Icon(menuItem.icon, size: 22, color: getIconColor()),
                    if (!isCollapsed) ...[
                      const SizedBox(width: TSizes.md),
                      Flexible(
                        child: Text(
                          menuItem.title,
                          style: Theme.of(context).textTheme.bodyMedium!.apply(color: getTextColor()),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    }
  }
}

// CustomPainter for the linking line
class _SubMenuLinePainter extends CustomPainter {
  final Color lineColor;
  final bool isLast;

  _SubMenuLinePainter({required this.lineColor, this.isLast = false});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = lineColor
      ..strokeWidth = 1.5
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    // Define coordinates based on the child menu item's visual layout
    const double verticalLineX = 5.0; // Horizontal position of the vertical line within the CustomPaint's width (20)
    const double horizontalLineY = TSizes.md + (22 / 2); // Vertical center of the icon
    const double horizontalLineLength = 10.0; // Length of the horizontal part of the L-shape

    // Vertical line
    final double verticalLineStopY = isLast ? horizontalLineY : size.height;
    canvas.drawLine(
      Offset(verticalLineX, 0), // Start from the top of this widget's height
      Offset(verticalLineX, verticalLineStopY), // Extend to bottom or until horizontal line
      paint,
    );

    // Horizontal line
    canvas.drawLine(
      Offset(verticalLineX, horizontalLineY),
      Offset(verticalLineX + horizontalLineLength, horizontalLineY),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    if (oldDelegate is _SubMenuLinePainter) {
      return oldDelegate.lineColor != lineColor || oldDelegate.isLast != isLast;
    }
    return true;
  }
}
