import 'package:alloy/data/abstract/base_data_table_controller.dart';
import 'package:alloy/features/account/models/account_model.dart';
import 'package:alloy/features/account/repository/account_repository.dart';

class AccountController extends TBaseController<AccountModel> {
  final AccountRepository _accountRepository = AccountRepository.instance;

  // AccountController(this._accountRepository); // Constructor updated

  @override
  Future<List<AccountModel>> fetchItems() async {
    // Accounts already contain handlerDetails and contactDetails from Firestore
    return await _accountRepository.getAllAccounts();
  }

  @override
  Stream<List<AccountModel>> streamItems() {
    // Accounts already contain handlerDetails and contactDetails from Firestore
    return _accountRepository.streamAllAccounts();
  }

  /// NEW: Stream only accounts that are marked as parents.
  Stream<List<AccountModel>> streamParentAccounts() {
    return _accountRepository.streamParentAccounts();
  }

  // Removed _mapDetailsToAccounts as it's no longer necessary

  // final UserRepository _userRepository;
  // final ContactRepository _contactRepository;

  // AccountController(this._accountRepository, this._userRepository, this._contactRepository);

  // @override
  // Future<List<AccountModel>> fetchItems() async {
  //   final accounts = await _accountRepository.getAllAccounts();
  //   final users = await _userRepository.getAllUsers();
  //   final contacts = await _contactRepository.getAllContacts();
  //   return _mapDetailsToAccounts(accounts, users, contacts);
  // }

  // @override
  // Stream<List<AccountModel>> streamItems() {
  //   return Rx.combineLatest3(
  //     _accountRepository.streamAllAccounts(),
  //     _userRepository.streamAllUsers(),
  //     _contactRepository.streamAllContacts(),
  //     _mapDetailsToAccounts,
  //   );
  // }

  // List<AccountModel> _mapDetailsToAccounts(
  //   List<AccountModel> accounts,
  //   List<UserModel> users,
  //   List<ContactModel> contacts,
  // ) {
  //   final userMap = {for (var user in users) user.id: user};
  //   final contactMap = {for (var contact in contacts) contact.id: contact};

  //   for (final account in accounts) {
  //     account.handlerDetails = account.handlerIds.map((id) => userMap[id]).whereType<UserModel>().toList();
  //     account.contactDetails = account.contactIds.map((id) => contactMap[id]).whereType<ContactModel>().toList();
  //   }
  //   return accounts;
  // }

  @override
  bool containsSearchQuery(AccountModel item, String searchText) {
    final searchLower = searchText.toLowerCase();
    return item.name.toLowerCase().contains(searchLower) ||
        (item.alsoKnownAs?.toLowerCase().contains(searchLower) ?? false) ||
        (item.phone?.toLowerCase().contains(searchLower) ?? false) ||
        (item.email?.toLowerCase().contains(searchLower) ?? false) || // Now includes email
        (item.emirates?.toLowerCase().contains(searchLower) ?? false); // Safely handle nullable emirates
  }

  @override
  Future<void> deleteItem(AccountModel item) {
    return _accountRepository.deleteAccount(item.id);
  }

  @override
  Comparable getComparableProperty(AccountModel item, int columnIndex) {
    // Implement this method to return a Comparable value for sorting based on column index
    // Ensure you always return a String, int, double, or DateTime.
    // Handle null values by providing a default comparable value (e.g., empty string, 0).
    switch (columnIndex) {
      case 0:
        return item.name.toLowerCase(); // Name
      case 1:
        return item.businessType.name.toLowerCase(); // Business (enum)
      case 2:
        return item.emirates?.toLowerCase() ?? ''; // Safely handle nullable emirates
      case 3:
        return item.phone?.toLowerCase() ?? ''; // Phone (nullable)
      case 4:
        return item.status.name.toLowerCase(); // Status (enum)
      case 5:
        // Handlers: Sort by the count of handlers
        return item.handlerDetails?.length ?? 0; // Assuming handlerDetails is List<UserModel>
      case 6:
        // Contacts: Sort by the count of contacts
        return item.contactDetails?.length ?? 0; // Assuming contactDetails is List<ContactModel>
      case 7:
        return ''; // Action column is not sortable
      default:
        return ''; // Fallback for undefined columns
    }
  }
}
