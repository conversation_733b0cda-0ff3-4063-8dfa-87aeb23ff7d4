import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class for a standalone Address.
class AddressModel {
  String id;
  String accountId; // NEW: The ID of the AccountModel this address belongs to
  String addressLine1;
  String? addressLine2; // Optional
  String? street; // Optional
  String city;
  String? state; // e.g., Emirate like "Ajman", "Dubai"
  String? zipCode;
  String country;
  String type; // e.g., "Billing", "Shipping", "Branch Office", "Warehouse"
  bool isDefault; // Flag to indicate if this is a default address for its type/account
  String? notes; // Any specific delivery instructions or additional notes

  DateTime? createdAt;
  DateTime? updatedAt;
  String createdByUserId; // The user who created this address record
  String? updatedByUserId;

  AddressModel({
    required this.id,
    required this.accountId, // NEW: Link to the owning account
    required this.addressLine1,
    this.addressLine2,
    this.street,
    required this.city,
    this.state,
    this.zipCode,
    required this.country,
    this.type = 'Shipping', // Default to 'Shipping'
    this.isDefault = false,
    this.notes,
    this.createdAt,
    this.updatedAt,
    required this.createdByUserId,
    this.updatedByUserId,
  });

  /// Empty Helper Function
  static AddressModel empty() => AddressModel(
    id: '',
    accountId: '', // Initialize new required field
    addressLine1: '',
    city: '',
    country: '',
    createdByUserId: '',
  );

  /// Convert AddressModel to JSON format for Firestore.
  Map<String, dynamic> toJson() {
    return {
      'accountId': accountId, // NEW: Store account ID
      'addressLine1': addressLine1,
      'addressLine2': addressLine2,
      'street': street,
      'city': city,
      'state': state,
      'zipCode': zipCode,
      'country': country,
      'type': type,
      'isDefault': isDefault,
      'notes': notes,
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
      'createdByUserId': createdByUserId,
      'updatedByUserId': updatedByUserId,
    };
  }

  /// Factory method to create an AddressModel from a Firestore DocumentSnapshot.
  factory AddressModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (!document.exists || document.data() == null) {
      return AddressModel.empty();
    }

    final data = document.data()!;
    return AddressModel(
      id: document.id,
      accountId: data['accountId'] ?? '', // NEW: Parse account ID
      addressLine1: data['addressLine1'] ?? '',
      addressLine2: data['addressLine2'],
      street: data['street'],
      city: data['city'] ?? '',
      state: data['state'],
      zipCode: data['zipCode'],
      country: data['country'] ?? '',
      type: data['type'] ?? 'Shipping',
      isDefault: data['isDefault'] ?? false,
      notes: data['notes'],
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      createdByUserId: data['createdByUserId'] ?? '',
      updatedByUserId: data['updatedByUserId'],
    );
  }

  // CopyWith method for immutability and easy updates
  AddressModel copyWith({
    String? id,
    String? accountId, // NEW: Add to copyWith
    String? addressLine1,
    String? addressLine2,
    String? street,
    String? city,
    String? state,
    String? zipCode,
    String? country,
    String? type,
    bool? isDefault,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByUserId,
    String? updatedByUserId,
  }) {
    return AddressModel(
      id: id ?? this.id,
      accountId: accountId ?? this.accountId, // NEW: Copy account ID
      addressLine1: addressLine1 ?? this.addressLine1,
      addressLine2: addressLine2 ?? this.addressLine2,
      street: street ?? this.street,
      city: city ?? this.city,
      state: state ?? this.state,
      zipCode: zipCode ?? this.zipCode,
      country: country ?? this.country,
      type: type ?? this.type,
      isDefault: isDefault ?? this.isDefault,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByUserId: createdByUserId ?? this.createdByUserId,
      updatedByUserId: updatedByUserId ?? this.updatedByUserId,
    );
  }

  /// Override toString to provide a readable representation of the address.
  @override
  String toString() {
    return '$street, $city, $state, $country';
  }
}
