import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/appbar/appbar.dart';
import '../../../../../common/widgets/forms/form_section.dart';
import '../../../../../utils/constants/text_strings.dart';
import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../controllers/deal_controller.dart';
import '../../../controllers/deal_item_controller.dart';
import '../../../models/deal_model.dart';

class EditDealMobile extends StatelessWidget {
  final DealModel deal;

  const EditDealMobile({super.key, required this.deal});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealController>();

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: TAppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Edit Deal', style: Theme.of(context).textTheme.headlineSmall),
              Text(deal.dealNumber, style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
          showBackArrow: true,
          actions: [_buildStatusIndicator(context)],
        ),
        body: Column(
          children: [
            TabBar(
              tabs: [
                Tab(icon: Icon(Iconsax.document), text: 'Basic Info'),
                Tab(icon: Icon(Iconsax.box), text: 'Items'),
              ],
            ),
            Expanded(
              child: TabBarView(
                children: [_buildBasicInfoTab(context, controller), _buildItemsTab(context, controller)],
              ),
            ),
          ],
        ),
        bottomNavigationBar: _buildBottomBar(context, controller),
      ),
    );
  }

  Widget _buildStatusIndicator(BuildContext context) {
    final statusColor = _getStatusColor(deal.status);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
        border: Border.all(color: statusColor),
      ),
      child: Text(
        deal.status.name.toUpperCase(),
        style: Theme.of(context).textTheme.labelSmall?.copyWith(color: statusColor, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildBasicInfoTab(BuildContext context, DealController controller) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Column(
        children: [
          TFormSection(
            title: 'Deal Information',
            children: [
              TextFormField(
                initialValue: deal.dealNumber,
                enabled: false,
                decoration: InputDecoration(
                  labelText: 'Deal Number',
                  prefixIcon: Icon(Iconsax.document),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                ),
              ),
              const SizedBox(height: TSizes.spaceBtwInputFields),
              TextFormField(
                initialValue: deal.projectDetails,
                onChanged: (value) => controller.projectDetailsController.text = value,
                decoration: InputDecoration(
                  labelText: 'Project Details',
                  prefixIcon: Icon(Iconsax.edit),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                ),
              ),
              const SizedBox(height: TSizes.spaceBtwInputFields),
              TextFormField(
                initialValue: deal.clientName,
                enabled: false,
                decoration: InputDecoration(
                  labelText: 'Client Name',
                  prefixIcon: Icon(Iconsax.user),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwSections),

          TFormSection(
            title: 'Deal Details',
            children: [
              DropdownButtonFormField<DealPriority>(
                value: deal.priority,
                items: DealPriority.values
                    .map(
                      (priority) =>
                          DropdownMenuItem<DealPriority>(value: priority, child: Text(priority.name.toUpperCase())),
                    )
                    .toList(),
                onChanged: (value) {
                  // Update the deal priority - we'll handle this in the save method
                },
                decoration: InputDecoration(
                  labelText: 'Priority',
                  prefixIcon: Icon(Iconsax.flag),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                ),
              ),
              const SizedBox(height: TSizes.spaceBtwInputFields),
              TextFormField(
                initialValue: deal.salesComments ?? '',
                onChanged: (value) => controller.salesNotesController.text = value,
                maxLines: 3,
                decoration: InputDecoration(
                  labelText: 'Sales Comments',
                  prefixIcon: Icon(Iconsax.message),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildItemsTab(BuildContext context, DealController controller) {
    return Padding(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Iconsax.box, color: TColors.primary),
              const SizedBox(width: TSizes.sm),
              Text('Deal Items', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () {
                  // Navigate to detailed specification form for adding items
                  Get.toNamed('/detailed-specification', arguments: deal);
                },
                icon: const Icon(Iconsax.add),
                label: const Text('Add Items'),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),
          Expanded(
            child: Obx(() {
              final itemController = Get.put(DealItemController());
              itemController.loadDealItems(deal.id);

              if (itemController.isLoading.value) {
                return Center(child: CircularProgressIndicator());
              }

              return itemController.dealItems.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Iconsax.box, size: 64, color: Colors.grey[400]),
                          const SizedBox(height: TSizes.spaceBtwItems),
                          Text(
                            'No items added yet',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                          ),
                          const SizedBox(height: TSizes.sm),
                          Text(
                            'Add items to this deal using the button above',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: itemController.dealItems.length,
                      itemBuilder: (context, index) {
                        final item = itemController.dealItems[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: TSizes.sm),
                          child: ListTile(
                            leading: Icon(Iconsax.box, color: TColors.primary),
                            title: Text(item.productName),
                            subtitle: Text('Qty: ${item.quotedQuantity} ${item.quotedUnit}'),
                            trailing: Text(
                              'AED ${item.totalItemPrice.toStringAsFixed(2)}',
                              style: Theme.of(
                                context,
                              ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold, color: TColors.primary),
                            ),
                          ),
                        );
                      },
                    );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomBar(BuildContext context, DealController controller) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [BoxShadow(color: Colors.black.withValues(alpha: 0.1), blurRadius: 10, offset: const Offset(0, -2))],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ),
          const SizedBox(width: TSizes.sm),
          Expanded(
            flex: 2,
            child: ElevatedButton(onPressed: () => controller.updateDeal(deal), child: const Text('Save Changes')),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Colors.grey;
      case DealStatus.pendingApproval:
        return Colors.orange;
      case DealStatus.approved:
        return Colors.green;
      case DealStatus.rejected:
        return Colors.red;
      case DealStatus.quotationGenerated:
        return Colors.indigo;
      case DealStatus.clientApproved:
        return Colors.teal;
      case DealStatus.clientDeclined:
        return Colors.red.shade300;
      case DealStatus.closed:
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
