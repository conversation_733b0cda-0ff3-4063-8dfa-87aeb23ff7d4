import 'package:excel/excel.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:universal_html/html.dart' show AnchorElement, Blob, Url;

import '../../../features/account/models/account_model.dart';
import '../../../features/authentication/models/user_model.dart';
import '../../../features/contact/models/contact_model.dart';
import '../models/report_config.dart';

class ExcelService {
  /// Exports data to an Excel file using provided configuration and raw data models.
  Future<void> exportExcelReport(ReportConfig config, List<dynamic> models) async {
    final excel = Excel.createExcel();
    _deleteDefaultSheet(excel);

    String sheetName = config.title.replaceAll(' ', '_').substring(0, config.title.length > 31 ? 31 : config.title.length); // Excel sheet name max 31 chars
    final Sheet sheet = excel[sheetName];

    // Add headers from config
    _addHeaderRow(sheet, config.columns.map((c) => c.headerText).toList());

    // Add data rows
    // For Excel, nested reports (like Accounts with Contacts) usually need to be flattened
    if (config.isNestedReport) {
      if (config.title == ReportConfig.accountsWithContactsReport.title) {
        // Specific logic for this nested report
        for (var model in models) {
          if (model is AccountModel) {
            if (model.contactDetails != null && model.contactDetails!.isNotEmpty) {
              for (var contact in model.contactDetails!) {
                sheet.appendRow([
                  TextCellValue(model.name),
                  TextCellValue(model.businessType.name.capitalizeFirst!),
                  TextCellValue(model.phone ?? 'N/A'),
                  TextCellValue(model.status.name.capitalizeFirst!),
                  TextCellValue(contact.name),
                  TextCellValue(contact.designation ?? 'N/A'),
                  TextCellValue(contact.phone),
                  TextCellValue(contact.email ?? 'N/A'),
                ]);
              }
            } else {
              // Account with no contacts - still add the account row with empty contact fields
              sheet.appendRow([
                TextCellValue(model.name),
                TextCellValue(model.businessType.name.capitalizeFirst!),
                TextCellValue(model.phone ?? 'N/A'),
                TextCellValue(model.status.name.capitalizeFirst!),
                TextCellValue('No Contacts'),
                TextCellValue(''),
                TextCellValue(''),
                TextCellValue(''),
              ]);
            }
          }
        }
      } else {
        // Fallback for other potential nested reports if not explicitly handled
        _addGenericDataRows(sheet, config, models);
      }
    } else {
      // For simple tabular reports
      _addGenericDataRows(sheet, config, models);
    }

    await _saveExcel(excel, 'BonnMetals_${config.filenamePrefix}_Report');
  }

  /// Helper to convert a dynamic model object into a Map<String, dynamic>.
  Map<String, dynamic> _modelToMap(dynamic model) {
    if (model is AccountModel) return model.toJson();
    if (model is ContactModel) return model.toJson();
    if (model is UserModel) return model.toJson();
    // Add other models here
    return {};
  }

  /// Adds rows for generic tabular reports.
  void _addGenericDataRows(Sheet sheet, ReportConfig config, List<dynamic> models) {
    for (var model in models) {
      final modelMap = _modelToMap(model);
      sheet.appendRow(
        config.columns.map((column) {
          dynamic value = modelMap[column.fieldName];
          dynamic formattedValue = column.formatter != null ? column.formatter!(value, model) : value?.toString();
          return TextCellValue(formattedValue ?? 'N/A');
        }).toList(),
      );
    }
  }

  void _deleteDefaultSheet(Excel excel) {
    final String initialDefaultSheetName = excel.sheets.keys.first;
    excel.delete(initialDefaultSheetName);
  }

  void _addHeaderRow(Sheet sheet, List<String> headers) {
    List<CellValue> headerCells = headers.map((h) => TextCellValue(h)).toList();
    sheet.appendRow(headerCells);
  }

  Future<void> _saveExcel(Excel excel, String filename) async {
    final bytes = excel.save(fileName: '$filename.xlsx');
    if (bytes == null) {
      throw Exception('Failed to generate Excel bytes.');
    }

    final Uint8List fileBytes = Uint8List.fromList(bytes);

    if (kIsWeb) {
      final Blob blob = Blob([fileBytes], 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      final url = Url.createObjectUrlFromBlob(blob);
      AnchorElement(href: url)
        ..setAttribute('download', '$filename.xlsx')
        ..click();
      await Future.delayed(const Duration(milliseconds: 100));
      Url.revokeObjectUrl(url);
    } else {
      await FileSaver.instance.saveFile(name: '$filename.xlsx', bytes: fileBytes, ext: 'xlsx', mimeType: MimeType.microsoftExcel);
    }
  }
}
