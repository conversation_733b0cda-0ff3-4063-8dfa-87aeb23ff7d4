import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/features/contact/screens/edit_contact/widgets/edit_contact_form.dart';
import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';

class EditContactScreen extends StatelessWidget {
  const EditContactScreen({super.key, required this.contact});

  final ContactModel contact;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            children: [
              // Breadcrumbs
              TBreadcrumbsWithHeading(
                heading: 'Back to Contacts List',
                breadcrumbItems: [
                  const TBreadcrumbItem(text: 'Contacts', route: TRoutes.contacts),
                  TBreadcrumbItem(text: 'Edit ${contact.name}'),
                ],
                showBackButton: true,
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              EditContactForm(contact: contact),
            ],
          ),
        ),
      ),
    );
  }
}
