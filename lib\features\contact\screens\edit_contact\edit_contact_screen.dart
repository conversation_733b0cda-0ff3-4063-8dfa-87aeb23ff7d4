import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/features/contact/screens/edit_contact/widgets/edit_contact_form.dart';
import 'package:flutter/material.dart';

class EditContactScreen extends StatelessWidget {
  const EditContactScreen({super.key, required this.contact});

  final ContactModel contact;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Edit ${contact.name}')),
      body: SingleChildScrollView(
        child: Center(child: EditContactForm(contact: contact)),
      ),
    );
  }
}
