import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/brand/screens/edit_brand/responsive_screens/edit_brand_desktop.dart';
import 'package:alloy/features/brand/screens/edit_brand/responsive_screens/edit_brand_mobile.dart';
import 'package:alloy/features/brand/screens/edit_brand/responsive_screens/edit_brand_tablet.dart';

class EditBrandScreen extends StatelessWidget {
  const EditBrandScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // read arguments from the url
    final brand = Get.arguments;

    return TSiteTemplate(
      useLayout: true,
      desktop: EditBrandDesktop(brand: brand),
      tablet: EditBrandTablet(brand: brand),
      mobile: EditBrandMobile(brand: brand),
    );
  }
}
