import 'dart:typed_data';

import 'package:alloy/utils/device/device_utility.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dropzone/flutter_dropzone.dart';
import 'package:get/state_manager.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/images/t_rounded_image.dart';
import 'package:alloy/common/media/controller/media_controller.dart';
import 'package:alloy/common/media/models/image_model.dart';
import 'package:alloy/common/media/widgets/folder_dropdown.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/sizes.dart';

/// use this package as html to access files from web

class MediaUploader extends StatelessWidget {
  const MediaUploader({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = MediaController.instance;
    return Obx(
      /// Show the whole uploader screen when Upload Images button is pressed
      () => controller.showImagesUploaderSection.value
          ? Column(
              children: [
                /// Drag & Drop Area
                TRoundedContainer(
                  height: 250,
                  showBorder: true,
                  borderColor: TColors.borderPrimary,
                  backgroundColor: TColors.primaryBackground,
                  padding: EdgeInsets.all(TSizes.defaultSpace),
                  child: Column(
                    children: [
                      Expanded(
                        child: Stack(
                          alignment: Alignment.center,
                          children: [
                            DropzoneView(
                              mime: ['image/png', 'image/jpeg', 'image/jpg'],
                              cursor: CursorType.Default,
                              operation: DragOperation.copy,
                              onLoaded: () => print(' Zone Loaded'),
                              onError: (ev) => print('Zone Error: $ev'),
                              onHover: () => print('Zone Hover'),
                              onLeave: () => print('Zone Leave'),
                              onCreated: (ctrl) => controller.dropzoneViewController = ctrl,
                              onDropInvalid: (ev) => print('Zone Drop Invalid: $ev'),
                              onDropFiles: (ev) async {
                                //  print('Zone Drop Files: $ev');
                              },
                              onDropFile: (file) async {
                                final bytes = await controller.dropzoneViewController.getFileData(file);
                                // Extract file metadata
                                final filename = await controller.dropzoneViewController.getFilename(file);
                                final mimeType = await controller.dropzoneViewController.getFileMIME(file);

                                final image = ImageModel(
                                  url: '',
                                  folder: '',
                                  filename: filename,
                                  contentType: mimeType,
                                  localImageToDisplay: Uint8List.fromList(bytes),
                                );
                                controller.selectedImagesToUpload.add(image);
                                // } else if (file is String) {
                                //   print('Zone Drop: $file');
                                // } else {
                                //   print('Zone Unknown type: ${file.name}');
                                // }
                              },
                            ),
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Image.asset(TImages.defaultMultiImageIcon, height: 50, width: 50),
                                const SizedBox(height: TSizes.spaceBtwItems),
                                Text('Drag & Drop Images Here', style: TextStyle(color: TColors.primary)),
                                const SizedBox(height: TSizes.spaceBtwItems),
                                OutlinedButton(onPressed: () => controller.selecLocalImages(), child: const Text('Select Images')),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                /// Heading and Locally Selected Images
                if (controller.selectedImagesToUpload.isNotEmpty)
                  TRoundedContainer(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        /// Heading
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              children: [
                                /// Folder Dropdown
                                Text('Select Folder', style: Theme.of(context).textTheme.headlineSmall),
                                const SizedBox(width: TSizes.spaceBtwItems),

                                // Folder List
                                MediaFolderDropdown(
                                  onChanged: (MediaCategory? newValue) {
                                    if (newValue != null) {
                                      controller.selectedPath.value = newValue;
                                    }
                                  },
                                ),
                              ],
                            ),

                            /// Upload and Remove Button
                            Row(
                              children: [
                                TextButton(onPressed: () => controller.selectedImagesToUpload.clear(), child: const Text("Remove All")),
                                const SizedBox(width: TSizes.spaceBtwItems),
                                TDeviceUtils.isMobileScreen(context)
                                    ? const SizedBox.shrink()
                                    : SizedBox(
                                        width: TSizes.buttonWidth,
                                        child: ElevatedButton(onPressed: () => controller.uploadImagesConfirmation(), child: const Text("Upload")),
                                      ),
                              ],
                            ),
                          ],
                        ),
                        const SizedBox(height: TSizes.spaceBtwSections),

                        Wrap(
                          alignment: WrapAlignment.start,
                          spacing: TSizes.spaceBtwItems / 2,
                          runSpacing: TSizes.spaceBtwItems / 2,
                          children: controller.selectedImagesToUpload
                              .where((image) => image.localImageToDisplay != null)
                              .map(
                                (image) => TRoundedImage(
                                  width: 90,
                                  height: 90,
                                  padding: TSizes.sm,
                                  imageType: ImageType.memory,
                                  memoryImage: image.localImageToDisplay,
                                  backgroundColor: TColors.primaryBackground,
                                ),
                              )
                              .toList(),
                        ),
                        const SizedBox(height: TSizes.spaceBtwItems),
                        TDeviceUtils.isMobileScreen(context)
                            ? SizedBox(
                                width: double.infinity,
                                child: ElevatedButton(onPressed: () => controller.uploadImagesConfirmation(), child: const Text("Upload")),
                              )
                            : const SizedBox.shrink(),
                      ],
                    ),
                  ),
              ],
            )
          : const SizedBox.shrink(),
    );
  }
}
