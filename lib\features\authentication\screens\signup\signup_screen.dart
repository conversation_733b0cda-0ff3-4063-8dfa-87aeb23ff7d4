import 'package:alloy/common/layouts/templates/login_template.dart';
import 'package:alloy/features/authentication/controllers/signup_controller.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class SignupScreen extends StatelessWidget {
  const SignupScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SignupController());
    return Scaffold(
      appBar: AppBar(title: const Text('Create Account')),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Form(
            key: controller.formKey,
            child: TLoginTemplate(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Let's create an account", style: Theme.of(context).textTheme.headlineMedium),
                  const SizedBox(height: TSizes.spaceBtwSections),

                  // First Name
                  TextFormField(
                    controller: controller.firstName,
                    validator: (value) => TValidator.validateEmptyText('First name', value),
                    decoration: const InputDecoration(labelText: TTexts.firstName, prefixIcon: Icon(Iconsax.user)),
                  ),
                  const SizedBox(height: TSizes.spaceBtwInputFields),

                  // Last Name (Nullable)
                  TextFormField(
                    controller: controller.lastName,
                    decoration: const InputDecoration(labelText: TTexts.lastName, prefixIcon: Icon(Iconsax.user)),
                  ),
                  const SizedBox(height: TSizes.spaceBtwInputFields),

                  // Username
                  TextFormField(
                    controller: controller.userName,
                    validator: (value) => TValidator.validateEmptyText('Username', value),
                    decoration: const InputDecoration(labelText: TTexts.username, prefixIcon: Icon(Iconsax.user_edit)),
                  ),
                  const SizedBox(height: TSizes.spaceBtwInputFields),

                  // Email
                  TextFormField(
                    controller: controller.email,
                    validator: TValidator.validateEmail,
                    decoration: const InputDecoration(labelText: TTexts.email, prefixIcon: Icon(Iconsax.direct)),
                  ),
                  const SizedBox(height: TSizes.spaceBtwInputFields),

                  // Phone Number (Nullable)
                  TextFormField(
                    controller: controller.phoneNumber,
                    decoration: const InputDecoration(labelText: TTexts.phoneNo, prefixIcon: Icon(Iconsax.call)),
                  ),
                  const SizedBox(height: TSizes.spaceBtwInputFields),

                  // Department
                  TextFormField(
                    controller: controller.department,
                    validator: (value) => TValidator.validateEmptyText('Department', value),
                    decoration: const InputDecoration(labelText: 'Department', prefixIcon: Icon(Iconsax.building)),
                  ),
                  const SizedBox(height: TSizes.spaceBtwInputFields),

                  // Password
                  Obx(
                    () => TextFormField(
                      controller: controller.password,
                      validator: TValidator.validatePassword,
                      obscureText: controller.hidePassword.value,
                      decoration: InputDecoration(
                        labelText: TTexts.password,
                        prefixIcon: const Icon(Iconsax.password_check),
                        suffixIcon: IconButton(
                          onPressed: () => controller.hidePassword.value = !controller.hidePassword.value,
                          icon: Icon(controller.hidePassword.value ? Iconsax.eye_slash : Iconsax.eye),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: TSizes.spaceBtwInputFields),

                  // Role Dropdown
                  Obx(
                    () => DropdownButtonFormField<AppRole>(
                      decoration: const InputDecoration(labelText: 'Role', prefixIcon: Icon(Iconsax.user_octagon)),
                      value: controller.selectedRole.value,
                      items: AppRole.values
                          .map(
                            (role) => DropdownMenuItem(
                              value: role,
                              child: Text(role.name.capitalize!), // Capitalize for display
                            ),
                          )
                          .toList(),
                      onChanged: (value) {
                        if (value != null) {
                          controller.selectedRole.value = value;
                        }
                      },
                      validator: (value) => value == null ? 'Please select a role' : null,
                    ),
                  ),
                  const SizedBox(height: TSizes.spaceBtwSections),

                  // Signup Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () => controller.signup(),
                      child: const Text(TTexts.createAccount),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

// Helper extension for String capitalization (optional, place in a utility file)
extension StringExtension on String {
  String? get capitalizeFirst {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1)}';
  }
}
