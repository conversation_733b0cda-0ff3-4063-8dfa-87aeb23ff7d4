import 'package:alloy/features/account/models/account_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:alloy/utils/exceptions/firebase_exceptions.dart';
import 'package:alloy/utils/exceptions/format_exceptions.dart';
import 'package:alloy/utils/exceptions/platform_exceptions.dart';

/// Repository for managing Account data in Firestore.
class AccountRepository extends GetxController {
  static AccountRepository get instance => Get.find();

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Main collection reference for Accounts
  final CollectionReference<AccountModel> _accountsCollection = FirebaseFirestore.instance
      .collection(TTexts.accounts) // Using TTexts.accountsCollection
      .withConverter<AccountModel>(
        fromFirestore: (snapshot, options) => AccountModel.fromSnapshot(snapshot),
        toFirestore: (account, options) => account.toJson(),
      );

  // Reference for Contacts collection (for denormalization)
  final CollectionReference<Map<String, dynamic>> _contactsCollection = FirebaseFirestore.instance.collection(
    TTexts.contacts,
  );

  /// Get all accounts
  Future<List<AccountModel>> getAllAccounts() async {
    try {
      final snapshot = await _accountsCollection.get(); // Using _accountsCollection
      return snapshot.docs.map((document) => document.data()).toList(); // Using .data() with converter
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again: $e';
    }
  }

  /// Stream all accounts from Firestore
  Stream<List<AccountModel>> streamAllAccounts({String? searchQuery}) {
    Query<AccountModel> query = _accountsCollection.orderBy('name'); // Using _accountsCollection

    return query.snapshots().map((snapshot) {
      List<AccountModel> accounts = snapshot.docs.map((doc) => doc.data()).toList();

      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        accounts = accounts.where((account) {
          return account.name.toLowerCase().contains(searchLower) ||
              (account.alsoKnownAs?.toLowerCase().contains(searchLower) ?? false) ||
              (account.phone?.toLowerCase().contains(searchLower) ?? false) ||
              (account.email?.toLowerCase().contains(searchLower) ?? false);
        }).toList();
      }
      return accounts;
    });
  }

  /// NEW: Stream accounts that are marked as parents.
  Stream<List<AccountModel>> streamParentAccounts() {
    return _accountsCollection
        .where('isParent', isEqualTo: true)
        .orderBy('name')
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.data()).toList());
  }

  /// Create Account
  Future<String> createAccount(AccountModel newAccount) async {
    try {
      final docRef = await _accountsCollection.add(newAccount); // Using _accountsCollection.add directly with converter
      return docRef.id;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } catch (e) {
      throw 'Something went wrong while creating account: $e';
    }
  }

  /// Create multiple accounts in a batch
  Future<void> createAccountsInBatch(List<AccountModel> accounts) async {
    try {
      final batch = _db.batch();
      for (final account in accounts) {
        final docRef = _accountsCollection.doc(); // Using _accountsCollection.doc
        // account.toJson() is handled by the converter, no need to manually add CreatedAt
        batch.set(docRef, account); // Set the model directly
      }
      await batch.commit();
    } catch (e) {
      throw 'An unexpected error occurred while batch creating accounts: $e';
    }
  }

  /// Fetches a single Account by its ID.
  Future<AccountModel> getAccountById(String accountId) async {
    try {
      final documentSnapshot = await _accountsCollection.doc(accountId).get();
      if (documentSnapshot.exists) {
        return documentSnapshot.data()!;
      } else {
        return AccountModel.empty(); // Return empty model if not found
      }
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while fetching account: $e';
    }
  }

  /// Update Account
  /// This method now handles denormalization of account details into associated contacts.
  Future<void> updateAccount(AccountModel updatedAccount, AccountModel originalAccount) async {
    try {
      final batch = _db.batch();
      final accountRef = _accountsCollection.doc(updatedAccount.id); // Get DocumentReference

      // 1. Update the main Account document
      batch.update(accountRef, updatedAccount.toJson()); // Use toJson() for direct update

      // 2. Determine contacts to add/remove references from
      final Set<String> originalContactIds = originalAccount.contactDetails?.map((c) => c.id).toSet() ?? {};
      final Set<String> updatedContactIds = updatedAccount.contactDetails?.map((c) => c.id).toSet() ?? {};

      // Get the denormalized account data to add/remove
      final Map<String, dynamic> accountSubJson = updatedAccount.toSubJson();

      // Contacts to add (newly assigned to this account)
      final Iterable<String> contactsToAddRefTo = updatedContactIds.difference(originalContactIds);
      for (final contactId in contactsToAddRefTo) {
        final contactRef = _contactsCollection.doc(contactId); // Use _contactsCollection
        batch.update(contactRef, {
          'accountDetails': FieldValue.arrayUnion([accountSubJson]),
          'updatedAt': FieldValue.serverTimestamp(), // Update timestamp on associated contact
        });
      }

      // Contacts to remove (no longer assigned to this account)
      final Iterable<String> contactsToRemoveRefFrom = originalContactIds.difference(updatedContactIds);
      for (final contactId in contactsToRemoveRefFrom) {
        final contactRef = _contactsCollection.doc(contactId); // Use _contactsCollection
        batch.update(contactRef, {
          'accountDetails': FieldValue.arrayRemove([accountSubJson]), // Use arrayRemove
          'updatedAt': FieldValue.serverTimestamp(), // Update timestamp on associated contact
        });
      }

      // IMPORTANT: Commit the batch AFTER all operations are added
      await batch.commit();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } catch (e) {
      // Catch any other unexpected errors during batch setup
      throw 'Failed to update account relationships: $e';
    }
  }

  /// Updates specific fields of an existing Account document.
  /// This is a more generic update method, used by AddressRepository.
  Future<void> updateAccountFields(String accountId, Map<String, dynamic> fields) async {
    try {
      await _accountsCollection.doc(accountId).update(fields);
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while updating account fields: $e';
    }
  }

  Future<bool> isDuplicateAccountName(String name) async {
    final snapshot = await _accountsCollection
        .where('name', isEqualTo: name)
        .limit(1)
        .get(); // Using _accountsCollection and 'name' field
    return snapshot.docs.isNotEmpty;
  }

  /// Delete Account
  Future<void> deleteAccount(String accountId) async {
    // Changed to take ID as per other methods
    try {
      // TODO: Implement cascading delete for associated contacts and addresses
      // For now, just deletes the main account document.
      // You would typically use Cloud Functions for robust cascading deletes.
      await _accountsCollection.doc(accountId).delete();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while deleting account: $e';
    }
  }
}
