import 'package:alloy/common/widgets/chips/choice_chip_list.dart';
import 'package:alloy/common/widgets/chips/name_chip.dart';
import 'package:alloy/common/widgets/data_table/table_action_buttons.dart';
import 'package:alloy/features/contact/controller/contact_controller.dart';
import 'package:alloy/features/contact/screens/edit_contact/edit_contact_screen.dart';
import 'package:alloy/utils/helpers/helper_functions.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ContactsRows extends DataTableSource {
  final controller = Get.find<ContactController>();

  @override
  DataRow2? getRow(int index) {
    final contact = controller.filteredItems[index];
    return DataRow2(
      selected: controller.selectedRows.length > index ? controller.selectedRows[index] : false,
      onSelectChanged: (isSelected) {
        if (controller.selectedRows.length > index) {
          controller.selectedRows[index] = isSelected ?? false;
        }
      },
      cells: [
        DataCell(Text(contact.name)),
        Data<PERSON>ell(Text(contact.email ?? '')),
        DataCell(Text(contact.phone)),
        DataCell(Text(contact.designation ?? '')),
        DataCell(
          TChoiceChipList(
            status: contact.active ? 'Active' : 'Inactive',
            labels: const {'Active': 'Active', 'Inactive': 'Inactive'},
          ),
        ),
        DataCell(
          Wrap(
            spacing: 4.0, // Adjust spacing for the new chips
            runSpacing: 4.0,
            children:
                contact.accountDetails?.map((account) {
                  final color = THelperFunctions.generateAvatarColor(account.id);
                  return TNameChip(
                    tooltipText: account.name,
                    text: account.name,
                    backgroundColor: color,
                    // You can adjust textColor if needed, e.g., using THelperFunctions.getContrastingTextColor(color)
                  );
                }).toList() ??
                [],
          ),
        ),
        DataCell(
          TTableActionButtons(
            onEditPressed: () => Get.to(() => EditContactScreen(contact: contact)),
            onDeletePressed: () => controller.confirmDeleteItem(contact),
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => controller.filteredItems.length;

  @override
  int get selectedRowCount => controller.selectedRows.where((isSelected) => isSelected).length;
}
