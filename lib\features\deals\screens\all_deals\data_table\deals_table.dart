import 'package:alloy/common/widgets/data_table/paginated_data_table.dart';
import 'package:alloy/features/deals/controllers/deal_controller.dart';
import 'package:alloy/features/deals/models/deal_model.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'table_source.dart';

class DealsTable extends StatelessWidget {
  const DealsTable({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealController>();
    final isMobile = TDeviceUtils.isMobileScreen(context);

    return Obx(() {
      // Trigger reactive updates
      Text(controller.filteredItems.length.toString());
      Text(controller.selectedRows.length.toString());

      return TPaginatedDataTable(
        minWidth: 1400,
        sortAscending: controller.sortAscending.value,
        sortColumnIndex: controller.sortColumnIndex.value,
        columns: [
          DataColumn2(
            label: const Text('Deal Number'),
            fixedWidth: isMobile ? null : 140,
            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (DealModel deal) => deal.dealNumber.toLowerCase()),
          ),
          DataColumn2(
            label: const Text('Client'),
            fixedWidth: isMobile ? null : 180,
            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (DealModel deal) => deal.clientName.toLowerCase()),
          ),
          DataColumn2(
            label: const Text('Sales Person'),
            fixedWidth: isMobile ? null : 150,
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (DealModel deal) => deal.salesPersonName.toLowerCase(),
            ),
          ),
          DataColumn2(
            label: const Text('Status'),
            fixedWidth: isMobile ? null : 120,
            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (DealModel deal) => deal.status.name.toLowerCase()),
          ),
          DataColumn2(
            label: const Text('Total Amount'),
            fixedWidth: isMobile ? null : 130,
            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (DealModel deal) => deal.grandTotalAmount),
          ),
          DataColumn2(
            label: const Text('Total Weight'),
            fixedWidth: isMobile ? null : 120,
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (DealModel deal) => 0.0,
            ), // TODO: Calculate from deal items
          ),
          DataColumn2(
            label: const Text('Progress'),
            fixedWidth: isMobile ? null : 100,
            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (DealModel deal) => deal.progressPercentage),
          ),
          DataColumn2(
            label: const Text('Created Date'),
            fixedWidth: isMobile ? null : 120,
            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (DealModel deal) => deal.createdAt ?? DateTime.now()),
          ),
          DataColumn2(label: const Text('Actions'), fixedWidth: isMobile ? null : 120),
        ],
        source: DealsRows(),
      );
    });
  }
}
