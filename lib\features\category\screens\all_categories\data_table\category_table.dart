import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class CategoryTable extends StatelessWidget {
  const CategoryTable({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<CategoryController>();
    return Obx(
      () => DataTable2(
        columnSpacing: 12,
        horizontalMargin: 12,
        columns: const [
          DataColumn2(label: Text('Name')),
          DataColumn2(label: Text('Actions'), fixedWidth: 100),
        ],
        rows: List.generate(controller.filteredItems.length, (index) {
          final category = controller.filteredItems[index];
          return DataRow2(
            cells: [
              DataCell(Text(category.name)),
              DataCell(
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      onPressed: () => controller.confirmDeleteItem(category),
                      icon: const Icon(Iconsax.trash, color: TColors.error),
                    ),
                  ],
                ),
              ),
            ],
          );
        }),
      ),
    );
  }
}
