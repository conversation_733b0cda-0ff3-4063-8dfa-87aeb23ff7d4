import 'package:cloud_firestore/cloud_firestore.dart';

class CategoryModel {
  String id;
  String name;
  String? description;

  CategoryModel({required this.id, required this.name, this.description});

  static CategoryModel empty() => CategoryModel(id: '', name: '');

  Map<String, dynamic> toJson() {
    return {'Name': name, 'Description': description};
  }

  factory CategoryModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (document.data() == null) return CategoryModel.empty();
    final data = document.data()!;
    return CategoryModel(id: document.id, name: data['Name'] as String, description: data['Description'] as String?);
  }
}
