import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:get/get.dart';
import '../../../utils/constants/enums.dart';
import '../../category/controller/category_controller.dart';
import '../../category/models/category_model.dart';

class ProductModel {
  String id;
  String name;
  String description;
  String categoryId;
  ProductSegment segment;
  double width; // in mm
  double height; // in mm
  String? thumbnail; // URL to product thumbnail image
  DateTime? createdAt;
  ProductStatus status; // NEW: Product Status

  // Optional: For displaying category name directly if needed, not stored in Firestore
  CategoryModel? category;

  ProductModel({
    required this.id,
    required this.name,
    this.description = '',
    required this.categoryId,
    required this.segment,
    this.status = ProductStatus.active, // NEW: Default to active
    required this.width,
    required this.height,
    this.thumbnail, // Initialize thumbnail
    this.createdAt,
    this.category,
  });

  /// Empty Helper Function
  static ProductModel empty() => ProductModel(
    id: '',
    name: '',
    description: '',
    categoryId: '',
    segment: ProductSegment.lengths,
    status: ProductStatus.active, // NEW: Default to active
    width: 0.0,
    height: 0.0,
    thumbnail: null, // Default to null
  );

  /// CopyWith method for immutability
  ProductModel copyWith({
    String? id,
    String? name,
    String? description,
    String? categoryId,
    ProductSegment? segment,
    ProductStatus? status, // NEW: Add status to copyWith
    double? width,
    double? height,
    String? thumbnail,
    DateTime? createdAt,
    CategoryModel? category,
  }) {
    return ProductModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      categoryId: categoryId ?? this.categoryId,
      segment: segment ?? this.segment,
      status: status ?? this.status, // NEW: Copy status
      width: width ?? this.width,
      height: height ?? this.height,
      thumbnail: thumbnail ?? this.thumbnail,
      createdAt: createdAt ?? this.createdAt,
      category: category ?? this.category,
    );
  }

  /// Convert ProductModel to JSON format for Firestore.
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'categoryId': categoryId,
      'segment': segment.name, // Store enum as string
      'status': status.name, // NEW: Store status as string
      'width': width,
      'height': height,
      'thumbnail': thumbnail,
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
    };
  }

  /// Factory method to create a ProductModel from a Firestore DocumentSnapshot.
  /// This is the method typically used by `withConverter`.
  factory ProductModel.fromFirestore(DocumentSnapshot<Map<String, dynamic>> snapshot, [SnapshotOptions? options]) {
    // You can directly use your existing fromSnapshot logic here
    return ProductModel.fromSnapshot(snapshot);
  }

  /// Factory method to create a ProductModel from a Firestore document snapshot.
  /// This is your original method.
  factory ProductModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (document.data() == null) return ProductModel.empty();

    final data = document.data()!;

    // Safely parse segment enum
    ProductSegment parsedSegment = ProductSegment.lengths;
    try {
      if (data['segment'] != null) {
        parsedSegment = ProductSegment.values.firstWhere((e) => e.name == (data['segment'].toString().toLowerCase()), orElse: () => ProductSegment.lengths);
      }
    } catch (e) {
      print('Error parsing segment for product ${document.id}: $e');
    }

    // NEW: Safely parse status enum
    ProductStatus parsedStatus = ProductStatus.active; // Default
    try {
      if (data['status'] != null) {
        parsedStatus = ProductStatus.values.firstWhere((e) => e.name == (data['status'].toString().toLowerCase()), orElse: () => ProductStatus.active);
      }
    } catch (e) {
      print('Error parsing status for product ${document.id}: $e');
    }

    final product = ProductModel(
      id: document.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      categoryId: data['categoryId'] ?? '',
      segment: parsedSegment,
      status: parsedStatus, // NEW: Assign parsed status
      width: (data['width'] as num?)?.toDouble() ?? 0.0,
      height: (data['height'] as num?)?.toDouble() ?? 0.0,
      thumbnail: data['thumbnail'] as String?,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
    );

    // Populate the 'category' field for display convenience (not from Firestore directly)
    try {
      final categoryController = Get.find<CategoryController>();
      product.category = categoryController.allItems.firstWhereOrNull((cat) => cat.id == product.categoryId);
    } catch (e) {
      print('Error finding CategoryController or category for product: $e');
    }

    return product;
  }
}
