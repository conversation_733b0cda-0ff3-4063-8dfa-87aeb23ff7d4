import 'package:alloy/features/brand/controller/brand_controller.dart';
import 'package:alloy/features/brand/models/brand_category_model.dart';
import 'package:alloy/features/category/models/category_model.dart';
import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:alloy/features/brand/models/brand_model.dart';
import 'package:alloy/features/brand/repository/brand_repository.dart';
import 'package:alloy/common/media/controller/media_controller.dart';
import 'package:alloy/common/media/models/image_model.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';

class EditBrandController extends GetxController {
  static EditBrandController get instance => Get.find();

  /// Variables
  final isFeatured = false.obs;
  final isLoading = false.obs;
  final nameController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  final RxList<CategoryModel> selectedCategories = <CategoryModel>[].obs;

  RxString imageUrl = ''.obs;

  final repository = Get.put(BrandRepository());

  /// -- Initialize
  void initializeForm(BrandModel brand) {
    nameController.text = brand.name;
    isFeatured.value = brand.isFeatured;
    imageUrl.value = brand.image;
    if (brand.brandCategories != null) {
      selectedCategories.addAll(brand.brandCategories ?? []);
    }
  }

  /// -- Toggle Category Selection to add to the brandCategory relationship
  void toggleSelection(CategoryModel category) {
    // Check if the category is already selected
    if (selectedCategories.contains(category)) {
      // If yes, remove it from the list
      selectedCategories.remove(category);
    } else {
      // If no, add it to the list
      selectedCategories.add(category);
    }
  }

  /// -- Update Brand
  Future<void> updateBrand(BrandModel brandToUpdate) async {
    try {
      TFullScreenLoader.openLoadingDialog('Updating Brand', TImages.defaultLoaderAnimation);
      // OR JUST A CIRCULAR LOADER
      // TFullScreenLoader.popUpCircular();

      // Check Internet Connectivity
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        return;
      }

      // Form Validation
      if (!formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        return;
      }
      // Check if the brand is to be updated in case the name, image or isFeatured is changed
      bool isBrandUpdated = false;
      if (brandToUpdate.name != nameController.text.trim() || brandToUpdate.image != imageUrl.value || brandToUpdate.isFeatured != isFeatured.value) {
        isBrandUpdated = true;

        // Map New Values to the Brand Model
        brandToUpdate.name = nameController.text.trim();
        brandToUpdate.image = imageUrl.value;
        brandToUpdate.isFeatured = isFeatured.value;
        brandToUpdate.updatedAt = DateTime.now();

        // Save Brand
        await repository.updateBrand(brandToUpdate);
      }
      // Update Brand Categories. Dont update if no new categories are selected
      if (selectedCategories.isNotEmpty) {
        await updateBrandCategories(brandToUpdate);
      }

      //if (isBrandUpdated) await updateBrandInProducts(brandToUpdate);

      // Update All Data List
      BrandController.instance.updateItemInRxList(brandToUpdate);

      // Clear Form
      clearForm();

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Brand Updated', message: 'Brand ${brandToUpdate.name} updated successfully.');
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    } finally {
      // Remove Loader
      isLoading.value = false;
    }
  }

  // Update Categories for this Brand
  Future<void> updateBrandCategories(BrandModel brandToUpdate) async {
    // Get all existing brand categories
    final existingBrandCategories = await repository.getCategoriesOfSpecificBrand(brandToUpdate.id);

    // Selected Category IDs
    final selectedCategoryIds = selectedCategories.map((category) => category.id);

    // Identify categories to be removed
    final categoriesToRemove = existingBrandCategories
        .where(
          (existingBrandCategory) =>
              // If the existing category is not in the selected categories, remove it. Notice the ! sign
              !selectedCategoryIds.contains(existingBrandCategory.categoryId),
        )
        .toList();

    // Remove unselected categories
    for (final categoryToRemove in categoriesToRemove) {
      await repository.deleteBrandCategory(categoryToRemove.id ?? '');
    }

    // Identify new categories to be added
    final newCategoriesToAdd = selectedCategories
        .where((newCategory) => !existingBrandCategories.any((existingCategory) => existingCategory.categoryId == newCategory.id))
        .toList();

    // Add new categories
    for (final newCategory in newCategoriesToAdd) {
      final newBrandCategory = BrandCategoryModel(id: '', brandId: brandToUpdate.id, categoryId: newCategory.id);
      newBrandCategory.id = await repository.createBrandCategory(newBrandCategory);
    }
    brandToUpdate.brandCategories!.assignAll(selectedCategories);
    BrandController.instance.updateItemInRxList(brandToUpdate);
  }

  /// -- Pick Image while creating a category
  Future<void> pickImage() async {
    final controller = Get.put(MediaController());

    // Select Image from Media function has an inbuilt bottom sheet that returns a list of selected images
    List<ImageModel>? selectedImages = await controller.selectImagesFromMedia();

    // Handle the selected images
    if (selectedImages != null && selectedImages.isNotEmpty) {
      // Select the first image from the list if selected images (to make a thumbnail)
      ImageModel selectedImage = selectedImages.first;
      // Update the selected thumbnail image URL
      imageUrl.value = selectedImage.url;
    }
  }

  /// -- Reset Fields and Clear Form
  void clearForm() {
    nameController.clear();
    // isLoading(false);
    imageUrl.value = '';
    isFeatured(false);
  }
}
