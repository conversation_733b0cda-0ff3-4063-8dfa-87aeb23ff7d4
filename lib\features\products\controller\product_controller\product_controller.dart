import 'package:alloy/data/abstract/base_data_table_controller.dart';
import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/features/products/models/product_model.dart';
import 'package:alloy/features/products/repository/product_repository.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../utils/constants/enums.dart';
import '../../../../utils/helpers/network_manager.dart';
import '../../../../utils/helpers/product_variant_calculator.dart';
import '../../../../utils/popups/full_screen_loader.dart';
import '../../../../utils/popups/loaders.dart';
import '../../models/product_variant_model.dart';
import '../../repository/product_variant_repository.dart';

class ProductController extends TBaseController<ProductModel> {
  static ProductController get instance => Get.find();
  final _productRepository = Get.put(ProductRepository());
  final _categoryController = Get.put(CategoryController());
  final _variantRepository = ProductVariantRepository.instance;

  // GlobalKey for the form, used in both Create and Edit screens
  final formKey = GlobalKey<FormState>();

  // Reactive variable to control visibility of discontinued products (UI toggle)
  final RxBool showDiscontinuedProducts = false.obs;

  // NEW: Reactive variables for Advanced Filters
  final RxString selectedCategoryFilterId = ''.obs; // Filter by Category ID
  final Rx<ProductSegment?> selectedSegmentFilter = Rx<ProductSegment?>(null); // Filter by Segment
  final Rx<ProductStatus?> selectedStatusFilter = Rx<ProductStatus?>(null); // Filter by Status

  @override
  void onInit() {
    super.onInit();
    // Fetch categories when the controller initializes for filter dropdowns
    if (_categoryController.allItems.isEmpty) {
      _categoryController.fetchItems();
    }

    // Listen to changes in filter Rx variables to re-trigger streamItems
    debounce(selectedCategoryFilterId, (_) => listenToStream(), time: const Duration(milliseconds: 300));
    debounce(selectedSegmentFilter, (_) => listenToStream(), time: const Duration(milliseconds: 300));
    debounce(selectedStatusFilter, (_) => listenToStream(), time: const Duration(milliseconds: 300));
    debounce(
      showDiscontinuedProducts,
      (_) => listenToStream(),
      time: const Duration(milliseconds: 300),
    ); // Also listen to this toggle
  }

  @override
  Future<List<ProductModel>> fetchItems() async {
    // This method is less used with streams, but can be implemented for one-time fetches.
    // If you need a one-time fetch, you would call _productRepository.getAllProducts() here.
    return [];
  }

  @override
  Stream<List<ProductModel>> streamItems() {
    // Combine filter criteria into a map to pass to the repository
    Map<String, dynamic> filters = {};
    if (selectedCategoryFilterId.value.isNotEmpty) {
      filters['categoryId'] = selectedCategoryFilterId.value;
    }
    if (selectedSegmentFilter.value != null) {
      filters['segment'] = selectedSegmentFilter.value!.name; // Use enum name for Firestore
    }
    // Only apply status filter if it's explicitly selected (not null) AND
    // if 'showDiscontinuedProducts' is false (meaning we want to exclude discontinued).
    // If 'showDiscontinuedProducts' is true, we don't add a status filter here,
    // as the client-side filter will handle including all statuses.
    if (selectedStatusFilter.value != null && !showDiscontinuedProducts.value) {
      filters['status'] = selectedStatusFilter.value!.name;
    } else if (!showDiscontinuedProducts.value) {
      // If showDiscontinuedProducts is false, implicitly filter out discontinued
      // unless a specific status is selected. This might need careful handling
      // if you want to show 'inactive' but not 'discontinued'.
      // For now, if no status filter is explicitly chosen and showDiscontinuedProducts is false,
      // we'll filter out 'discontinued' at the client side.
      // The Firestore query will fetch based on other filters, then client-side refines.
      // To truly reduce reads for 'discontinued', we'd need to add a where clause:
      // filters['status_not_discontinued'] = true; // This would require a field in Firestore.
      // For now, let's keep it simple: if a status is selected, use it.
      // If showDiscontinuedProducts is false, the client-side filter will handle it.
    }

    return _productRepository.streamAllProducts(filters: filters).distinct().map((products) {
      // OPTIMIZED: Single-pass functional approach with immutable updates
      final categoryMap = {for (var category in _categoryController.allItems) category.id: category};

      return products
          .where((product) => showDiscontinuedProducts.value || product.status != ProductStatus.discontinued)
          .map((product) {
            // Create new instance instead of side effect
            final enrichedProduct = ProductModel(
              id: product.id,
              name: product.name,
              description: product.description,
              categoryId: product.categoryId,
              segment: product.segment,
              status: product.status,
              width: product.width,
              height: product.height,
              thumbnail: product.thumbnail,
              createdAt: product.createdAt,
              category: categoryMap[product.categoryId], // Assign category without side effect
            );
            return enrichedProduct;
          })
          .toList();
    });
  }

  @override
  bool containsSearchQuery(ProductModel item, String searchText) {
    final searchLower = searchText.toLowerCase();
    return item.name.toLowerCase().contains(searchLower) ||
        (item.description.toLowerCase().contains(searchLower)) ||
        (item.category?.name.toLowerCase().contains(searchLower) ?? false) ||
        item.segment.name.toLowerCase().contains(searchLower) ||
        item.status.name.toLowerCase().contains(searchLower);
  }

  @override
  Comparable getComparableProperty(ProductModel item, int columnIndex) {
    switch (columnIndex) {
      case 0:
        return item.thumbnail ?? ''; // For thumbnail column, sort by URL string or empty
      case 1:
        return item.name.toLowerCase();
      case 2:
        return item.category?.name.toLowerCase() ?? '';
      case 3:
        return item.segment.name.toLowerCase();
      case 4:
        return item.width; // Or combine width and height for sorting dimensions
      case 5:
        return item.status.name.toLowerCase(); // Assuming status is at index 5
      default:
        return ''; // Fallback for undefined columns
    }
  }

  @override
  Future<void> deleteItem(ProductModel item) {
    return _productRepository.deleteProduct(item.id);
  }

  /// --- Update Product ---
  Future<void> updateProduct(ProductModel product) async {
    try {
      TFullScreenLoader.popUpCircular();
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }
      await _productRepository.updateProduct(product);
      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Product "${product.name}" updated successfully!');
      listenToStream();
      Get.back();
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  /// --- Copy Product and its Variants ---
  Future<void> copyProduct(ProductModel originalProduct, String newName, {double? newWidth, double? newHeight}) async {
    try {
      TFullScreenLoader.popUpCircular();
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final copiedProduct = originalProduct.copyWith(
        id: '',
        name: newName.trim(),
        width: newWidth ?? originalProduct.width,
        height: newHeight ?? originalProduct.height,
        createdAt: null,
        status: ProductStatus.active,
      );

      final newProductId = await _productRepository.createProduct(copiedProduct);
      final newProductWithId = copiedProduct.copyWith(id: newProductId);

      final originalVariants = await _variantRepository.streamVariantsForProduct(originalProduct.id).first;

      final List<ProductVariantModel> newVariants = [];
      for (final originalVariant in originalVariants) {
        final Map<String, double> calculatedValues = ProductVariantCalculator.calculateWeightAndSqm(
          productCategoryName: newProductWithId.category?.name ?? '',
          widthMm: newProductWithId.width,
          heightMm: newProductWithId.height,
          lengthM: double.tryParse(originalVariant.attributes['Length']?.replaceAll('m', '').trim() ?? '0') ?? 0.0,
          thicknessMm:
              double.tryParse(originalVariant.attributes['Thickness']?.replaceAll('mm', '').trim() ?? '0') ?? 0.0,
          material: originalVariant.attributes['Material'] ?? '',
          productSegment: newProductWithId.segment,
        );

        newVariants.add(
          originalVariant.copyWith(
            id: '',
            productId: newProductId,
            sku: '${newName.replaceAll(' ', '-')}-${originalVariant.sku.split('-').skip(1).join('-')}',
            weight: calculatedValues['factoryWeightKg'],
            sqm: calculatedValues['factorySqm'],
            createdAt: null,
            updatedAt: null,
            status: ProductStatus.active,
          ),
        );
      }

      if (newVariants.isNotEmpty) {
        await _variantRepository.createVariantsInBatch(newVariants);
      }

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Product "$newName" and its variants copied successfully!');
      listenToStream();
      Get.back();
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: 'Failed to copy product: $e');
    }
  }

  /// NEW: Clears all advanced product filters.
  void clearFilters() {
    selectedCategoryFilterId.value = '';
    selectedSegmentFilter.value = null;
    selectedStatusFilter.value = null;
    // searchQuery.value = ''; // Also clear search query
    showDiscontinuedProducts.value = false; // Reset this toggle too
    listenToStream(); // Re-fetch data with cleared filters
  }
}
