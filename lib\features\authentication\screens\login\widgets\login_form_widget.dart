import 'package:alloy/features/authentication/controllers/login_controller.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class TLoginFormWidget extends StatelessWidget {
  const TLoginFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = LoginController.instance;
    return Form(
      key: controller.loginForm<PERSON>ey,
      child: Padding(
        padding: EdgeInsetsGeometry.symmetric(vertical: TSizes.spaceBtwSections),
        child: Column(
          children: [
            TextFormField(
              controller: controller.email,
              validator: (value) => TValidator.validateEmptyText('Username', value),
              decoration: InputDecoration(labelText: TTexts.username, prefixIcon: Icon(Iconsax.direct_right)),
              textInputAction: TextInputAction.next, // <<< MODIFIED
              onFieldSubmitted: (_) {
                // When "Enter" or "Next" is pressed on the email field,
                // move focus to the password field.
                FocusScope.of(context).requestFocus(controller.passwordFocusNode);
              },
            ),
            SizedBox(height: TSizes.spaceBtwItems),
            Obx(
              () => TextFormField(
                controller: controller.password,
                focusNode: controller.passwordFocusNode, // <<< ADDED
                validator: (value) => TValidator.validateEmptyText('Password', value),
                obscureText: controller.hidePassword.value,
                decoration: InputDecoration(
                  labelText: TTexts.password,
                  prefixIcon: Icon(Iconsax.password_check),
                  suffixIcon: IconButton(
                    onPressed: () => controller.hidePassword.toggle(),
                    icon: Icon(controller.hidePassword.value ? Iconsax.eye_slash : Iconsax.eye),
                  ),
                ),
                textInputAction: TextInputAction.done, // <<< MODIFIED
                onFieldSubmitted: (String value) {
                  // When "Enter" or "Done" is pressed on the password field,
                  // attempt to log in.
                  controller.loginWithEmailAndPassword();
                },
              ),
            ),
            SizedBox(height: TSizes.spaceBtwSections / 2),

            /// Remember Me & Forget Password
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Obx(
                      () => Checkbox(
                        value: controller.rememberMe.value,
                        onChanged: (value) => controller.rememberMe.value = value!,
                      ),
                    ),
                    Text(TTexts.rememberMe),
                  ],
                ),
                TextButton(onPressed: () => Get.toNamed(TRoutes.forgotPassword), child: Text(TTexts.forgetPassword)),
              ],
            ),
            SizedBox(height: TSizes.spaceBtwSections),

            /// Sign In Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () => controller.loginWithEmailAndPassword(),
                child: Text(TTexts.signIn),
              ),

              // child: ElevatedButton(onPressed: () => controller.registerAdmin(), child: Text(TTexts.signIn)),
            ),
            SizedBox(height: TSizes.spaceBtwItems),
            TextButton(
              onPressed: () => Get.toNamed(TRoutes.signup),
              child: const Text("Don't have an account? Create one"),
            ),
          ],
        ),
      ),
    );
  }
}
