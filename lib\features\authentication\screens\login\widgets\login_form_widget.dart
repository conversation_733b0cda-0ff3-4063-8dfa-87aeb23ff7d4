import 'package:alloy/common/widgets/forms/enhanced_text_field.dart';
import 'package:alloy/common/widgets/forms/form_section.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/features/authentication/controllers/login_controller.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class TLoginFormWidget extends StatelessWidget {
  const TLoginFormWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = LoginController.instance;
    return TRoundedContainer(
      backgroundColor: Theme.of(context).cardColor,
      child: Form(
        key: controller.loginFormKey,
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            children: [
              // Login Credentials Section
              TFormSection(
                title: 'Sign In to Your Account',
                icon: Iconsax.login,
                isRequired: true,
                showDivider: false,
                children: [
                  TEnhancedTextField(
                    controller: controller.email,
                    labelText: 'Username',
                    hintText: 'Enter your username',
                    prefixIcon: Iconsax.user,
                    isRequired: true,
                    validator: (value) => TValidator.validateEmptyText('Username', value),
                    textInputAction: TextInputAction.next,
                    focusNode: null,
                    nextFocusNode: controller.passwordFocusNode,
                    onFieldSubmitted: (_) {
                      FocusScope.of(context).requestFocus(controller.passwordFocusNode);
                    },
                  ),
                  const SizedBox(height: TSizes.spaceBtwItems),
                  Obx(
                    () => TEnhancedTextField(
                      controller: controller.password,
                      labelText: 'Password',
                      hintText: 'Enter your password',
                      prefixIcon: Iconsax.password_check,
                      isRequired: true,
                      obscureText: controller.hidePassword.value,
                      focusNode: controller.passwordFocusNode,
                      validator: (value) => TValidator.validateEmptyText('Password', value),
                      textInputAction: TextInputAction.done,
                      suffixIcon: IconButton(
                        onPressed: () => controller.hidePassword.toggle(),
                        icon: Icon(controller.hidePassword.value ? Iconsax.eye_slash : Iconsax.eye),
                      ),
                      onFieldSubmitted: (_) => controller.loginWithEmailAndPassword(),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections / 2),

              // Remember Me & Forget Password Section
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Obx(
                        () => Checkbox(
                          value: controller.rememberMe.value,
                          onChanged: (value) => controller.rememberMe.value = value!,
                          activeColor: Theme.of(context).primaryColor,
                        ),
                      ),
                      Text(TTexts.rememberMe, style: Theme.of(context).textTheme.bodyMedium),
                    ],
                  ),
                  TextButton(
                    onPressed: () => Get.toNamed(TRoutes.forgotPassword),
                    child: Text(
                      TTexts.forgetPassword,
                      style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.w500),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Sign In Button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () => controller.loginWithEmailAndPassword(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    elevation: 2,
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(TSizes.buttonRadius)),
                  ),
                  child: Text(TTexts.signIn, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600)),
                ),
              ),
              const SizedBox(height: TSizes.spaceBtwItems),

              // Create Account Link
              Center(
                child: TextButton(
                  onPressed: () => Get.toNamed(TRoutes.signup),
                  child: RichText(
                    text: TextSpan(
                      style: Theme.of(context).textTheme.bodyMedium,
                      children: [
                        const TextSpan(text: "Don't have an account? "),
                        TextSpan(
                          text: "Create one",
                          style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.w600),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
