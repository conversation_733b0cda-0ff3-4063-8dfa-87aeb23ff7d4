import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart'; // For PlatformException
import 'package:get/get.dart';
import '../../../utils/exceptions/firebase_exceptions.dart'; // TFirebaseException
import '../../../utils/exceptions/platform_exceptions.dart'; // TPlatformException
import '../models/settings_model.dart'; // SettingsModel

/// Repository for managing global application settings in Firestore.
class SettingsRepository extends GetxController {
  static SettingsRepository get instance => Get.find();

  final FirebaseFirestore _db = FirebaseFirestore.instance;

  // Collection reference for settings, using a fixed document ID 'global_settings'
  // We use withConverter for automatic mapping to/from SettingsModel.
  final CollectionReference<SettingsModel> _settingsCollection = FirebaseFirestore.instance
      .collection('settings') // Assuming your Firestore collection name is 'settings' (lowercase)
      .withConverter<SettingsModel>(
        fromFirestore: (snapshot, options) => SettingsModel.fromSnapshot(snapshot),
        toFirestore: (settings, options) => settings.toJson(),
      );

  /// Fetches the global settings document from Firestore.
  /// Since there's only one settings document, its ID is fixed as 'global_settings'.
  Future<SettingsModel> fetchSettings() async {
    try {
      final documentSnapshot = await _settingsCollection.doc('global_settings').get();
      if (documentSnapshot.exists) {
        return documentSnapshot.data()!; // Data is already converted to SettingsModel
      } else {
        // If settings document doesn't exist, return an empty/default model
        // and potentially create it in Firestore on the first save.
        return SettingsModel.empty();
      }
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while fetching settings: $e';
    }
  }

  /// Updates the global settings document in Firestore.
  /// If the document does not exist, it will be created.
  Future<void> updateSettings(SettingsModel settings) async {
    try {
      // Use set with merge: true to update existing fields and add new ones without overwriting the entire document.
      await _settingsCollection.doc('global_settings').set(settings, SetOptions(merge: true));
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while updating settings: $e';
    }
  }
}
