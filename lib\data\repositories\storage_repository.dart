import 'dart:io'; // For File
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../utils/exceptions/firebase_exceptions.dart'; // TFirebaseException
import '../../utils/exceptions/platform_exceptions.dart'; // TPlatformException

/// Repository for handling Firebase Storage operations.
class StorageRepository extends GetxController {
  static StorageRepository get instance => Get.find();

  final _firebaseStorage = FirebaseStorage.instance;

  /// Uploads a file to Firebase Storage.
  /// [path]: The full path in Firebase Storage (e.g., 'Products/Thumbnails/image.jpg').
  /// [data]: Can be a `File` (for mobile/desktop) or `Uint8List` (for web).
  /// Returns the download URL of the uploaded file.
  Future<String> uploadFile(String path, dynamic data) async {
    // Corrected: data is dynamic
    try {
      final ref = _firebaseStorage.ref().child(path);
      UploadTask uploadTask;

      if (data is File) {
        // For mobile/desktop
        uploadTask = ref.putFile(data);
      } else if (data is Uint8List) {
        // For web
        uploadTask = ref.putData(data);
      } else {
        throw 'Unsupported data type for upload. Must be File or Uint8List.';
      }

      final snapshot = await uploadTask.whenComplete(() => null);
      final downloadUrl = await snapshot.ref.getDownloadURL();
      return downloadUrl;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again: $e';
    }
  }

  /// Deletes a file from Firebase Storage given its URL.
  Future<void> deleteFileByUrl(String fileUrl) async {
    try {
      final ref = _firebaseStorage.refFromURL(fileUrl);
      await ref.delete();
    } on FirebaseException catch (e) {
      // If file doesn't exist, it's not an error we need to throw for deletion.
      if (e.code == 'object-not-found') {
        print('File not found at URL: $fileUrl. Skipping deletion.');
        return;
      }
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again: $e';
    }
  }

  /// Deletes a file from Firebase Storage given its path.
  Future<void> deleteFileByPath(String path) async {
    try {
      final ref = _firebaseStorage.ref().child(path);
      await ref.delete();
    } on FirebaseException catch (e) {
      if (e.code == 'object-not-found') {
        print('File not found at path: $path. Skipping deletion.');
        return;
      }
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again: $e';
    }
  }
}
