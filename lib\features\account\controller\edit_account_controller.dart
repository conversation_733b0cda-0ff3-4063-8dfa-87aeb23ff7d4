import 'package:alloy/features/account/controller/account_controller.dart';
import 'package:alloy/features/account/models/account_model.dart';
import 'package:alloy/features/account/repository/account_repository.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../address/controller/address_controller.dart';
import '../../address/models/address_model.dart';
import '../../address/repository/address_repository.dart';

class EditAccountController extends GetxController {
  static EditAccountController get instance => Get.find();

  /// Variables
  final formKey = GlobalKey<FormState>();
  final TextEditingController name;
  final TextEditingController alsoKnownAs;
  final TextEditingController phone;
  final TextEditingController paymentTerms;
  final TextEditingController email; // NEW: Email field

  final Rx<BusinessType> businessType;
  final Rx<PriorityLevel> priority;
  final Rx<AccountStatus> status;
  final RxBool isParent; // NEW: To allow making this account a parent
  final Rx<AccountModel?> selectedParentAccount; // NEW: To hold the selected parent account

  final RxList<String> selectedIndustries = <String>[].obs;
  final RxList<UserModel> selectedHandlers = <UserModel>[].obs;
  final RxList<ContactModel> selectedContacts = <ContactModel>[].obs;

  // NEW: Reactive list to hold addresses associated with this account
  final RxList<AddressModel> accountAddresses = <AddressModel>[].obs;
  // Changed: _originalAccountAddresses is now a regular List, not RxList
  List<AddressModel> _originalAccountAddresses = []; // To track changes

  // NEW: Reactive variables for denormalized location fields
  final RxString denormalizedCountry = ''.obs;
  final RxString denormalizedEmirates = ''.obs;

  // Repositories
  final AccountRepository _accountRepository = Get.find<AccountRepository>();
  final AddressRepository _addressRepository = Get.find<AddressRepository>();
  final AddressController _addressController = Get.find<AddressController>(); // For address streaming
  final AccountController _accountController = Get.find<AccountController>(); // For parent accounts

  // Reactive list for parent account dropdown options
  final RxList<AccountModel> parentAccountOptions = <AccountModel>[].obs;

  // Store the original account for comparison during update
  final AccountModel _originalAccount;

  // Constructor to initialize all controllers and Rx variables
  EditAccountController({required AccountModel initialAccount})
    : _originalAccount = initialAccount,
      name = TextEditingController(text: initialAccount.name),
      alsoKnownAs = TextEditingController(text: initialAccount.alsoKnownAs ?? ''),
      phone = TextEditingController(text: initialAccount.phone ?? ''),
      paymentTerms = TextEditingController(text: initialAccount.paymentTerms ?? ''),
      email = TextEditingController(text: initialAccount.email ?? ''),
      businessType = initialAccount.businessType.obs,
      priority = initialAccount.priority.obs,
      status = initialAccount.status.obs,
      isParent = initialAccount.isParent.obs,
      selectedParentAccount = Rx<AccountModel?>(null) {
    // Initialize with null, will be set in onInit
    selectedIndustries.assignAll(initialAccount.industry ?? []);
    selectedHandlers.assignAll(initialAccount.handlerDetails ?? []);
    selectedContacts.assignAll(initialAccount.contactDetails ?? []);

    // Initialize denormalized fields with existing account data
    denormalizedCountry.value = initialAccount.country ?? '';
    denormalizedEmirates.value = initialAccount.emirates ?? '';
  }

  @override
  void onInit() {
    super.onInit();
    // Listen to parent accounts stream to populate the dropdown
    _accountController.streamParentAccounts().listen((accounts) async {
      parentAccountOptions.assignAll(accounts);
      // After parent options are loaded, try to set the selected parent account
      if (_originalAccount.parentId != null && _originalAccount.parentId!.isNotEmpty) {
        final parent = accounts.firstWhereOrNull((p) => p.id == _originalAccount.parentId);
        selectedParentAccount.value = parent;
        if (parent == null) {
          TLoaders.warningSnackBar(
            title: 'Parent Not Found',
            message: 'Could not load details for parent account ID: ${_originalAccount.parentId}',
          );
        }
      }
    });

    // Fetch and initialize account addresses
    _loadAddresses();

    // FIXED: Use debounce to avoid concurrent modification issues
    // This ensures _updateDenormalizedLocation is called only after the list has stabilized
    debounce(accountAddresses, (_) => _updateDenormalizedLocation(), time: const Duration(milliseconds: 100));
  }

  Future<void> _loadAddresses() async {
    // Defer showing the loader until after the build phase
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TFullScreenLoader.popUpCircular(); // Show loading for address fetch
    });

    try {
      _addressController.loadAddressesForAccount(_originalAccount.id);
      // Listen to the stream and assign to accountAddresses
      _addressController.filteredItems.listen(
        (addresses) {
          accountAddresses.assignAll(addresses);
          // Now _originalAccountAddresses is a regular List, assign copy directly
          _originalAccountAddresses = addresses.map((a) => a.copyWith()).toList(); // Deep copy for comparison
          // _updateDenormalizedLocation() is now called via the `ever` listener,
          // but an initial call here ensures it's set up immediately after load.
          _updateDenormalizedLocation();
          TFullScreenLoader.stopLoading(); // Stop loading once data is received and assigned
        },
        onError: (error) {
          TFullScreenLoader.stopLoading(); // Stop loading on error
          TLoaders.errorSnackBar(title: 'Error Loading Addresses', message: error.toString());
        },
      );
    } catch (e) {
      // This catch block handles errors from _addressController.loadAddressesForAccount itself,
      // not errors from the stream's listen callback.
      TFullScreenLoader.stopLoading(); // Ensure loader stops if an error occurs before stream even starts listening
      TLoaders.errorSnackBar(title: 'Error Loading Addresses', message: e.toString());
    }
  }

  @override
  void onClose() {
    name.dispose();
    alsoKnownAs.dispose();
    phone.dispose();
    paymentTerms.dispose();
    email.dispose();
    super.onClose();
  }

  /// Toggle Handler Selection
  void toggleHandlerSelection(UserModel handler) {
    if (selectedHandlers.contains(handler)) {
      selectedHandlers.remove(handler);
    } else {
      selectedHandlers.add(handler);
    }
  }

  /// Toggle Contact Selection
  void toggleContactSelection(ContactModel contact) {
    if (selectedContacts.contains(contact)) {
      selectedContacts.remove(contact);
    } else {
      selectedContacts.add(contact);
    }
  }

  /// Add a new address to the temporary list for the account
  void addAddress(AddressModel address) {
    // Determine if this should be the default address
    bool shouldBeDefault = accountAddresses.isEmpty; // If it's the first address, make it default

    // If it's a billing address and set as default (or should be default),
    // ensure only one billing address is default.
    if (address.type == 'Billing' && (address.isDefault || shouldBeDefault)) {
      for (var i = 0; i < accountAddresses.length; i++) {
        if (accountAddresses[i].type == 'Billing' && accountAddresses[i].isDefault) {
          accountAddresses[i] = accountAddresses[i].copyWith(isDefault: false);
        }
      }
      address = address.copyWith(isDefault: true); // Ensure the new one is default
    } else if (shouldBeDefault) {
      address = address.copyWith(isDefault: true); // If it's the first address overall, make it default
    }

    // Assign a temporary ID if not already present, for list management before saving
    final addressWithTempId = address.id.isEmpty
        ? address.copyWith(id: 'temp_${DateTime.now().millisecondsSinceEpoch}')
        : address;

    // Add the address to a temporary list, then assign all to trigger reactivity properly
    final List<AddressModel> tempAddresses = List.from(accountAddresses);
    tempAddresses.add(addressWithTempId);
    accountAddresses.assignAll(tempAddresses); // Use assignAll to trigger a single rebuild
    // Removed direct call: _updateDenormalizedLocation(); // Now handled by `ever` listener
  }

  /// Update an existing address in the temporary list
  void updateAddress(AddressModel updatedAddress) {
    final List<AddressModel> tempAddresses = List.from(accountAddresses);
    final index = tempAddresses.indexWhere((addr) => addr.id == updatedAddress.id);
    if (index != -1) {
      // If it's a billing address and set as default, ensure only one is default
      if (updatedAddress.type == 'Billing' && updatedAddress.isDefault) {
        for (var i = 0; i < tempAddresses.length; i++) {
          if (tempAddresses[i].type == 'Billing' &&
              tempAddresses[i].id != updatedAddress.id &&
              tempAddresses[i].isDefault) {
            tempAddresses[i] = tempAddresses[i].copyWith(isDefault: false);
          }
        }
      }
      tempAddresses[index] = updatedAddress;
      accountAddresses.assignAll(tempAddresses); // Use assignAll to trigger a single rebuild
      // Removed direct call: _updateDenormalizedLocation(); // Now handled by `ever` listener
    }
  }

  /// Remove an address from the temporary list
  void removeAddress(AddressModel address) {
    final List<AddressModel> tempAddresses = List.from(accountAddresses);
    tempAddresses.removeWhere((addr) => addr.id == address.id);

    // If the removed address was the default billing, find a new default or clear
    if (address.isDefault && address.type == 'Billing') {
      final newDefaultBilling = tempAddresses.firstWhereOrNull((addr) => addr.type == 'Billing');
      if (newDefaultBilling != null) {
        final index = tempAddresses.indexOf(newDefaultBilling);
        tempAddresses[index] = newDefaultBilling.copyWith(isDefault: true);
      }
    }
    accountAddresses.assignAll(tempAddresses); // Use assignAll to trigger a single rebuild
    // Removed direct call: _updateDenormalizedLocation(); // Now handled by `ever` listener
  }

  /// Internal method to update denormalized country and emirates based on default billing address.
  /// Uses a safe copy of the list to avoid concurrent modification issues.
  void _updateDenormalizedLocation() {
    try {
      // Create a safe copy of the list to avoid concurrent modification
      final List<AddressModel> addressesCopy = List.from(accountAddresses);
      final defaultBillingAddress = addressesCopy.firstWhereOrNull((addr) => addr.isDefault && addr.type == 'Billing');
      if (defaultBillingAddress != null) {
        // country is non-nullable, state is nullable
        denormalizedCountry.value = defaultBillingAddress.country;
        denormalizedEmirates.value = defaultBillingAddress.state ?? '';
      } else {
        denormalizedCountry.value = '';
        denormalizedEmirates.value = '';
      }
    } catch (e) {
      // Log error but don't crash the app - using debugPrint for development
      debugPrint('Error updating denormalized location: $e');
    }
  }

  /// Update Account
  Future<void> updateAccount(String accountId, String currentUserId) async {
    try {
      TFullScreenLoader.popUpCircular();

      if (!await NetworkManager.instance.isConnected()) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'No Internet Connection', message: 'Please check your internet connection.');
        return;
      }

      if (!formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'Invalid Form', message: 'Please correct the errors in the form.');
        return;
      }

      // 1. Create the updated AccountModel with current form values and denormalized location
      final updatedAccount = AccountModel(
        id: accountId,
        name: name.text.trim(),
        alsoKnownAs: alsoKnownAs.text.trim(),
        phone: phone.text.trim(),
        paymentTerms: paymentTerms.text.trim(),
        email: email.text.trim(), // NEW: Email
        businessType: businessType.value,
        priority: priority.value,
        status: status.value,
        isParent: isParent.value, // NEW: Set isParent flag
        parentId: selectedParentAccount.value?.id, // NEW: Set parentId
        industry: selectedIndustries.toList(),
        createdAt: _originalAccount.createdAt, // Preserve original creation date
        updatedAt: DateTime.now(),
        createdByUserId: _originalAccount.createdByUserId, // Preserve original creator
        updatedByUserId: currentUserId, // Set current user as updater
        addressIds: [], // Will be populated after addresses are processed
        country: denormalizedCountry.value, // Denormalized from default billing address
        emirates: denormalizedEmirates.value, // Denormalized from default billing address
      );
      updatedAccount.handlerDetails = selectedHandlers.toList();
      updatedAccount.contactDetails = selectedContacts.toList();

      // 2. Process Addresses (Add, Update, Delete)
      final List<String> finalAddressIds = [];
      String? defaultBillingAddressId;

      // Create safe copies to avoid concurrent modification
      final List<AddressModel> currentAddresses = List.from(accountAddresses);
      final List<AddressModel> originalAddresses = List.from(_originalAccountAddresses);

      // Addresses to Add/Update
      for (var newOrUpdatedAddr in currentAddresses) {
        final existingOriginalAddr = originalAddresses.firstWhereOrNull((a) => a.id == newOrUpdatedAddr.id);

        if (existingOriginalAddr == null || newOrUpdatedAddr.id.startsWith('temp_')) {
          // New address (has temp ID or not in original list)
          final addressToCreate = newOrUpdatedAddr.copyWith(
            accountId: accountId,
            createdByUserId: currentUserId,
            updatedByUserId: currentUserId,
            id: '', // Ensure ID is empty for Firestore to generate a new one
          );
          final newAddressId = await _addressRepository.createAddress(addressToCreate);
          finalAddressIds.add(newAddressId);
          if (addressToCreate.isDefault && addressToCreate.type == 'Billing') {
            defaultBillingAddressId = newAddressId;
          }
        } else if (existingOriginalAddr != newOrUpdatedAddr) {
          // Existing address that has been modified
          final addressToUpdate = newOrUpdatedAddr.copyWith(
            accountId: accountId, // Ensure accountId is set for existing addresses too
            updatedByUserId: currentUserId,
          );
          await _addressRepository.updateAddress(addressToUpdate);
          finalAddressIds.add(addressToUpdate.id);
          if (addressToUpdate.isDefault && addressToUpdate.type == 'Billing') {
            defaultBillingAddressId = addressToUpdate.id;
          }
        } else {
          // Existing address, no changes
          finalAddressIds.add(newOrUpdatedAddr.id);
          if (newOrUpdatedAddr.isDefault && newOrUpdatedAddr.type == 'Billing') {
            defaultBillingAddressId = newOrUpdatedAddr.id;
          }
        }
      }

      // Addresses to Delete (present in original but not in current list)
      for (var originalAddr in originalAddresses) {
        if (!currentAddresses.any((currentAddr) => currentAddr.id == originalAddr.id)) {
          await _addressRepository.deleteAddress(originalAddr.id);
        }
      }

      // 3. Update the AccountModel with the final list of address IDs
      final accountToSave = updatedAccount.copyWith(addressIds: finalAddressIds);
      await _accountRepository.updateAccount(accountToSave, _originalAccount); // Pass _originalAccount for comparison

      // 4. Set the default billing address to trigger denormalization on AccountModel
      if (defaultBillingAddressId != null) {
        await _addressRepository.setDefaultAddress(accountId, defaultBillingAddressId, 'Billing');
      } else {
        // If no default billing address is set, clear the country/emirates on the account
        await _accountRepository.updateAccountFields(accountId, {'country': null, 'emirates': null});
      }

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Account Updated',
        message: 'Account ${updatedAccount.name} updated successfully!',
      );
      Get.offAllNamed(TRoutes.accounts); // Navigate back to accounts list
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }
}
