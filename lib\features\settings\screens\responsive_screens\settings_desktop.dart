import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../../../../common/widgets/images/t_rounded_image.dart';
import '../../../../common/widgets/forms/enhanced_text_field.dart';
import '../../../../utils/constants/enums.dart';
import '../../../../utils/constants/image_strings.dart';
import '../../../../utils/constants/sizes.dart';
import '../../../../utils/constants/colors.dart';
import '../../../../utils/validators/validation.dart';
import '../../controllers/settings_controller.dart';

class SettingsDesktop extends StatelessWidget {
  const SettingsDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SettingsController());

    return Scaffold(
      backgroundColor: TColors.lightGrey,
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Modern Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Settings',
                        style: Theme.of(
                          context,
                        ).textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.w600, color: TColors.dark),
                      ),
                      Text(
                        'Configure your application settings',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: TColors.darkGrey),
                      ),
                    ],
                  ),
                  ElevatedButton.icon(
                    onPressed: () => controller.saveSettings(),
                    icon: const Icon(Iconsax.tick_circle, size: 18),
                    label: const Text('Save Settings'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: TColors.primary,
                      foregroundColor: TColors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Main Content Area
              Obx(() {
                if (controller.isLoading.value) {
                  return const Center(child: CircularProgressIndicator(color: TColors.primary));
                }

                return Form(
                  key: controller.formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Company Information Section
                      _buildSection(
                        context,
                        icon: Iconsax.building,
                        title: 'Company Information',
                        subtitle: 'Basic company details and contact information',
                        child: Column(
                          children: [
                            // Company Name & Address
                            TEnhancedTextField(
                              controller: controller.companyName,
                              labelText: 'Company Name',
                              prefixIcon: Iconsax.building,
                              validator: (value) => TValidator.validateEmptyText('Company Name', value),
                            ),
                            const SizedBox(height: 16),
                            TEnhancedTextField(
                              controller: controller.companyAddress,
                              labelText: 'Company Address',
                              prefixIcon: Iconsax.location,
                              maxLines: 2,
                              validator: (value) => TValidator.validateEmptyText('Company Address', value),
                            ),
                            const SizedBox(height: 16),

                            // Phone & Email Row
                            Row(
                              children: [
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.companyPhone,
                                    labelText: 'Company Phone',
                                    prefixIcon: Iconsax.call,
                                    keyboardType: TextInputType.phone,
                                    validator: (value) => TValidator.validateEmptyText('Company Phone', value),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.companyEmail,
                                    labelText: 'Company Email',
                                    prefixIcon: Iconsax.sms,
                                    keyboardType: TextInputType.emailAddress,
                                    validator: (value) => TValidator.validateEmail(value),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Currency & Tax Rate Row
                            Row(
                              children: [
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.defaultCurrency,
                                    labelText: 'Default Currency',
                                    prefixIcon: Iconsax.dollar_square,
                                    validator: (value) => TValidator.validateEmptyText('Default Currency', value),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.globalTaxRatePercentage,
                                    labelText: 'Global Tax Rate (%)',
                                    prefixIcon: Iconsax.percentage_square,
                                    keyboardType: TextInputType.number,
                                    validator: (value) => TValidator.validateEmptyText('Tax Rate', value),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // Company Logos Section
                            Row(
                              children: [
                                Icon(Iconsax.image, color: TColors.primary, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'Company Logos',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: TColors.dark),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                            Row(
                              children: [
                                Expanded(
                                  child: _buildLogoUploadSection(
                                    context,
                                    'Company Logo',
                                    controller.companyLogoUrl.value,
                                    controller.uploadCompanyLogo,
                                    TImages.bonnAppLogo,
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: _buildLogoUploadSection(
                                    context,
                                    'App Logo',
                                    controller.appLogoUrl.value,
                                    controller.uploadAppLogo,
                                    TImages.bonnAppLogo,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Document Numbering Section
                      _buildSection(
                        context,
                        icon: Iconsax.document_text,
                        title: 'Document Numbering',
                        subtitle: 'Configure quotation and invoice numbering',
                        child: Column(
                          children: [
                            // Quotation Settings Row
                            Row(
                              children: [
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.nextQuotationNumberPrefix,
                                    labelText: 'Quotation Prefix',
                                    prefixIcon: Iconsax.tag,
                                    validator: (value) => TValidator.validateEmptyText('Quotation Prefix', value),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.nextQuotationNumberStart,
                                    labelText: 'Quotation Start #',
                                    prefixIcon: Iconsax.hashtag,
                                    keyboardType: TextInputType.number,
                                    validator: (value) => TValidator.validateEmptyText('Quotation Start Number', value),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Invoice Settings Row
                            Row(
                              children: [
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.nextInvoiceNumberPrefix,
                                    labelText: 'Invoice Prefix',
                                    prefixIcon: Iconsax.tag_2,
                                    validator: (value) => TValidator.validateEmptyText('Invoice Prefix', value),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.nextInvoiceNumberStart,
                                    labelText: 'Invoice Start #',
                                    prefixIcon: Iconsax.hashtag,
                                    keyboardType: TextInputType.number,
                                    validator: (value) => TValidator.validateEmptyText('Invoice Start Number', value),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Validity & Lead Time Row
                            Row(
                              children: [
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.defaultQuotationValidityDays,
                                    labelText: 'Quotation Validity (Days)',
                                    prefixIcon: Iconsax.calendar_1,
                                    keyboardType: TextInputType.number,
                                    validator: (value) =>
                                        TValidator.validateEmptyText('Quotation Validity Days', value),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: TEnhancedTextField(
                                    controller: controller.defaultLeadTimeDays,
                                    labelText: 'Default Lead Time (Days)',
                                    prefixIcon: Iconsax.clock,
                                    keyboardType: TextInputType.number,
                                    validator: (value) => TValidator.validateEmptyText('Default Lead Time Days', value),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),

                      // Material Rates Section
                      _buildSection(
                        context,
                        icon: Iconsax.box,
                        title: 'Material Rates',
                        subtitle: 'Configure material pricing per unit',
                        child: Column(
                          children: [
                            // Material Rates Grid
                            GridView.builder(
                              shrinkWrap: true,
                              physics: const NeverScrollableScrollPhysics(),
                              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                                crossAxisCount: 3,
                                crossAxisSpacing: 16,
                                mainAxisSpacing: 16,
                                childAspectRatio: 3,
                              ),
                              itemCount: controller.materialTypes.length,
                              itemBuilder: (context, index) {
                                final materialType = controller.materialTypes[index];
                                return TEnhancedTextField(
                                  controller: controller.materialRateControllers[materialType]!,
                                  labelText: '$materialType Rate',
                                  prefixIcon: Iconsax.box,
                                  keyboardType: TextInputType.number,
                                  validator: (value) => TValidator.validateEmptyText('$materialType Rate', value),
                                );
                              },
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),

                      // User Access & Inventory Settings Section
                      _buildSection(
                        context,
                        icon: Iconsax.security_user,
                        title: 'User Access & Inventory',
                        subtitle: 'Configure user permissions and inventory settings',
                        child: Column(
                          children: [
                            // User Access Settings
                            Row(
                              children: [
                                Icon(Iconsax.user_octagon, color: TColors.primary, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'User Access Settings',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: TColors.dark),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Access Control Switches
                            _buildSwitchTile(
                              context,
                              'Allow New User Registration',
                              'Enable new users to register accounts',
                              controller.allowNewUserRegistration,
                              Iconsax.user_add,
                            ),
                            const SizedBox(height: 12),
                            _buildSwitchTile(
                              context,
                              'Require Email Verification',
                              'Require users to verify their email addresses',
                              controller.requireEmailVerification,
                              Iconsax.verify,
                            ),
                            const SizedBox(height: 16),

                            // Default User Role
                            TEnhancedTextField(
                              controller: controller.defaultNewUserRole,
                              labelText: 'Default New User Role',
                              prefixIcon: Iconsax.user_tag,
                              validator: (value) => TValidator.validateEmptyText('Default User Role', value),
                            ),
                            const SizedBox(height: 24),

                            // Inventory Settings
                            Row(
                              children: [
                                Icon(Iconsax.box_1, color: TColors.primary, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  'Inventory Settings',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600, color: TColors.dark),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Low Stock Threshold
                            TEnhancedTextField(
                              controller: controller.lowStockThreshold,
                              labelText: 'Low Stock Threshold',
                              prefixIcon: Iconsax.warning_2,
                              keyboardType: TextInputType.number,
                              validator: (value) => TValidator.validateEmptyText('Low Stock Threshold', value),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: TColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: TColors.darkGrey.withValues(alpha: 0.1), blurRadius: 10, offset: const Offset(0, 4)),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: TColors.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: TColors.primary, size: 20),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(
                      context,
                    ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600, color: TColors.dark),
                  ),
                  Text(subtitle, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: TColors.darkGrey)),
                ],
              ),
            ],
          ),
          const SizedBox(height: 24),
          child,
        ],
      ),
    );
  }

  Widget _buildSwitchTile(BuildContext context, String title, String subtitle, RxBool value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: TColors.lightGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: TColors.borderPrimary.withValues(alpha: 0.1)),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: TColors.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: TColors.primary, size: 18),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(
                    context,
                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600, color: TColors.dark),
                ),
                Text(subtitle, style: Theme.of(context).textTheme.bodySmall?.copyWith(color: TColors.darkGrey)),
              ],
            ),
          ),
          Obx(
            () => Switch(
              value: value.value,
              onChanged: (newValue) => value.value = newValue,
              activeColor: TColors.primary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLogoUploadSection(
    BuildContext context,
    String title,
    String currentUrl,
    VoidCallback onUpload,
    String defaultImage,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: TColors.lightGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: TColors.borderPrimary.withValues(alpha: 0.1)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600, color: TColors.dark),
          ),
          const SizedBox(height: 12),
          TRoundedImage(
            image: currentUrl.isNotEmpty ? currentUrl : defaultImage,
            width: 80,
            height: 80,
            imageType: currentUrl.isNotEmpty ? ImageType.network : ImageType.asset,
            backgroundColor: TColors.light,
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: onUpload,
              icon: const Icon(Iconsax.camera, size: 16),
              label: const Text('Upload'),
              style: OutlinedButton.styleFrom(
                foregroundColor: TColors.primary,
                side: BorderSide(color: TColors.primary),
                padding: const EdgeInsets.symmetric(vertical: 8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
