import 'package:flutter/material.dart';
import 'package:get/get.dart'; // For theme context

class TNameChip extends StatelessWidget {
  final String text;
  final Color backgroundColor;
  final Color textColor;
  final double radius;
  final EdgeInsetsGeometry padding;
  final String? tooltipText; // New: Optional tooltip text

  const TNameChip({
    super.key,
    required this.text,
    required this.backgroundColor,
    this.textColor = Colors.white,
    this.radius = 8.0,
    this.padding = const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
    this.tooltipText, // Initialize the new parameter
  });

  @override
  Widget build(BuildContext context) {
    String displayText = text.trim().isNotEmpty ? text.split(' ')[0].capitalizeFirst! : '?';

    // The core chip design
    final Widget chipContent = Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(radius),
        boxShadow: [
          // A subtle shadow for a modern "lifted" effect
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2), // changes position of shadow
          ),
        ],
        // Optional: gradient for a more "elegant" feel
        // gradient: LinearGradient(
        //   colors: [backgroundColor.withOpacity(0.8), backgroundColor],
        //   begin: Alignment.topLeft,
        //   end: Alignment.bottomRight,
        // ),
      ),
      child: Text(
        displayText,
        style:
            Theme.of(context).textTheme.labelSmall?.copyWith(color: textColor, fontWeight: FontWeight.bold) ??
            TextStyle(color: textColor, fontSize: 10, fontWeight: FontWeight.bold),
      ),
    );

    // Conditionally wrap with Tooltip
    if (tooltipText != null && tooltipText!.isNotEmpty) {
      return Tooltip(message: tooltipText!, child: chipContent);
    } else {
      // If no tooltip text, just return the chip content directly
      return chipContent;
    }
  }
}
