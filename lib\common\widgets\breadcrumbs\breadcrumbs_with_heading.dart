import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/helpers/helper_functions.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class TBreadcrumbItem {
  const TBreadcrumbItem({required this.text, this.route});
  final String text;
  final String? route;
}

class TBreadcrumbsWithHeading extends StatelessWidget {
  const TBreadcrumbsWithHeading({
    super.key,
    required this.heading,
    this.breadcrumbItems = const [],
    this.trailing,
    this.showBackButton = false,
  });

  final String heading;
  final List<TBreadcrumbItem> breadcrumbItems;
  final Widget? trailing;
  final bool showBackButton;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Breadcrumbs
        if (breadcrumbItems.isNotEmpty)
          Row(
            children: [
              InkWell(
                onTap: () => Get.offAllNamed(TRoutes.dashboard),
                child: Text('Dashboard', style: textTheme.labelMedium?.apply(color: Colors.red)),
              ),
              ...breadcrumbItems.map((item) {
                final isLast = item == breadcrumbItems.last;
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Text(' / ', style: TextStyle(color: TColors.darkGrey)),
                    InkWell(
                      onTap: isLast || item.route == null ? null : () => Get.toNamed(item.route!),
                      child: Text(
                        THelperFunctions.capitalizeFirstLetter(item.text),
                        style: isLast ? textTheme.labelMedium : textTheme.labelMedium?.apply(color: Colors.red),
                      ),
                    ),
                  ],
                );
              }),
            ],
          ),
        if (breadcrumbItems.isNotEmpty) const SizedBox(height: TSizes.spaceBtwItems / 2),

        // Heading & Trailing Widget
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            // Heading
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (showBackButton) IconButton(onPressed: () => Get.back(), icon: const Icon(Iconsax.arrow_left)),
                if (showBackButton) const SizedBox(width: TSizes.spaceBtwItems),
                Text(heading, style: Theme.of(context).textTheme.headlineMedium),
              ],
            ),
            if (trailing != null) trailing!,
          ],
        ),
      ],
    );
  }
}
