// Helper to build a single menu item for CustomPopupMenu
import 'package:custom_pop_up_menu/custom_pop_up_menu.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../utils/constants/colors.dart';
import '../../../utils/constants/sizes.dart';
import '../../reporting/controllers/report_controller.dart';
import '../../reporting/models/report_type.dart';

CustomPopupMenu customPopupWidget(ReportController reportController) {
  return CustomPopupMenu(
    // You can define a controller if you need programmatic control
    // controller: _customPopupMenuController,
    menuBuilder: () => ClipRRect(
      borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
      child: Container(
        color: TColors.primary, // Background color for the entire popup
        padding: const EdgeInsets.all(TSizes.sm),
        child: IntrinsicWidth(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildPopupMenuItem(reportController, 'Export Accounts Excel', 'excel_accounts'),
              _buildPopupMenuItem(reportController, 'Export Accounts & Contacts Excel', 'excel_accounts_contacts'),
              const Divider(color: TColors.white, height: 10, thickness: 0.5), // Divider
              _buildPopupMenuItem(reportController, 'Print Accounts PDF', 'pdf_accounts'),
              _buildPopupMenuItem(reportController, 'Print Accounts & Contacts PDF', 'pdf_accounts_contacts'),
              const Divider(color: TColors.white, height: 10, thickness: 0.5), // Divider
              _buildPopupMenuItem(reportController, 'Export Accounts CSV', 'csv_accounts'),
              _buildPopupMenuItem(reportController, 'Export Accounts & Contacts CSV', 'csv_accounts_contacts'),
            ],
          ),
        ),
      ),
    ),

    showArrow: true, // Show a pointing arrow
    barrierColor: Colors.black.withOpacity(0.3), // Dim background
    position: PreferredPosition.bottom,
    pressType: PressType.singleClick,
    // You can define a controller if you need programmatic control
    // controller: _customPopupMenuController,
    child: Container(
      padding: const EdgeInsets.all(8.0),
      decoration: BoxDecoration(color: TColors.primary, borderRadius: BorderRadius.circular(TSizes.cardRadiusMd)),
      child: const Icon(Iconsax.export_2, color: TColors.white),
    ),
    // horizontalMargin: 10, // Adjust margin
    // verticalMargin: 10,
    // enablePassEvent: false, // Prevents event from passing to underlying widgets
  );
}

Widget _buildPopupMenuItem(ReportController reportController, String text, String value) {
  return GestureDetector(
    onTap: () {
      _onMenuItemSelected(reportController, value);
      // Important: Dismiss the popup after selection
      // You might need to access the controller for CustomPopupMenu to dismiss it
      // For simplicity, we'll let it dismiss on click away if dismissOnClickAway is true
      // or rely on a controller if you set one up.
    },
    child: Container(
      padding: const EdgeInsets.symmetric(horizontal: TSizes.md, vertical: TSizes.sm),
      decoration: BoxDecoration(
        color: Colors.transparent, // Default
        borderRadius: BorderRadius.circular(TSizes.cardRadiusSm),
      ),
      child: Text(
        text,
        style: Theme.of(Get.context!).textTheme.bodyMedium!.copyWith(color: TColors.white), // Adjust text color
      ),
    ),
  );
}

// Function to handle menu item selection
void _onMenuItemSelected(ReportController reportController, String value) {
  if (value == 'excel_accounts') {
    reportController.exportToExcel(ReportType.accounts);
  } else if (value == 'excel_accounts_contacts') {
    reportController.exportToExcel(ReportType.accountsWithContacts);
  } else if (value == 'pdf_accounts') {
    reportController.generateAndPreviewPdf(ReportType.accounts);
  } else if (value == 'pdf_accounts_contacts') {
    reportController.generateAndPreviewPdf(ReportType.accountsWithContacts);
  } else if (value == 'csv_accounts') {
    // Assuming you still have CSV methods or would adapt to a generic export
    // reportController.exportToCsv(ReportType.accounts);
    Get.snackbar('CSV Export', 'CSV Accounts Export Triggered (Placeholder)');
  } else if (value == 'csv_accounts_contacts') {
    // reportController.exportToCsv(ReportType.accountsWithContacts);
    Get.snackbar('CSV Export', 'CSV Accounts & Contacts Export Triggered (Placeholder)');
  }
}
