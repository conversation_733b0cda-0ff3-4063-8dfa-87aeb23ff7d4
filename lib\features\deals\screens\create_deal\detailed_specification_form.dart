import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../../../../common/layouts/templates/site_template.dart';
import '../../../../common/widgets/appbar/appbar.dart';
import '../../../../common/widgets/containers/rounded_container.dart';
import '../../../../utils/constants/colors.dart';
import '../../../../utils/constants/enums.dart';
import '../../../../utils/constants/sizes.dart';
import '../../../../utils/helpers/helper_functions.dart';
import '../../controllers/deal_controller.dart';
import '../../controllers/deal_item_controller.dart';
import 'widgets/deal_summary_section.dart';
import 'widgets/product_catalog_browser.dart';
import 'widgets/selected_items_list.dart';

/// Stage 2: Detailed Deal Specification Form
/// Comprehensive item builder with product selection, weight calculations, pricing, and real-time totals
class DetailedSpecificationForm extends StatelessWidget {
  const DetailedSpecificationForm({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DealController());
    final itemController = Get.put(DealItemController());
    final dark = THelperFunctions.isDarkMode(context);

    return TSiteTemplate(
      desktop: _buildDesktopLayout(context, controller, itemController, dark),
      mobile: _buildMobileLayout(context, controller, itemController, dark),
    );
  }

  /// Desktop layout with side-by-side panels
  Widget _buildDesktopLayout(
    BuildContext context,
    DealController controller,
    DealItemController itemController,
    bool dark,
  ) {
    return Scaffold(
      appBar: TAppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Deal Specification', style: Theme.of(context).textTheme.headlineMedium),
            Text('Stage 2: Add detailed items and specifications', style: Theme.of(context).textTheme.bodyMedium),
          ],
        ),
        showBackArrow: true,
        actions: [
          _buildStageIndicator(context),
          const SizedBox(width: TSizes.spaceBtwItems),
          _buildActionButtons(context, controller, itemController),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Left Panel: Product Catalog Browser
            Expanded(
              flex: 2,
              child: TRoundedContainer(
                padding: const EdgeInsets.all(TSizes.md),
                backgroundColor: dark ? TColors.dark : TColors.light,
                child: const ProductCatalogBrowser(),
              ),
            ),
            const SizedBox(width: TSizes.spaceBtwSections),

            // Right Panel: Selected Items & Summary
            Expanded(
              flex: 3,
              child: Column(
                children: [
                  // Deal Summary Section
                  TRoundedContainer(
                    padding: const EdgeInsets.all(TSizes.md),
                    backgroundColor: dark ? TColors.dark : TColors.light,
                    child: const DealSummarySection(),
                  ),
                  const SizedBox(height: TSizes.spaceBtwSections),

                  // Selected Items List
                  Expanded(
                    child: TRoundedContainer(
                      padding: const EdgeInsets.all(TSizes.md),
                      backgroundColor: dark ? TColors.dark : TColors.light,
                      child: const SelectedItemsList(),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Mobile layout with tabbed interface
  Widget _buildMobileLayout(
    BuildContext context,
    DealController controller,
    DealItemController itemController,
    bool dark,
  ) {
    return DefaultTabController(
      length: 3,
      child: Scaffold(
        appBar: TAppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Deal Specification', style: Theme.of(context).textTheme.headlineSmall),
              Text('Stage 2: Add Items', style: Theme.of(context).textTheme.bodySmall),
            ],
          ),
          showBackArrow: true,
          actions: [_buildStageIndicator(context)],
          bottom: const TabBar(
            tabs: [
              Tab(icon: Icon(Iconsax.box), text: 'Products'),
              Tab(icon: Icon(Iconsax.shopping_cart), text: 'Selected'),
              Tab(icon: Icon(Iconsax.receipt), text: 'Summary'),
            ],
          ),
        ),
        body: const TabBarView(children: [ProductCatalogBrowser(), SelectedItemsList(), DealSummarySection()]),
        bottomNavigationBar: _buildMobileBottomBar(context, controller, itemController),
      ),
    );
  }

  /// Stage indicator showing current progress
  Widget _buildStageIndicator(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
      decoration: BoxDecoration(
        color: TColors.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
        border: Border.all(color: TColors.primary),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Iconsax.document_text, size: 16, color: TColors.primary),
          const SizedBox(width: TSizes.xs),
          Text(
            'Stage 2/4',
            style: Theme.of(
              context,
            ).textTheme.labelMedium?.copyWith(color: TColors.primary, fontWeight: FontWeight.w600),
          ),
        ],
      ),
    );
  }

  /// Action buttons for desktop layout
  Widget _buildActionButtons(BuildContext context, DealController controller, DealItemController itemController) {
    return Row(
      children: [
        OutlinedButton.icon(
          onPressed: () => _saveDraft(controller, itemController),
          icon: const Icon(Iconsax.document_download),
          label: const Text('Save Draft'),
        ),
        const SizedBox(width: TSizes.spaceBtwItems),
        ElevatedButton.icon(
          onPressed: () => _proceedToReview(controller, itemController),
          icon: const Icon(Iconsax.arrow_right_3),
          label: const Text('Review & Price'),
        ),
      ],
    );
  }

  /// Bottom navigation bar for mobile layout
  Widget _buildMobileBottomBar(BuildContext context, DealController controller, DealItemController itemController) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [BoxShadow(color: Colors.black.withOpacity(0.1), blurRadius: 10, offset: const Offset(0, -2))],
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () => _saveDraft(controller, itemController),
              icon: const Icon(Iconsax.document_download),
              label: const Text('Save Draft'),
            ),
          ),
          const SizedBox(width: TSizes.spaceBtwItems),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: () => _proceedToReview(controller, itemController),
              icon: const Icon(Iconsax.arrow_right_3),
              label: const Text('Review & Price'),
            ),
          ),
        ],
      ),
    );
  }

  /// Save current progress as draft
  void _saveDraft(DealController controller, DealItemController itemController) {
    // Update deal stage and save
    controller.currentStage.value = DealCreationStage.detailedSpec;
    controller.saveDraftDeal();
  }

  /// Proceed to pricing review stage
  void _proceedToReview(DealController controller, DealItemController itemController) {
    // Validate that at least one item is selected
    if (itemController.dealItems.isEmpty) {
      Get.snackbar(
        'Validation Error',
        'Please add at least one item to proceed',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: TColors.error,
        colorText: TColors.white,
      );
      return;
    }

    // Update stage and proceed
    controller.currentStage.value = DealCreationStage.pricingReview;

    // Navigate to pricing review screen
    Get.toNamed('/dealPricingReview');
  }
}
