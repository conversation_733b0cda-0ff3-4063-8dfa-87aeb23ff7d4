import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../common/widgets/forms/form_section.dart';
import '../../../../../utils/constants/text_strings.dart';
import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_controller.dart';
import '../../../controllers/deal_item_controller.dart';
import '../../../models/deal_model.dart';

class EditDealDesktop extends StatelessWidget {
  final DealModel deal;

  const EditDealDesktop({super.key, required this.deal});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealController>();
    final isDark = THelperFunctions.isDarkMode(context);

    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            _buildHeader(context),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Main Content
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left Column - Deal Information
                Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      _buildDealInfoSection(context, controller, isDark),
                      const SizedBox(height: TSizes.spaceBtwSections),
                      _buildDealDetailsSection(context, controller, isDark),
                    ],
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwSections),

                // Right Column - Deal Items & Summary
                Expanded(
                  flex: 3,
                  child: Column(
                    children: [
                      _buildItemsSection(context, controller, isDark),
                      const SizedBox(height: TSizes.spaceBtwSections),
                      _buildSummarySection(context, isDark),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: TSizes.spaceBtwSections),

            // Action Buttons
            _buildActionButtons(context, controller),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final statusColor = _getStatusColor(deal.status);

    return Row(
      children: [
        Icon(Iconsax.edit, size: 28, color: TColors.primary),
        const SizedBox(width: TSizes.spaceBtwItems),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Edit Deal', style: Theme.of(context).textTheme.headlineMedium),
            Text(deal.dealNumber, style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: Colors.grey[600])),
          ],
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: TSizes.md, vertical: TSizes.sm),
          decoration: BoxDecoration(
            color: statusColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(TSizes.borderRadiusMd),
            border: Border.all(color: statusColor),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(_getStatusIcon(deal.status), size: 16, color: statusColor),
              const SizedBox(width: TSizes.xs),
              Text(
                deal.status.name.toUpperCase(),
                style: Theme.of(
                  context,
                ).textTheme.labelMedium?.copyWith(color: statusColor, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
        const SizedBox(width: TSizes.spaceBtwItems),
        IconButton(onPressed: () => Get.back(), icon: const Icon(Iconsax.close_circle), tooltip: 'Close'),
      ],
    );
  }

  Widget _buildDealInfoSection(BuildContext context, DealController controller, bool isDark) {
    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.lg),
      backgroundColor: isDark ? TColors.dark : TColors.white,
      showBorder: true,
      child: TFormSection(
        title: 'Deal Information',
        children: [
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  initialValue: deal.dealNumber,
                  enabled: false,
                  decoration: InputDecoration(
                    labelText: 'Deal Number',
                    prefixIcon: Icon(Iconsax.document),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                  ),
                ),
              ),
              const SizedBox(width: TSizes.spaceBtwInputFields),
              Expanded(
                child: TextFormField(
                  initialValue: 'v${deal.version}',
                  enabled: false,
                  decoration: InputDecoration(
                    labelText: 'Version',
                    prefixIcon: Icon(Iconsax.code),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwInputFields),
          TextFormField(
            initialValue: deal.projectDetails,
            onChanged: (value) => controller.projectDetailsController.text = value,
            decoration: InputDecoration(
              labelText: 'Project Details',
              prefixIcon: Icon(Iconsax.edit),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
            ),
          ),
          const SizedBox(height: TSizes.spaceBtwInputFields),
          Row(
            children: [
              Expanded(
                child: TextFormField(
                  initialValue: deal.clientName,
                  enabled: false,
                  decoration: InputDecoration(
                    labelText: 'Client Name',
                    prefixIcon: Icon(Iconsax.user),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                  ),
                ),
              ),
              const SizedBox(width: TSizes.spaceBtwInputFields),
              Expanded(
                child: TextFormField(
                  initialValue: deal.salesPersonName,
                  enabled: false,
                  decoration: InputDecoration(
                    labelText: 'Sales Person',
                    prefixIcon: Icon(Iconsax.user_tag),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDealDetailsSection(BuildContext context, DealController controller, bool isDark) {
    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.lg),
      backgroundColor: isDark ? TColors.dark : TColors.white,
      showBorder: true,
      child: TFormSection(
        title: 'Deal Details',
        children: [
          Row(
            children: [
              Expanded(
                child: DropdownButtonFormField<DealPriority>(
                  value: deal.priority,
                  items: DealPriority.values
                      .map(
                        (priority) =>
                            DropdownMenuItem<DealPriority>(value: priority, child: Text(priority.name.toUpperCase())),
                      )
                      .toList(),
                  onChanged: (value) {
                    // Update the deal priority - we'll handle this in the save method
                  },
                  decoration: InputDecoration(
                    labelText: 'Priority',
                    prefixIcon: Icon(Iconsax.flag),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                  ),
                ),
              ),
              const SizedBox(width: TSizes.spaceBtwInputFields),
              Expanded(
                child: TextFormField(
                  initialValue: THelperFunctions.getFormattedDate(deal.validityDate),
                  enabled: false,
                  decoration: InputDecoration(
                    labelText: 'Validity Date',
                    prefixIcon: Icon(Iconsax.calendar),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwInputFields),
          TextFormField(
            initialValue: deal.salesComments ?? '',
            onChanged: (value) => controller.salesNotesController.text = value,
            maxLines: 3,
            decoration: InputDecoration(
              labelText: 'Sales Comments',
              prefixIcon: Icon(Iconsax.message),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
            ),
          ),
          const SizedBox(height: TSizes.spaceBtwInputFields),
          TextFormField(
            initialValue: deal.dealInstructions ?? '',
            onChanged: (value) {
              // Update deal instructions - we'll handle this in the save method
            },
            maxLines: 3,
            decoration: InputDecoration(
              labelText: 'Deal Instructions',
              prefixIcon: Icon(Iconsax.note),
              border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.inputFieldRadius)),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemsSection(BuildContext context, DealController controller, bool isDark) {
    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.lg),
      backgroundColor: isDark ? TColors.dark : TColors.white,
      showBorder: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Iconsax.box, color: TColors.primary),
              const SizedBox(width: TSizes.sm),
              Text('Deal Items', style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: () {
                  // Navigate to detailed specification form for adding items
                  Get.toNamed('/detailed-specification', arguments: deal);
                },
                icon: const Icon(Iconsax.add),
                label: const Text('Add Items'),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          SizedBox(
            height: 300,
            child: Obx(() {
              final itemController = Get.put(DealItemController());
              itemController.loadDealItems(deal.id);

              if (itemController.isLoading.value) {
                return Center(child: CircularProgressIndicator());
              }

              return itemController.dealItems.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Iconsax.box, size: 64, color: Colors.grey[400]),
                          const SizedBox(height: TSizes.spaceBtwItems),
                          Text(
                            'No items added yet',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Colors.grey[600]),
                          ),
                          const SizedBox(height: TSizes.sm),
                          Text(
                            'Add items to this deal using the button above',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: itemController.dealItems.length,
                      itemBuilder: (context, index) {
                        final item = itemController.dealItems[index];
                        return Card(
                          margin: const EdgeInsets.only(bottom: TSizes.sm),
                          child: ListTile(
                            leading: Icon(Iconsax.box, color: TColors.primary),
                            title: Text(item.productName),
                            subtitle: Text('${item.productType.name} | Qty: ${item.quotedQuantity} ${item.quotedUnit}'),
                            trailing: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Text(
                                  'AED ${item.autoCalculatedUnitPrice.toStringAsFixed(2)}',
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                                Text(
                                  'AED ${item.totalItemPrice.toStringAsFixed(2)}',
                                  style: Theme.of(
                                    context,
                                  ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold, color: TColors.primary),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    );
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildSummarySection(BuildContext context, bool isDark) {
    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.lg),
      backgroundColor: isDark ? TColors.dark : TColors.white,
      showBorder: true,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Iconsax.receipt, color: TColors.primary),
              const SizedBox(width: TSizes.sm),
              Text(
                'Deal Summary',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          _buildSummaryRow(context, 'Subtotal', 'AED ${deal.subTotalAmount.toStringAsFixed(2)}'),
          _buildSummaryRow(context, 'Discount', 'AED ${deal.discountAmount.toStringAsFixed(2)}'),
          _buildSummaryRow(context, 'VAT (${deal.vatPercentage}%)', 'AED ${deal.vatAmount.toStringAsFixed(2)}'),
          const Divider(),
          _buildSummaryRow(context, 'Grand Total', 'AED ${deal.grandTotalAmount.toStringAsFixed(2)}', isTotal: true),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(BuildContext context, String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: TSizes.xs),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(fontWeight: isTotal ? FontWeight.bold : FontWeight.normal),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: isTotal ? TColors.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context, DealController controller) {
    return Row(
      children: [
        const Spacer(),
        OutlinedButton(onPressed: () => Get.back(), child: const Text('Cancel')),
        const SizedBox(width: TSizes.sm),
        ElevatedButton.icon(
          onPressed: () => controller.updateDeal(deal),
          icon: const Icon(Iconsax.tick_circle),
          label: const Text('Save Changes'),
        ),
      ],
    );
  }

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Colors.grey;
      case DealStatus.pendingApproval:
        return Colors.orange;
      case DealStatus.approved:
        return Colors.green;
      case DealStatus.rejected:
        return Colors.red;
      case DealStatus.quotationGenerated:
        return Colors.indigo;
      case DealStatus.clientApproved:
        return Colors.teal;
      case DealStatus.clientDeclined:
        return Colors.red.shade300;
      case DealStatus.closed:
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Iconsax.edit;
      case DealStatus.pendingApproval:
        return Iconsax.clock;
      case DealStatus.approved:
        return Iconsax.tick_circle;
      case DealStatus.rejected:
        return Iconsax.close_circle;
      case DealStatus.quotationGenerated:
        return Iconsax.document;
      case DealStatus.clientApproved:
        return Iconsax.like;
      case DealStatus.clientDeclined:
        return Iconsax.dislike;
      case DealStatus.closed:
        return Iconsax.archive;
      default:
        return Iconsax.document;
    }
  }
}
