import 'package:alloy/common/widgets/containers/circular_container.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/controllers/dashboard/dashboard_controller.dart';
import 'package:alloy/utils/helpers/helper_functions.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class TOrderStatusPieChart extends StatelessWidget {
  const TOrderStatusPieChart({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = DashboardController.instance;
    return TRoundedContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Order Status', style: Theme.of(context).textTheme.headlineSmall),

          // Graph
          SizedBox(
            height: 400,
            child: PieChart(
              PieChartData(
                sections: controller.orderStatusData.entries.map((entry) {
                  final status = entry.key;
                  final count = entry.value;

                  return PieChartSectionData(
                    // THelperFunction has colors for each status eg. pending, processing, shipped, etc
                    color: THelperFunctions.getOrderStatusColor(status),
                    value: count.toDouble(),
                    title: '$count',
                    titleStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.white),
                    radius: 100,
                  );
                }).toList(),
                pieTouchData: PieTouchData(touchCallback: (FlTouchEvent event, pieTouchResponse) {}, enabled: true),
              ),
            ),
          ),
          // Show Status and Color Meta Build a LEGEND
          SizedBox(
            width: double.infinity,
            child: DataTable(
              columns: const [
                DataColumn(label: Text('Status')),
                DataColumn(label: Text('Orders')),
                DataColumn(label: Text('Total')),
              ],
              rows: controller.orderStatusData.entries.map((entry) {
                final status = entry.key;
                final int count = entry.value;
                final totalAmount = controller.totalOrderAmount[status] ?? 0.0;
                final color = THelperFunctions.getOrderStatusColor(status);
                return DataRow(
                  cells: [
                    DataCell(
                      Row(
                        children: [
                          TCircularContainer(width: 20, height: 20, backgroundColor: color),
                          Expanded(child: Text('  ${controller.getDisplayStatusName(status)}')),
                        ],
                      ),
                    ),

                    DataCell(Text(count.toString())),
                    DataCell(Text('\$${totalAmount.toStringAsFixed(2)}')),
                  ],
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
