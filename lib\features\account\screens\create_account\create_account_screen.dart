import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/account/screens/create_account/responsive_screens/create_account_desktop.dart';
import 'package:flutter/material.dart';

class CreateAccountScreen extends StatelessWidget {
  const CreateAccountScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(
      desktop: CreateAccountDesktop(),
      tablet: CreateAccountDesktop(),
      mobile: CreateAccountDesktop(), // For now, use desktop layout for all
    );
  }
}
