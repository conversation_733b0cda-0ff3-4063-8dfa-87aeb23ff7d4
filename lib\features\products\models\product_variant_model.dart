import 'package:alloy/utils/constants/enums.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ProductVariantModel {
  String id;
  String productId; // ID of the parent ProductModel (template)
  String sku; // Unique Stock Keeping Unit for this variant
  String categoryId; // Category ID, denormalized for easier querying/filtering
  ProductSegment segment; // Segment, denormalized for easier querying/filtering
  ProductStatus status; // NEW: Status of the variant (active, inactive, discontinued)
  double? weight; // Weight in KG (factory weight)
  double? sqm; // Square Meters (if applicable, e.g., for sheet products - factory SQM)
  Map<String, String> attributes; // e.g., {'Material': 'PG', 'Thickness': '1.2mm', 'Finish': 'Standard', 'Length': '3.0m'}

  // Inventory Buckets
  int quantityOnHand; // Current stock available
  int quantityOnOrder; // Quantity currently on purchase orders
  int quantityInProduction; // Quantity currently being manufactured

  DateTime? createdAt;
  DateTime? updatedAt;

  ProductVariantModel({
    required this.id,
    required this.productId,
    required this.sku,
    required this.categoryId,
    required this.segment,
    this.status = ProductStatus.active, // NEW: Default to active
    this.weight,
    this.sqm,
    required this.attributes,
    this.quantityOnHand = 0,
    this.quantityOnOrder = 0,
    this.quantityInProduction = 0,
    this.createdAt,
    this.updatedAt,
  });

  /// Static function to create an empty ProductVariantModel.
  static ProductVariantModel empty() => ProductVariantModel(
    id: '',
    productId: '',
    sku: '',
    categoryId: '',
    segment: ProductSegment.lengths, // Default segment
    status: ProductStatus.active, // NEW: Default to active
    attributes: {},
  );

  /// CopyWith method for immutability (useful for updates)
  ProductVariantModel copyWith({
    String? id,
    String? productId,
    String? sku,
    String? categoryId,
    ProductSegment? segment,
    ProductStatus? status, // NEW: Add status to copyWith
    double? weight,
    double? sqm,
    Map<String, String>? attributes,
    int? quantityOnHand,
    int? quantityOnOrder,
    int? quantityInProduction,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProductVariantModel(
      id: id ?? this.id,
      productId: productId ?? this.productId,
      sku: sku ?? this.sku,
      categoryId: categoryId ?? this.categoryId,
      segment: segment ?? this.segment,
      status: status ?? this.status, // NEW: Copy status
      weight: weight ?? this.weight,
      sqm: sqm ?? this.sqm,
      attributes: attributes ?? this.attributes,
      quantityOnHand: quantityOnHand ?? this.quantityOnHand,
      quantityOnOrder: quantityOnOrder ?? this.quantityOnOrder,
      quantityInProduction: quantityInProduction ?? this.quantityInProduction,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Convert ProductVariantModel to JSON format for Firestore.
  /// Uses camelCase for database field names as per your requirement.
  Map<String, dynamic> toJson() {
    return {
      'productId': productId,
      'sku': sku,
      'categoryId': categoryId,
      'segment': segment.name, // Store enum name as string
      'status': status.name, // NEW: Store status as string
      'weight': weight,
      'sqm': sqm,
      'attributes': attributes, // Map directly
      'quantityOnHand': quantityOnHand,
      'quantityOnOrder': quantityOnOrder,
      'quantityInProduction': quantityInProduction,
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
      'updatedAt': updatedAt ?? FieldValue.serverTimestamp(), // Update timestamp on modification
    };
  }

  /// Factory method to create a ProductVariantModel from a Firestore document snapshot.
  /// Reads camelCase field names from the database.
  factory ProductVariantModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (document.data() == null) return ProductVariantModel.empty();

    final data = document.data()!;

    // Safely parse segment enum
    ProductSegment parsedSegment = ProductSegment.lengths;
    try {
      if (data['segment'] != null) {
        parsedSegment = ProductSegment.values.firstWhere((e) => e.name == (data['segment'].toString().toLowerCase()), orElse: () => ProductSegment.lengths);
      }
    } catch (e) {
      print('Error parsing segment for product variant ${document.id}: $e');
    }

    // NEW: Safely parse status enum
    ProductStatus parsedStatus = ProductStatus.active; // Default
    try {
      if (data['status'] != null) {
        parsedStatus = ProductStatus.values.firstWhere((e) => e.name == (data['status'].toString().toLowerCase()), orElse: () => ProductStatus.active);
      }
    } catch (e) {
      print('Error parsing status for product variant ${document.id}: $e');
    }

    return ProductVariantModel(
      id: document.id,
      productId: data['productId'] ?? '',
      sku: data['sku'] ?? '',
      categoryId: data['categoryId'] ?? '',
      segment: parsedSegment,
      status: parsedStatus, // NEW: Assign parsed status
      weight: (data['weight'] as num?)?.toDouble(),
      sqm: (data['sqm'] as num?)?.toDouble(),
      attributes: Map<String, String>.from(data['attributes'] ?? {}), // Ensure attributes is a Map<String, String>
      quantityOnHand: (data['quantityOnHand'] as int?) ?? 0,
      quantityOnOrder: (data['quantityOnOrder'] as int?) ?? 0,
      quantityInProduction: (data['quantityInProduction'] as int?) ?? 0,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }
}
