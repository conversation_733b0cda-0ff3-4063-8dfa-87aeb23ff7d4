import 'package:alloy/features/authentication/repository/authentication_repository.dart';
import 'package:alloy/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AuthGuard extends GetMiddleware {
  // Optional: Override priority if you have multiple middlewares.
  // Lower numbers run first.
  // @override
  // int? get priority => 1;

  @override
  RouteSettings? redirect(String? route) {
    final authRepo = AuthenticationRepository.instance;
    print("$AuthGuard : ${authRepo.isAuthenticated}");
    // Ensure authRepo.isAuthenticated accurately reflects the current auth state.
    // This might be a simple getter or backed by an RxBool.
    return authRepo.isAuthenticated ? null : const RouteSettings(name: TRoutes.login);
  }
}
