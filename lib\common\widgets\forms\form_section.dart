import 'package:flutter/material.dart';
import '../../../utils/constants/sizes.dart';
import '../containers/rounded_container.dart';

/// A reusable form section widget that provides consistent styling and layout
/// for grouping related form fields with optional headers and descriptions
class TFormSection extends StatelessWidget {
  const TFormSection({
    super.key,
    required this.title,
    required this.children,
    this.subtitle,
    this.icon,
    this.isRequired = false,
    this.isCollapsible = false,
    this.initiallyExpanded = true,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.showDivider = true,
  });

  /// The main title of the form section
  final String title;

  /// Optional subtitle or description
  final String? subtitle;

  /// Optional icon to display next to the title
  final IconData? icon;

  /// Whether this section contains required fields
  final bool isRequired;

  /// Whether the section can be collapsed/expanded
  final bool isCollapsible;

  /// Initial expansion state (only used if isCollapsible is true)
  final bool initiallyExpanded;

  /// List of form fields or widgets to display in this section
  final List<Widget> children;

  /// Custom padding for the section content
  final EdgeInsetsGeometry? padding;

  /// Custom background color
  final Color? backgroundColor;

  /// Custom border color
  final Color? borderColor;

  /// Whether to show a divider after the header
  final bool showDivider;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    Widget content = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        _buildHeader(context, theme),

        if (showDivider) ...[
          const SizedBox(height: TSizes.spaceBtwItems),
          Divider(color: borderColor ?? theme.dividerColor.withOpacity(0.3), height: 1),
        ],

        const SizedBox(height: TSizes.spaceBtwItems),

        // Content
        ...children.map(
          (child) => Padding(
            padding: const EdgeInsets.only(bottom: TSizes.spaceBtwInputFields),
            child: child,
          ),
        ),
      ],
    );

    if (isCollapsible) {
      content = ExpansionTile(
        title: _buildHeaderContent(context, theme),
        initiallyExpanded: initiallyExpanded,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(TSizes.defaultSpace, 0, TSizes.defaultSpace, TSizes.spaceBtwItems),
            child: Column(children: children),
          ),
        ],
      );
    }

    return TRoundedContainer(
      padding: (padding as EdgeInsets?) ?? const EdgeInsets.all(TSizes.defaultSpace),
      backgroundColor: backgroundColor ?? theme.cardColor,
      borderColor: borderColor ?? theme.dividerColor, // Fix: Ensure borderColor is non-nullable
      child: content,
    );
  }

  Widget _buildHeader(BuildContext context, ThemeData theme) {
    if (isCollapsible) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildHeaderContent(context, theme),
        if (subtitle != null) ...[
          const SizedBox(height: TSizes.xs),
          Text(subtitle!, style: theme.textTheme.bodyMedium?.copyWith(color: theme.textTheme.bodySmall?.color)),
        ],
      ],
    );
  }

  Widget _buildHeaderContent(BuildContext context, ThemeData theme) {
    return Row(
      children: [
        if (icon != null) ...[
          Icon(icon, size: 20, color: theme.primaryColor),
          const SizedBox(width: TSizes.spaceBtwItems),
        ],
        Expanded(
          child: Text(title, style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600)),
        ),
        if (isRequired)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: TSizes.xs, vertical: 2),
            decoration: BoxDecoration(
              color: theme.colorScheme.error.withOpacity(0.1),
              borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
            ),
            child: Text(
              'Required',
              style: theme.textTheme.labelSmall?.copyWith(color: theme.colorScheme.error, fontWeight: FontWeight.w500),
            ),
          ),
      ],
    );
  }
}

/// A specialized form section for grouping related fields in a row layout
class TFormRow extends StatelessWidget {
  const TFormRow({
    super.key,
    required this.children,
    this.spacing = TSizes.spaceBtwInputFields,
    this.crossAxisAlignment = CrossAxisAlignment.start,
  });

  final List<Widget> children;
  final double spacing;
  final CrossAxisAlignment crossAxisAlignment;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: crossAxisAlignment,
      children: children.asMap().entries.map((entry) {
        final index = entry.key;
        final child = entry.value;

        return Expanded(
          child: index < children.length - 1
              ? Padding(
                  padding: EdgeInsets.only(right: spacing),
                  child: child,
                )
              : child,
        );
      }).toList(),
    );
  }
}
