import 'package:alloy/common/widgets/data_table/paginated_data_table.dart';
import 'package:alloy/features/contact/controller/contact_controller.dart';
import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ContactSelectionDialog extends StatefulWidget {
  const ContactSelectionDialog({super.key, required this.initialSelectedContacts, required this.onContactsSelected});

  final List<ContactModel> initialSelectedContacts;
  final Function(List<ContactModel>) onContactsSelected; // Callback to return selected contacts

  @override
  State<ContactSelectionDialog> createState() => _ContactSelectionDialogState();
}

class _ContactSelectionDialogState extends State<ContactSelectionDialog> {
  final contactController = Get.find<ContactController>(); // Still needs to find global ContactController for search
  late List<ContactModel> _selectedContacts; // Use late for initialization in initState

  @override
  void initState() {
    super.initState();
    // Initialize with the contacts passed to the dialog
    _selectedContacts = List.from(widget.initialSelectedContacts);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Select Contacts'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(hintText: 'Search Contacts', prefixIcon: Icon(Icons.search)),
              onChanged: (value) => contactController.searchQuery(value),
            ),
            SizedBox(height: 16),
            Obx(() {
              if (contactController.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }
              return Expanded(
                // Wrap TPaginatedDataTable in Expanded or Flexible for AlertDialog content
                child: TPaginatedDataTable(
                  columns: [
                    DataColumn2(label: Text('Name')),
                    DataColumn2(label: Text('Email')),
                    DataColumn2(label: Text('Phone')),
                  ],
                  source: _ContactDataSource(
                    contacts: contactController.filteredItems,
                    selectedContacts: _selectedContacts,
                    onRowSelected: (contact) {
                      setState(() {
                        if (_selectedContacts.contains(contact)) {
                          _selectedContacts.remove(contact);
                        } else {
                          _selectedContacts.add(contact);
                        }
                      });
                    },
                  ),
                ),
              );
            }),
          ],
        ),
      ),
      actions: [
        TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
        ElevatedButton(
          onPressed: () {
            widget.onContactsSelected(_selectedContacts); // Use the callback
            Get.back();
          },
          child: const Text('Done'),
        ),
      ],
    );
  }
}

class _ContactDataSource extends DataTableSource {
  final List<ContactModel> contacts;
  final List<ContactModel> selectedContacts;
  final Function(ContactModel) onRowSelected;

  _ContactDataSource({required this.contacts, required this.selectedContacts, required this.onRowSelected});

  @override
  DataRow? getRow(int index) {
    final contact = contacts[index];
    return DataRow(
      selected: selectedContacts.contains(
        contact,
      ), // for this search statement to work, we need to override the == and hashCode in the ContactModel
      cells: [DataCell(Text(contact.name)), DataCell(Text(contact.email ?? '')), DataCell(Text(contact.phone))],
      onSelectChanged: (isSelected) {
        onRowSelected(contact);
      },
    );
  }

  @override
  bool get isRowCountApproximate => false;

  @override
  int get rowCount => contacts.length;

  @override
  int get selectedRowCount => 0;
}
