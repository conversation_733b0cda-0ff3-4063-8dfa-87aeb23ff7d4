import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:alloy/features/category/models/category_model.dart';
import 'package:alloy/utils/exceptions/firebase_exceptions.dart';
import 'package:alloy/utils/exceptions/platform_exceptions.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class CategoryRepository extends GetxController {
  static CategoryRepository get instance => Get.find();

  final _db = FirebaseFirestore.instance;

  /// Get all categories
  Future<List<CategoryModel>> getAllCategories() async {
    try {
      final snapshot = await _db.collection('categories').get();
      return snapshot.docs.map((document) => CategoryModel.fromSnapshot(document)).toList();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Stream all categories from Firestore
  Stream<List<CategoryModel>> streamAllCategories() {
    return _db.collection('categories').snapshots().map((snapshot) {
      return snapshot.docs.map((document) => CategoryModel.fromSnapshot(document)).toList();
    });
  }

  /// Delete Category
  Future<void> deleteCategory(String categoryId) async {
    try {
      await _db.collection('categories').doc(categoryId).delete();
    } catch (e) {
      throw 'An unexpected error occurred: $e';
    }
  }

  /// Create a new category
  Future<void> create(CategoryModel category) async {
    try {
      await _db.collection("categories").add(category.toJson());
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }
}
