import 'package:get/get.dart';
import '../../../utils/constants/enums.dart';
import '../../../utils/helpers/network_manager.dart';
import '../../../utils/popups/full_screen_loader.dart';
import '../../../utils/popups/loaders.dart';
import '../../authentication/controllers/user_controller.dart';
import '../models/deal_model.dart';
import '../repositroy/deal_repository.dart';

/// Deal Status Management Controller
/// Manages sequential deal status transitions with role-based validation
class DealStatusController extends GetxController {
  static DealStatusController get instance => Get.find();

  final _dealRepository = Get.put(DealRepository());
  final _userController = Get.put(UserController());

  // Loading states
  final isUpdatingStatus = false.obs;

  /// Submit deal for approval
  Future<void> submitForApproval(DealModel deal) async {
    try {
      isUpdatingStatus.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Validate current status
      if (deal.status != DealStatus.draft) {
        throw 'Only draft deals can be submitted for approval';
      }

      // Validate deal completeness
      _validateDealForSubmission(deal);

      final currentUser = _userController.user.value;

      // Update deal status
      final updatedDeal = deal.copyWith(
        status: DealStatus.pendingApproval,
        requiresApproval: true,
        isLocked: true, // Lock deal during approval process
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
      );

      await _dealRepository.updateDeal(updatedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Submitted for Approval',
        message: 'Deal ${deal.dealNumber} has been submitted for approval.',
      );

      // TODO: Send notification to approvers
      _sendApprovalRequestNotification(deal);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Submission Failed', message: e.toString());
    } finally {
      isUpdatingStatus.value = false;
    }
  }

  /// Generate quotation (after approval)
  Future<void> generateQuotation(DealModel deal) async {
    try {
      isUpdatingStatus.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Validate current status
      if (deal.status != DealStatus.approved) {
        throw 'Only approved deals can have quotations generated';
      }

      // Validate permissions
      if (!_canGenerateQuotation(deal)) {
        throw 'You do not have permission to generate quotation for this deal';
      }

      final currentUser = _userController.user.value;

      // Update deal status
      final updatedDeal = deal.copyWith(
        status: DealStatus.quotationGenerated,
        quotationGeneratedAt: DateTime.now(),
        quotationGeneratedByUserId: currentUser.id!,
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
      );

      await _dealRepository.updateDeal(updatedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Quotation Generated',
        message: 'Quotation for deal ${deal.dealNumber} has been generated.',
      );

      // TODO: Trigger PDF generation
      // Get.find<QuotationPdfController>().generateQuotationPdf(updatedDeal);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Quotation Generation Failed', message: e.toString());
    } finally {
      isUpdatingStatus.value = false;
    }
  }

  /// Mark deal as client approved
  Future<void> markClientApproved(DealModel deal, {String? clientComments}) async {
    try {
      isUpdatingStatus.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Validate current status
      if (deal.status != DealStatus.quotationGenerated) {
        throw 'Only deals with generated quotations can be marked as client approved';
      }

      final currentUser = _userController.user.value;

      // Update deal status
      final updatedDeal = deal.copyWith(
        status: DealStatus.clientApproved,
        clientApprovedAt: DateTime.now(),
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
        // Add client comments if provided
        dealInstructions: clientComments != null
            ? '${deal.dealInstructions ?? ''}\n\nClient Approval Comments: $clientComments'.trim()
            : deal.dealInstructions,
      );

      await _dealRepository.updateDeal(updatedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Client Approved',
        message: 'Deal ${deal.dealNumber} has been marked as client approved.',
      );

      // TODO: Send notification to production team
      _sendClientApprovalNotification(deal);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Status Update Failed', message: e.toString());
    } finally {
      isUpdatingStatus.value = false;
    }
  }

  /// Mark deal as client declined
  Future<void> markClientDeclined(DealModel deal, String declineReason) async {
    try {
      isUpdatingStatus.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Validate current status
      if (deal.status != DealStatus.quotationGenerated) {
        throw 'Only deals with generated quotations can be marked as client declined';
      }

      final currentUser = _userController.user.value;

      // Update deal status
      final updatedDeal = deal.copyWith(
        status: DealStatus.clientDeclined,
        isLocked: false, // Allow editing after client decline
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
        // Add decline reason to deal instructions
        dealInstructions: '${deal.dealInstructions ?? ''}\n\nClient Decline Reason: $declineReason'.trim(),
      );

      await _dealRepository.updateDeal(updatedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Client Declined',
        message: 'Deal ${deal.dealNumber} has been marked as client declined.',
      );

      // TODO: Send notification to sales team
      _sendClientDeclineNotification(deal, declineReason);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Status Update Failed', message: e.toString());
    } finally {
      isUpdatingStatus.value = false;
    }
  }

  /// Close deal
  Future<void> closeDeal(DealModel deal, String closeReason) async {
    try {
      isUpdatingStatus.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Validate permissions
      if (!_canCloseDeal()) {
        throw 'You do not have permission to close deals';
      }

      final currentUser = _userController.user.value;

      // Update deal status
      final updatedDeal = deal.copyWith(
        status: DealStatus.closed,
        closedAt: DateTime.now(),
        closedByUserId: currentUser.id!,
        isLocked: true, // Lock closed deals
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
        // Add close reason to deal instructions
        dealInstructions: '${deal.dealInstructions ?? ''}\n\nDeal Closed: $closeReason'.trim(),
      );

      await _dealRepository.updateDeal(updatedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Deal Closed', message: 'Deal ${deal.dealNumber} has been closed.');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Close Deal Failed', message: e.toString());
    } finally {
      isUpdatingStatus.value = false;
    }
  }

  /// Create superseding deal
  Future<void> createSupersedingDeal(DealModel originalDeal) async {
    try {
      isUpdatingStatus.value = true;
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final currentUser = _userController.user.value;

      // Mark original deal as superseded
      final supersededDeal = originalDeal.copyWith(
        status: DealStatus.superseded,
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
        isLocked: true,
      );

      await _dealRepository.updateDeal(supersededDeal);

      // Create new deal based on original
      final newDeal = originalDeal.copyWith(
        id: '', // Will be generated by Firestore
        dealNumber: '', // Will be generated
        status: DealStatus.draft,
        version: originalDeal.version + 1,
        previousDealId: originalDeal.id,
        createdAt: DateTime.now(),
        createdByUserId: currentUser.id!,
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
        isLocked: false,
        // Reset approval fields
        requiresApproval: false,
        approvedByUserId: null,
        approvalDate: null,
        quotationGeneratedAt: null,
        quotationGeneratedByUserId: null,
        clientApprovedAt: null,
        closedAt: null,
        closedByUserId: null,
      );

      final newDealId = await _dealRepository.createDeal(newDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Superseding Deal Created',
        message: 'New deal version has been created. Original deal marked as superseded.',
      );

      // Navigate to new deal
      Get.toNamed('/deals/$newDealId/edit');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Superseding Deal Creation Failed', message: e.toString());
    } finally {
      isUpdatingStatus.value = false;
    }
  }

  /// Validate deal for submission
  void _validateDealForSubmission(DealModel deal) {
    final errors = <String>[];

    // Check required fields
    if (deal.clientId.isEmpty) errors.add('Client is required');
    if (deal.contactPersonId.isEmpty) errors.add('Contact person is required');
    if (deal.projectDetails.isEmpty) errors.add('Project details are required');
    if (deal.totalDealValue <= 0) errors.add('Deal must have items with value');

    // Check if deal has items
    // TODO: Add validation for deal items count

    if (errors.isNotEmpty) {
      throw 'Deal validation failed:\n${errors.join('\n')}';
    }
  }

  /// Check if user can generate quotation
  bool _canGenerateQuotation(DealModel deal) {
    final currentUser = _userController.user.value;

    // Admins and managers can generate any quotation
    if (currentUser.role == UserRole.admin || currentUser.role == UserRole.manager) {
      return true;
    }

    // Sales users can only generate quotation for their own deals
    if (currentUser.role == UserRole.salesUser) {
      return deal.salesPersonId == currentUser.id;
    }

    return false;
  }

  /// Check if user can close deals
  bool _canCloseDeal() {
    final currentUser = _userController.user.value;
    return currentUser.role == UserRole.admin || currentUser.role == UserRole.manager;
  }

  /// Get allowed status transitions for a deal
  List<DealStatus> getAllowedTransitions(DealModel deal) {
    final currentUser = _userController.user.value;
    final transitions = <DealStatus>[];

    switch (deal.status) {
      case DealStatus.draft:
        transitions.add(DealStatus.pendingApproval);
        break;

      case DealStatus.pendingApproval:
        if (_canApproveDeal(currentUser.role)) {
          transitions.addAll([DealStatus.approved, DealStatus.rejected]);
        }
        break;

      case DealStatus.approved:
        if (_canGenerateQuotation(deal)) {
          transitions.add(DealStatus.quotationGenerated);
        }
        break;

      case DealStatus.quotationGenerated:
        transitions.addAll([DealStatus.clientApproved, DealStatus.clientDeclined]);
        break;

      case DealStatus.clientApproved:
        if (_canCloseDeal()) {
          transitions.add(DealStatus.closed);
        }
        break;

      case DealStatus.clientDeclined:
      case DealStatus.rejected:
        // Can create superseding deal or close
        if (_canCloseDeal()) {
          transitions.add(DealStatus.closed);
        }
        break;

      case DealStatus.unlockRequested:
        if (currentUser.role == UserRole.admin || currentUser.role == UserRole.manager) {
          transitions.add(DealStatus.draft);
        }
        break;

      default:
        break;
    }

    return transitions;
  }

  /// Check if user can approve deals
  bool _canApproveDeal(UserRole role) {
    return role == UserRole.admin || role == UserRole.manager || role == UserRole.productionReviewer;
  }

  /// Send approval request notification (placeholder)
  void _sendApprovalRequestNotification(DealModel deal) {
    // TODO: Implement notification system
    print('Sending approval request notification for deal ${deal.dealNumber}');
  }

  /// Send client approval notification (placeholder)
  void _sendClientApprovalNotification(DealModel deal) {
    // TODO: Implement notification system
    print('Sending client approval notification for deal ${deal.dealNumber}');
  }

  /// Send client decline notification (placeholder)
  void _sendClientDeclineNotification(DealModel deal, String reason) {
    // TODO: Implement notification system
    print('Sending client decline notification for deal ${deal.dealNumber}');
  }

  /// Check if current user can submit deals for approval
  bool get canSubmitForApproval {
    final currentUser = _userController.user.value;
    return currentUser.role == UserRole.salesUser ||
        currentUser.role == UserRole.manager ||
        currentUser.role == UserRole.admin;
  }

  /// Check if current user can generate quotations
  bool get canGenerateQuotations {
    final currentUser = _userController.user.value;
    return currentUser.role == UserRole.salesUser ||
        currentUser.role == UserRole.manager ||
        currentUser.role == UserRole.admin;
  }

  /// Check if current user can close deals
  bool get canCloseDeals {
    return _canCloseDeal();
  }
}
