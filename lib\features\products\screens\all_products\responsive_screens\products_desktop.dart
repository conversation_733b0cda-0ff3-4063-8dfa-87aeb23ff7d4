import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/data_table/table_header.dart';
import 'package:alloy/common/widgets/popups/popup_menu_button.dart';
import 'package:alloy/features/products/controller/product_controller/product_controller.dart';
import 'package:alloy/features/products/screens/all_products/data_table/products_table.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../../../../../common/reporting/controllers/report_controller.dart';
import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../category/controller/category_controller.dart';
import '../../create_product/widget/import_products_widget.dart';
import '../../product_variants/widget/bulk_variant_creation_widget.dart';

class ProductsDesktop extends StatelessWidget {
  // Renamed for clarity based on user's context
  const ProductsDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    final productController = Get.put(ProductController());
    final categoryController = Get.find<CategoryController>(); // For category filter dropdown
    final reportController = Get.put(ReportController());

    return Scaffold(
      body: Padding(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Breadcrumbs
            const TBreadcrumbsWithHeading(
              heading: 'Products Management', // Updated heading
              breadcrumbItems: [TBreadcrumbItem(text: 'Products')], // Updated breadcrumb
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Main Content Area
            Expanded(
              child: TRoundedContainer(
                child: Column(
                  children: [
                    // --- Header Row for Actions, Search, and Filters ---
                    Padding(
                      padding: const EdgeInsets.all(TSizes.md),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start, // Align items to the start
                            crossAxisAlignment: CrossAxisAlignment.center, // Vertically center all items
                            children: [
                              // ################# Printing Menu (Popup Button)
                              popUpMenuButton(reportController),

                              const SizedBox(width: TSizes.spaceBtwItems), // Space after popup menu
                              // ################# Create New Account Button
                              ElevatedButton.icon(
                                icon: const Padding(padding: EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Iconsax.save_add)),
                                onPressed: () => Get.toNamed(TRoutes.createProduct),
                                label: const Text('   Create New Product   '),
                              ),
                              const SizedBox(width: TSizes.spaceBtwItems),
                              // ################## Bulk Create Variants Button
                              ElevatedButton.icon(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext dialogContext) {
                                      return Dialog(
                                        // Using Dialog for a simple modal, or AlertDialog for more structure
                                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(TSizes.cardRadiusLg)),
                                        elevation: 0,
                                        backgroundColor: Colors.transparent, // Make background transparent to show TRoundedContainer's color
                                        child: ConstrainedBox(
                                          // Constrain the size of the dialog content
                                          constraints: BoxConstraints(
                                            maxWidth: 600, // Max width for the dialog
                                            maxHeight: MediaQuery.of(dialogContext).size.height * 0.8, // Max height
                                          ),
                                          child: BulkVariantCreationWidget(), // Navigate to bulk create screen
                                        ),
                                      );
                                    },
                                  );
                                },
                                icon: const Padding(padding: EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Iconsax.magicpen)),
                                label: const Text('Bulk Add Variants   '),
                              ),
                              const SizedBox(width: TSizes.spaceBtwInputFields),
                              // ############### Import Products Button
                              ElevatedButton.icon(
                                onPressed: () {
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext dialogContext) {
                                      return Dialog(
                                        // Using Dialog for a simple modal, or AlertDialog for more structure
                                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(TSizes.cardRadiusLg)),
                                        elevation: 0,
                                        backgroundColor: Colors.transparent, // Make background transparent to show TRoundedContainer's color
                                        child: ConstrainedBox(
                                          // Constrain the size of the dialog content
                                          constraints: BoxConstraints(
                                            maxWidth: 600, // Max width for the dialog
                                            maxHeight: MediaQuery.of(dialogContext).size.height * 0.8, // Max height
                                          ),
                                          child: ImportProductsWidget(), // Your import screen as the dialog content
                                        ),
                                      );
                                    },
                                  );
                                },
                                icon: const Padding(padding: EdgeInsets.symmetric(horizontal: 8.0), child: Icon(Iconsax.import)),
                                label: const Text('Excel Import Products  '),
                              ),
                              const SizedBox(width: TSizes.spaceBtwItems),

                              const Spacer(), // Pushes the search bar to the far right
                              // Search Bar
                              Expanded(
                                flex: 2,
                                child: TTableHeader(
                                  hintText: 'Search Products (Name, Description, Category, Segment, Status)',
                                  searchOnChanged: (query) => productController.searchQuery(query),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: TSizes.spaceBtwItems),

                          // NEW: Advanced Filters Row
                          Row(
                            children: [
                              // Category Filter
                              Expanded(
                                child: Obx(
                                  () => DropdownButtonFormField<String>(
                                    decoration: const InputDecoration(labelText: 'Filter by Category'),
                                    value: productController.selectedCategoryFilterId.value.isEmpty ? null : productController.selectedCategoryFilterId.value,
                                    items: [
                                      const DropdownMenuItem(value: '', child: Text('All Categories')), // Option to clear filter
                                      ...categoryController.allItems.map((category) => DropdownMenuItem(value: category.id, child: Text(category.name))),
                                    ],
                                    onChanged: (value) {
                                      productController.selectedCategoryFilterId.value = value ?? '';
                                    },
                                  ),
                                ),
                              ),
                              const SizedBox(width: TSizes.spaceBtwInputFields),

                              // Segment Filter
                              Expanded(
                                child: Obx(
                                  () => DropdownButtonFormField<ProductSegment?>(
                                    decoration: const InputDecoration(labelText: 'Filter by Segment'),
                                    value: productController.selectedSegmentFilter.value,
                                    items: [
                                      const DropdownMenuItem(value: null, child: Text('All Segments')), // Option to clear filter
                                      ...ProductSegment.values.map((segment) => DropdownMenuItem(value: segment, child: Text(segment.name))),
                                    ],
                                    onChanged: (value) {
                                      productController.selectedSegmentFilter.value = value;
                                    },
                                  ),
                                ),
                              ),
                              const SizedBox(width: TSizes.spaceBtwInputFields),

                              // Status Filter
                              Expanded(
                                child: Obx(
                                  () => DropdownButtonFormField<ProductStatus?>(
                                    decoration: const InputDecoration(labelText: 'Filter by Status'),
                                    value: productController.selectedStatusFilter.value,
                                    items: [
                                      const DropdownMenuItem(value: null, child: Text('All Statuses')), // Option to clear filter
                                      ...ProductStatus.values.map((status) => DropdownMenuItem(value: status, child: Text(status.name))),
                                    ],
                                    onChanged: (value) {
                                      productController.selectedStatusFilter.value = value;
                                    },
                                  ),
                                ),
                              ),
                              const SizedBox(width: TSizes.spaceBtwInputFields),

                              // Show Discontinued Products Toggle (moved here for better grouping)
                              Obx(
                                () => Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Switch(
                                      value: productController.showDiscontinuedProducts.value,
                                      onChanged: (value) {
                                        productController.showDiscontinuedProducts.value = value;
                                      },
                                      activeColor: TColors.primary,
                                    ),
                                    Text('Show Discontinued', style: Theme.of(context).textTheme.labelLarge),
                                  ],
                                ),
                              ),
                              const SizedBox(width: TSizes.spaceBtwInputFields),

                              // Clear Filters Button
                              OutlinedButton(onPressed: () => productController.clearFilters(), child: const Text('Clear Filters')),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Table - Wrapped in Expanded
                    const Expanded(child: ProductsTable()),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
