import 'package:alloy/utils/constants/colors.dart';
import 'package:flutter/material.dart';

class TPrimaryHeaderContainer extends StatelessWidget {
  const TPrimaryHeaderContainer({super.key, required this.child, this.height = 400});

  final Widget child;
  final double height;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: height,
      child: Stack(
        children: [
          Positioned(
            top: -150,
            right: -250,
            child: TCircularContainer(backgroundColor: TColors.textWhite.withOpacity(0.1)),
          ),
          Positioned(
            top: 100,
            right: -300,
            child: TCircularContainer(backgroundColor: TColors.textWhite.withOpacity(0.1)),
          ),
          child,
        ],
      ),
    );
  }
}

// Helper for TPrimaryHeaderContainer
class TCircularContainer extends StatelessWidget {
  const TCircularContainer({
    super.key,
    this.width = 400,
    this.height = 400,
    this.radius = 400,
    this.padding = 0,
    this.child,
    this.backgroundColor = TColors.white,
  });

  final double? width;
  final double? height;
  final double radius;
  final double padding;
  final Widget? child;
  final Color backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(radius), color: backgroundColor),
      child: child,
    );
  }
}
