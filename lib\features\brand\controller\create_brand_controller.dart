import 'package:alloy/features/brand/models/brand_category_model.dart';
import 'package:alloy/features/category/models/category_model.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:alloy/features/brand/models/brand_model.dart';
import 'package:alloy/features/brand/repository/brand_repository.dart';
import 'package:alloy/common/media/controller/media_controller.dart';
import 'package:alloy/common/media/models/image_model.dart';

class CreateBrandController extends GetxController {
  static CreateBrandController get instance => Get.find();

  /// Variables
  final isFeatured = false.obs;
  final isLoading = false.obs;
  final nameController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  final RxList<CategoryModel> selectedCategories = <CategoryModel>[].obs;

  RxString imageUrl = ''.obs;

  /// -- Toggle Category Selection to add to the brandCategory relationship
  void toggleSelection(CategoryModel category) {
    // Check if the category is already selected
    if (selectedCategories.contains(category)) {
      // If yes, remove it from the list
      selectedCategories.remove(category);
    } else {
      // If no, add it to the list
      selectedCategories.add(category);
    }
  }

  /// -- Create Brand
  Future<void> createBrand() async {
    try {
      // TFullScreenLoader.openLoadingDialog(
      //   'Creating Brand',
      //   TImages.defaultLoaderAnimation,
      // );
      // OR JUST A CIRCULAR LOADER
      TFullScreenLoader.popUpCircular();

      // Check Internet Connectivity
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        return;
      }

      // Form Validation
      if (!formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Create Brand Map
      final newBrand = BrandModel(
        id: '',
        productsCount: 0,
        name: nameController.text.trim(),
        image: imageUrl.value,
        createdAt: DateTime.now(),
        isFeatured: isFeatured.value,
      );

      // Save Brand
      // And also get the document ID from the repository and to be added it to the categoryModel
      newBrand.id = await BrandRepository.instance.createBrand(newBrand);

      // Create Brand Category Relationship
      if (selectedCategories.isNotEmpty) {
        if (newBrand.id.isEmpty) {
          throw 'Error storing relational data.Brand ID is empty';
        }
        for (final category in selectedCategories) {
          final newBrandCategory = BrandCategoryModel(id: '', brandId: newBrand.id, categoryId: category.id);
          await BrandRepository.instance.createBrandCategory(newBrandCategory);
        }

        /// Add the selected categories to the brand model's brandCategories list
        // This is not mapped in the BrandModel, so it will not be saved in the database
        // if brandCategories is null, initialize it
        newBrand.brandCategories ??= [];
        // Append all selected categories to the brandCategories list
        newBrand.brandCategories!.addAll(selectedCategories);
      }
      // Update All Data List
      // BrandController.instance.addItemToLists(newBrand);

      // Clear Form
      clearForm();

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Brand Created', message: 'Category ${newBrand.name} created successfully.');
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    } finally {
      // Remove Loader
      isLoading.value = false;
    }
  }

  /// -- Pick Image while creating a category
  Future<void> pickImage() async {
    final controller = Get.put(MediaController());

    // Select Image from Media function has an inbuilt bottom sheet that returns a list of selected images
    List<ImageModel>? selectedImages = await controller.selectImagesFromMedia();

    // Handle the selected images
    if (selectedImages != null && selectedImages.isNotEmpty) {
      // Select the first image from the list if selected images (to make a thumbnail)
      ImageModel selectedImage = selectedImages.first;
      // Update the selected thumbnail image URL
      imageUrl.value = selectedImage.url;
    }
  }

  /// -- Reset Fields and Clear Form
  void clearForm() {
    nameController.clear();
    isLoading(false);
    imageUrl.value = '';
    isFeatured(false);
    selectedCategories.clear();
  }
}
