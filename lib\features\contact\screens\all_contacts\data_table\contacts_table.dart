import 'package:alloy/common/widgets/data_table/paginated_data_table.dart';
import 'package:alloy/features/contact/controller/contact_controller.dart';
import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/features/contact/screens/all_contacts/data_table/table_source.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ContactsTable extends StatelessWidget {
  const ContactsTable({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ContactController>();
    final isMobile = TDeviceUtils.isMobileScreen(context);

    return Obx(() {
      // This is just to trigger the Obx.
      Text(controller.filteredItems.length.toString());
      Text(controller.selectedRows.length.toString());

      return TPaginatedDataTable(
        minWidth: 800,
        sortAscending: controller.sortAscending.value,
        sortColumnIndex: controller.sortColumnIndex.value,
        columns: [
          DataColumn2(
            label: Text('Name'),
            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (ContactModel contact) => contact.name.toLowerCase()),
          ),
          DataColumn2(
            label: Text('Email'),
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ContactModel contact) => contact.email?.toLowerCase() ?? '',
            ),
          ),
          DataColumn2(
            label: Text('Phone'),
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ContactModel contact) => contact.phone.toLowerCase(),
            ),
          ),
          DataColumn2(label: Text('Designation'), fixedWidth: isMobile ? null : 150),
          DataColumn2(label: Text('Status'), fixedWidth: isMobile ? null : 120),
          DataColumn2(label: Text('Accounts'), fixedWidth: isMobile ? null : 100),
          DataColumn2(label: Text('Action'), fixedWidth: isMobile ? null : 100),
        ],
        source: ContactsRows(),
      );
    });
  }
}
