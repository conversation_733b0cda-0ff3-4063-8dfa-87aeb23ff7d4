import 'package:alloy/utils/constants/colors.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TChoiceChipList extends StatelessWidget {
  const TChoiceChipList({super.key, required this.status, required this.labels});

  final String status;
  final Map<String, String> labels;

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    Color chipColor;
    switch (status.toLowerCase()) {
      case 'active':
        chipColor = TColors.success;
        break;
      case 'inactive':
        chipColor = TColors.error;
        break;
      default:
        chipColor = TColors.warning;
    }
    return Chip(
      label: Text(
        labels[status.toLowerCase()] ?? status.capitalizeFirst!,
        style: TextStyle(color: isDarkMode ? TColors.black : TColors.white),
      ),
      backgroundColor: chipColor,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
    );
  }
}
