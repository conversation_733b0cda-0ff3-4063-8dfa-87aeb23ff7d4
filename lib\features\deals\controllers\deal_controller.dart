import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../data/abstract/base_data_table_controller.dart';
import '../../../features/account/controller/account_controller.dart';
import '../../../features/account/models/account_model.dart';
import '../../../features/authentication/controllers/user_controller.dart';
import '../../../features/contact/controller/contact_controller.dart';
import '../../../features/contact/models/contact_model.dart';
import '../../../features/settings/controllers/settings_controller.dart';
import '../../../utils/constants/enums.dart';
import '../../../utils/helpers/network_manager.dart';
import '../../../utils/popups/full_screen_loader.dart';
import '../../../utils/popups/loaders.dart';
import '../models/deal_model.dart';
import '../repositroy/deal_repository.dart';

/// Controller for managing Deal operations with role-based access control
class DealController extends TBaseController<DealModel> {
  static DealController get instance => Get.find();

  // Dependencies
  final DealRepository _dealRepository = DealRepository.instance;
  final UserController _userController = UserController.instance;
  final SettingsController _settingsController = SettingsController.instance;
  final AccountController _accountController = Get.put(AccountController());
  final ContactController _contactController = Get.put(ContactController());

  // Form key for deal forms
  final formKey = GlobalKey<FormState>();

  // Two-Stage Deal Creation Controllers
  final basicRequirementsController = TextEditingController();
  final estimatedQuantityController = TextEditingController();
  final projectDetailsController = TextEditingController();
  final expectedTimelineController = TextEditingController();
  final budgetRangeController = TextEditingController();
  final salesNotesController = TextEditingController();
  final clientLPOController = TextEditingController();

  // Two-Stage Deal Creation State
  final RxString selectedClientId = ''.obs;
  final RxString selectedContactId = ''.obs;
  final RxString urgencyLevel = ''.obs;
  final Rx<DealCreationStage> currentStage = DealCreationStage.basicIntent.obs;
  final RxBool useClientDescriptions = false.obs;

  // Filter variables specific to deals
  final RxString selectedClientFilter = ''.obs;
  final Rxn<DealStatus> selectedStatusFilter = Rxn<DealStatus>();
  final RxBool showLockedDeals = true.obs;
  final RxBool showOnlyMyDeals = false.obs; // Toggle for sales users

  // Real data for clients and contacts
  final RxList<AccountModel> clients = <AccountModel>[].obs;
  final RxList<ContactModel> contacts = <ContactModel>[].obs;

  @override
  void onInit() {
    super.onInit();

    // Listen to filter changes to re-trigger stream
    debounce(selectedClientFilter, (_) => listenToStream(), time: const Duration(milliseconds: 300));
    debounce(selectedStatusFilter, (_) => listenToStream(), time: const Duration(milliseconds: 300));
    debounce(showLockedDeals, (_) => listenToStream(), time: const Duration(milliseconds: 300));
    debounce(showOnlyMyDeals, (_) => listenToStream(), time: const Duration(milliseconds: 300));

    // Load clients and contacts data
    _loadClientsAndContacts();
  }

  /// Load clients and contacts data from repositories
  void _loadClientsAndContacts() {
    // Load all accounts as potential clients
    _accountController.streamItems().listen((accounts) {
      // Filter for customer accounts only (customer, partner, or other can be clients)
      final customerAccounts = accounts
          .where(
            (account) =>
                account.businessType == BusinessType.customer ||
                account.businessType == BusinessType.partner ||
                account.businessType == BusinessType.other,
          )
          .toList();
      clients.assignAll(customerAccounts);
    });

    // Load all contacts
    _contactController.streamItems().listen((allContacts) {
      contacts.assignAll(allContacts);
    });
  }

  @override
  Future<List<DealModel>> fetchItems() async {
    // Not primarily used with streaming approach
    return [];
  }

  @override
  Stream<List<DealModel>> streamItems() {
    final currentUser = _userController.user.value;

    return _dealRepository
        .streamAllDeals(
          currentUserId: currentUser.id ?? '',
          currentUserRoles: currentUser.roles,
          searchQuery: null, // Search is handled by base controller
          statusFilter: selectedStatusFilter.value,
          clientIdFilter: selectedClientFilter.value.isNotEmpty ? selectedClientFilter.value : null,
          showLocked: showLockedDeals.value,
        )
        .map((deals) {
          // Apply additional client-side filtering for "My Deals" toggle
          if (showOnlyMyDeals.value &&
              !currentUser.roles.contains(UserRole.Admin) &&
              !currentUser.roles.contains(UserRole.Manager)) {
            return deals.where((deal) => deal.ownerId == currentUser.id).toList();
          }
          return deals;
        });
  }

  @override
  bool containsSearchQuery(DealModel item, String searchText) {
    final searchLower = searchText.toLowerCase();
    return item.dealNumber.toLowerCase().contains(searchLower) ||
        item.clientName.toLowerCase().contains(searchLower) ||
        item.projectDetails.toLowerCase().contains(searchLower) ||
        item.salesPersonName.toLowerCase().contains(searchLower) ||
        item.paymentTerms.toLowerCase().contains(searchLower) ||
        item.deliveryTime.toLowerCase().contains(searchLower);
  }

  @override
  Comparable getComparableProperty(DealModel item, int columnIndex) {
    switch (columnIndex) {
      case 0:
        return item.dealNumber.toLowerCase();
      case 1:
        return item.clientName.toLowerCase();
      case 2:
        return item.projectDetails.toLowerCase();
      case 3:
        return item.status.name.toLowerCase();
      case 4:
        return item.dealDate;
      case 5:
        return item.validityDate;
      case 6:
        return item.grandTotalAmount;
      case 7:
        return item.salesPersonName.toLowerCase();
      default:
        return '';
    }
  }

  @override
  Future<void> deleteItem(DealModel item) async {
    try {
      // Check permissions - only allow deletion if user is owner or has admin/manager role
      final currentUser = _userController.user.value;
      if (item.ownerId != currentUser.id &&
          !currentUser.roles.contains(UserRole.Admin) &&
          !currentUser.roles.contains(UserRole.Manager)) {
        TLoaders.errorSnackBar(title: 'Permission Denied', message: 'You can only delete your own deals.');
        return;
      }

      // Check if deal is locked
      if (item.isLocked) {
        TLoaders.errorSnackBar(title: 'Cannot Delete', message: 'Cannot delete a locked deal. Please unlock it first.');
        return;
      }

      await _dealRepository.deleteDeal(item.id);
      TLoaders.successSnackBar(title: 'Success', message: 'Deal "${item.dealNumber}" deleted successfully.');
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Generate next deal number based on settings
  Future<String> generateNextDealNumber() async {
    try {
      await _settingsController.fetchSettings();
      final settings = _settingsController.globalSettings.value;
      final prefix = settings.nextQuotationNumberPrefix;
      final nextNumber = settings.nextQuotationNumberStart;

      // You might want to implement a counter in settings or use timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString().substring(8);
      return '$prefix$nextNumber$timestamp';
    } catch (e) {
      // Fallback if settings not available
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString().substring(8);
      return 'Q-$timestamp';
    }
  }

  /// Create a new deal
  Future<void> createDeal(DealModel deal) async {
    try {
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Generate deal number if not provided
      if (deal.dealNumber.isEmpty) {
        deal = deal.copyWith(dealNumber: await generateNextDealNumber());
      }

      // Set current user as owner
      final currentUser = _userController.user.value;
      deal = deal.copyWith(
        ownerId: currentUser.id!,
        salesPersonId: currentUser.id!,
        salesPersonName: '${currentUser.firstName} ${currentUser.lastName}'.trim(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdByUserId: currentUser.id!,
        updatedByUserId: currentUser.id!,
      );

      await _dealRepository.createDeal(deal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Deal "${deal.dealNumber}" created successfully.');

      // Navigate back or to deal details
      Get.back();
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Update an existing deal
  Future<void> updateDeal(DealModel deal) async {
    try {
      TFullScreenLoader.popUpCircular();

      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Check if deal is locked and user has permission to edit
      if (deal.isLocked) {
        final currentUser = _userController.user.value;
        if (!currentUser.roles.contains(UserRole.Admin) && !currentUser.roles.contains(UserRole.Manager)) {
          TFullScreenLoader.stopLoading();
          TLoaders.errorSnackBar(title: 'Deal Locked', message: 'This deal is locked and cannot be edited.');
          return;
        }
      }

      // Update audit fields
      final currentUser = _userController.user.value;
      deal = deal.copyWith(updatedAt: DateTime.now(), updatedByUserId: currentUser.id!);

      await _dealRepository.updateDeal(deal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Deal "${deal.dealNumber}" updated successfully.');

      Get.back();
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Approve a deal (Manager/Admin only)
  Future<void> approveDeal(DealModel deal) async {
    try {
      final currentUser = _userController.user.value;

      // Check permissions
      if (!currentUser.roles.contains(UserRole.Admin) && !currentUser.roles.contains(UserRole.Manager)) {
        TLoaders.errorSnackBar(title: 'Permission Denied', message: 'Only managers and admins can approve deals.');
        return;
      }

      TFullScreenLoader.popUpCircular();

      final approvedDeal = deal.copyWith(
        status: DealStatus.Approved,
        isLocked: true,
        approvedByUserId: currentUser.id!,
        approvalDate: DateTime.now(),
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
      );

      await _dealRepository.updateDeal(approvedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Deal "${deal.dealNumber}" approved successfully.');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Reject a deal (Manager/Admin only)
  Future<void> rejectDeal(DealModel deal, {String? reason}) async {
    try {
      final currentUser = _userController.user.value;

      // Check permissions
      if (!currentUser.roles.contains(UserRole.Admin) && !currentUser.roles.contains(UserRole.Manager)) {
        TLoaders.errorSnackBar(title: 'Permission Denied', message: 'Only managers and admins can reject deals.');
        return;
      }

      TFullScreenLoader.popUpCircular();

      final rejectedDeal = deal.copyWith(
        status: DealStatus.Rejected,
        isLocked: false, // Allow editing after rejection
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
      );

      await _dealRepository.updateDeal(rejectedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Deal "${deal.dealNumber}" rejected.');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Request unlock for a locked deal
  Future<void> requestUnlock(DealModel deal) async {
    try {
      TFullScreenLoader.popUpCircular();

      final unlockRequestDeal = deal.copyWith(
        status: DealStatus.UnlockRequested,
        updatedAt: DateTime.now(),
        updatedByUserId: _userController.user.value.id!,
      );

      await _dealRepository.updateDeal(unlockRequestDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Unlock request submitted for deal "${deal.dealNumber}".');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Unlock a deal (Manager/Admin only)
  Future<void> unlockDeal(DealModel deal) async {
    try {
      final currentUser = _userController.user.value;

      // Check permissions
      if (!currentUser.roles.contains(UserRole.Admin) && !currentUser.roles.contains(UserRole.Manager)) {
        TLoaders.errorSnackBar(title: 'Permission Denied', message: 'Only managers and admins can unlock deals.');
        return;
      }

      TFullScreenLoader.popUpCircular();

      final unlockedDeal = deal.copyWith(
        isLocked: false,
        status: DealStatus.Draft, // Reset to draft for editing
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
      );

      await _dealRepository.updateDeal(unlockedDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Deal "${deal.dealNumber}" unlocked successfully.');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Create a new version of an existing deal
  Future<void> createDealVersion(DealModel originalDeal) async {
    try {
      TFullScreenLoader.popUpCircular();

      final currentUser = _userController.user.value;
      final newVersion = originalDeal.version + 1;
      final newDealNumber = '${originalDeal.dealNumber}-v$newVersion';

      final newDeal = originalDeal.copyWith(
        id: '', // Will be generated by Firestore
        dealNumber: newDealNumber,
        version: newVersion,
        previousDealId: originalDeal.id,
        status: DealStatus.Draft,
        isLocked: false,
        approvedByUserId: null,
        approvalDate: null,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdByUserId: currentUser.id!,
        updatedByUserId: currentUser.id!,
      );

      // Mark original deal as superseded
      final supersededDeal = originalDeal.copyWith(
        status: DealStatus.Superseded,
        updatedAt: DateTime.now(),
        updatedByUserId: currentUser.id!,
      );

      await _dealRepository.updateDeal(supersededDeal);
      await _dealRepository.createDeal(newDeal);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'New version "$newDealNumber" created successfully.');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: e.toString());
    }
  }

  /// Check if current user can edit the deal
  bool canEditDeal(DealModel deal) {
    final currentUser = _userController.user.value;

    // Admin and Manager can edit any deal
    if (currentUser.roles.contains(UserRole.Admin) || currentUser.roles.contains(UserRole.Manager)) {
      return true;
    }

    // Owner can edit if not locked
    if (deal.ownerId == currentUser.id && !deal.isLocked) {
      return true;
    }

    return false;
  }

  /// Check if current user can approve the deal
  bool canApproveDeal(DealModel deal) {
    final currentUser = _userController.user.value;
    return (currentUser.roles.contains(UserRole.Admin) || currentUser.roles.contains(UserRole.Manager)) &&
        deal.status == DealStatus.PendingApproval;
  }

  /// Check if current user can delete the deal
  bool canDeleteDeal(DealModel deal) {
    final currentUser = _userController.user.value;

    // Admin can delete any unlocked deal
    if (currentUser.roles.contains(UserRole.Admin) && !deal.isLocked) {
      return true;
    }

    // Owner can delete their own unlocked deal
    if (deal.ownerId == currentUser.id && !deal.isLocked) {
      return true;
    }

    return false;
  }

  /// Clear all filters
  void clearAllFilters() {
    selectedClientFilter.value = '';
    selectedStatusFilter.value = null;
    showLockedDeals.value = true;
    showOnlyMyDeals.value = false;
  }

  /// Toggle row selection for data table
  void toggleRowSelection(int index) {
    if (index >= 0 && index < selectedRows.length) {
      selectedRows[index] = !selectedRows[index];
    }
  }

  // ===== TWO-STAGE DEAL CREATION METHODS =====

  /// Select client for deal creation
  void selectClient(String clientId) {
    selectedClientId.value = clientId;
    selectedContactId.value = ''; // Reset contact selection
    // Load client contacts here
    _loadClientContacts(clientId);
  }

  /// Select contact person for deal
  void selectContact(String contactId) {
    selectedContactId.value = contactId;
  }

  /// Get contacts for selected client
  List<ContactModel> getClientContacts() {
    // Return filtered contacts for selected client based on accountDetails
    return contacts
        .where((contact) => contact.accountDetails?.any((account) => account.id == selectedClientId.value) ?? false)
        .toList();
  }

  /// Load contacts for a specific client
  void _loadClientContacts(String clientId) {
    // Contacts are already loaded in _loadClientsAndContacts()
    // This method is called when client selection changes to trigger UI updates
    selectedContactId.value = ''; // Reset contact selection when client changes
  }

  /// Validate basic intent form
  bool validateBasicIntent() {
    if (selectedClientId.value.isEmpty) {
      TLoaders.errorSnackBar(title: 'Validation Error', message: 'Please select a client');
      return false;
    }

    if (basicRequirementsController.text.trim().isEmpty) {
      TLoaders.errorSnackBar(title: 'Validation Error', message: 'Please enter basic requirements');
      return false;
    }

    return true;
  }

  /// Save deal as draft
  Future<void> saveDraftDeal() async {
    try {
      TFullScreenLoader.openLoadingDialog('Saving draft...', 'assets/animations/loading.json');

      // Create deal model with basic intent data
      final deal = DealModel(
        id: '', // Will be generated by Firestore
        dealNumber: await _generateDealNumber(),
        ownerId: _userController.user.value.id ?? '',
        clientId: selectedClientId.value,
        clientName: _getClientName(selectedClientId.value),
        clientAddress: _getClientAddress(selectedClientId.value),
        dealDate: DateTime.now(),
        validityDate: DateTime.now().add(const Duration(days: 30)),
        projectDetails: projectDetailsController.text.trim(),
        creationStage: DealCreationStage.basicIntent,
        basicRequirements: basicRequirementsController.text.trim(),
        salesPersonNotes: salesNotesController.text.trim(),
        salesComments: salesNotesController.text.trim(), // Sales comments for production team
        useClientDescriptions: useClientDescriptions.value,
        status: DealStatus.Draft,
        createdByUserId: _userController.user.value.id ?? '',
        salesPersonId: _userController.user.value.id ?? '',
        salesPersonName: _userController.user.value.fullName,
      );

      await _dealRepository.createDeal(deal);
      TFullScreenLoader.stopLoading();
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error', message: 'Failed to save draft: ${e.toString()}');
    }
  }

  /// Proceed to detailed specification stage
  void proceedToDetailedSpecification() {
    currentStage.value = DealCreationStage.detailedSpec;
    // Additional logic for stage transition
  }

  /// Generate unique deal number
  Future<String> _generateDealNumber() async {
    // Implement deal number generation logic
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    return 'DEAL-${timestamp.toString().substring(8)}';
  }

  /// Get client name by ID (mock implementation)
  String _getClientName(String clientId) {
    // Replace with actual client data fetching
    return 'Client Name';
  }

  /// Get client address by ID (mock implementation)
  String _getClientAddress(String clientId) {
    // Replace with actual client data fetching
    return 'Client Address';
  }

  /// Clear form data
  void clearFormData() {
    basicRequirementsController.clear();
    estimatedQuantityController.clear();
    projectDetailsController.clear();
    expectedTimelineController.clear();
    budgetRangeController.clear();
    salesNotesController.clear();
    clientLPOController.clear();
    selectedClientId.value = '';
    selectedContactId.value = '';
    urgencyLevel.value = '';
    useClientDescriptions.value = false;
    currentStage.value = DealCreationStage.basicIntent;
  }

  @override
  void onClose() {
    // Dispose controllers
    basicRequirementsController.dispose();
    estimatedQuantityController.dispose();
    projectDetailsController.dispose();
    expectedTimelineController.dispose();
    budgetRangeController.dispose();
    salesNotesController.dispose();
    clientLPOController.dispose();
    super.onClose();
  }
}
