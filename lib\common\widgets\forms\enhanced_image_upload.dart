import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../../utils/constants/sizes.dart';
import '../containers/rounded_container.dart';

/// Enhanced image upload component with drag-and-drop, preview, and progress
class TEnhancedImageUpload extends StatefulWidget {
  const TEnhancedImageUpload({
    super.key,
    required this.onImageSelected,
    this.initialImageUrl,
    this.labelText = 'Upload Image',
    this.helperText,
    this.isRequired = false,
    this.maxWidth = 200,
    this.maxHeight = 200,
    this.allowMultiple = false,
    this.acceptedFormats = const ['jpg', 'jpeg', 'png', 'gif'],
    this.maxFileSizeMB = 5,
    this.showProgress = true,
    this.borderRadius,
    this.backgroundColor,
    this.borderColor,
    this.onImageRemoved,
  });

  final void Function(XFile file) onImageSelected;
  final void Function()? onImageRemoved;
  final String? initialImageUrl;
  final String labelText;
  final String? helperText;
  final bool isRequired;
  final double maxWidth;
  final double maxHeight;
  final bool allowMultiple;
  final List<String> acceptedFormats;
  final double maxFileSizeMB;
  final bool showProgress;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final Color? borderColor;

  @override
  State<TEnhancedImageUpload> createState() => _TEnhancedImageUploadState();
}

class _TEnhancedImageUploadState extends State<TEnhancedImageUpload> {
  XFile? _selectedFile;
  bool _isUploading = false;
  double _uploadProgress = 0.0;
  String? _errorMessage;
  bool _isDragOver = false;

  final ImagePicker _picker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Label with required indicator
        if (widget.labelText.isNotEmpty) ...[
          Row(
            children: [
              Text(widget.labelText, style: theme.textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w500)),
              if (widget.isRequired) ...[
                const SizedBox(width: 4),
                Text(
                  '*',
                  style: TextStyle(color: theme.colorScheme.error, fontWeight: FontWeight.bold),
                ),
              ],
            ],
          ),
          const SizedBox(height: TSizes.xs),
        ],

        // Upload Area
        _buildUploadArea(context, theme),

        // Helper text
        if (widget.helperText != null) ...[
          const SizedBox(height: TSizes.xs),
          Text(widget.helperText!, style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor)),
        ],

        // Error message
        if (_errorMessage != null) ...[
          const SizedBox(height: TSizes.xs),
          Text(_errorMessage!, style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.error)),
        ],

        // Upload progress
        if (_isUploading && widget.showProgress) ...[
          const SizedBox(height: TSizes.spaceBtwItems),
          LinearProgressIndicator(value: _uploadProgress),
          const SizedBox(height: TSizes.xs),
          Text('Uploading... ${(_uploadProgress * 100).toInt()}%', style: theme.textTheme.bodySmall),
        ],
      ],
    );
  }

  Widget _buildUploadArea(BuildContext context, ThemeData theme) {
    final hasImage = _selectedFile != null || widget.initialImageUrl != null;

    return GestureDetector(
      onTap: _pickImage,
      child: MouseRegion(
        onEnter: (_) => setState(() => _isDragOver = true),
        onExit: (_) => setState(() => _isDragOver = false),
        child: TRoundedContainer(
          width: widget.maxWidth,
          height: widget.maxHeight,
          backgroundColor:
              widget.backgroundColor ?? (_isDragOver ? theme.primaryColor.withOpacity(0.1) : theme.cardColor),
          borderColor: widget.borderColor ?? (_isDragOver ? theme.primaryColor : theme.dividerColor),
          //borderRadius: widget.borderRadius,
          child: hasImage ? _buildImagePreview(theme) : _buildUploadPrompt(theme),
        ),
      ),
    );
  }

  Widget _buildImagePreview(ThemeData theme) {
    return Stack(
      children: [
        // Image
        ClipRRect(
          borderRadius: widget.borderRadius ?? BorderRadius.circular(TSizes.borderRadiusMd),
          child: SizedBox(width: widget.maxWidth, height: widget.maxHeight, child: _buildImage()),
        ),

        // Remove button
        if (widget.onImageRemoved != null)
          Positioned(
            top: 8,
            right: 8,
            child: GestureDetector(
              onTap: () {
                setState(() {
                  _selectedFile = null;
                  _errorMessage = null;
                });
                widget.onImageRemoved?.call();
              },
              child: Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(color: theme.colorScheme.error, shape: BoxShape.circle),
                child: const Icon(Icons.close, color: Colors.white, size: 16),
              ),
            ),
          ),

        // Upload overlay
        if (_isUploading)
          Container(
            width: widget.maxWidth,
            height: widget.maxHeight,
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.5),
              borderRadius: widget.borderRadius ?? BorderRadius.circular(TSizes.borderRadiusMd),
            ),
            child: const Center(child: CircularProgressIndicator(color: Colors.white)),
          ),
      ],
    );
  }

  Widget _buildImage() {
    if (_selectedFile != null) {
      if (kIsWeb) {
        return Image.network(
          _selectedFile!.path,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
        );
      } else {
        return Image.file(
          File(_selectedFile!.path),
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
        );
      }
    } else if (widget.initialImageUrl != null) {
      return Image.network(
        widget.initialImageUrl!,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorWidget(),
      );
    }
    return _buildErrorWidget();
  }

  Widget _buildErrorWidget() {
    return const Center(child: Icon(Icons.broken_image, size: 50, color: Colors.grey));
  }

  Widget _buildUploadPrompt(ThemeData theme) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(Icons.cloud_upload_outlined, size: 48, color: theme.primaryColor),
        const SizedBox(height: TSizes.spaceBtwItems),
        Text(
          'Click to upload',
          style: theme.textTheme.titleMedium?.copyWith(color: theme.primaryColor, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: TSizes.xs),
        Text('or drag and drop', style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor)),
        const SizedBox(height: TSizes.xs),
        Text('Max ${widget.maxFileSizeMB}MB', style: theme.textTheme.bodySmall?.copyWith(color: theme.hintColor)),
      ],
    );
  }

  Future<void> _pickImage() async {
    try {
      setState(() {
        _errorMessage = null;
      });

      final XFile? file = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (file != null) {
        // Validate file size
        final fileSize = await file.length();
        final fileSizeMB = fileSize / (1024 * 1024);

        if (fileSizeMB > widget.maxFileSizeMB) {
          setState(() {
            _errorMessage = 'File size must be less than ${widget.maxFileSizeMB}MB';
          });
          return;
        }

        // Validate file format
        final extension = file.path.split('.').last.toLowerCase();
        if (!widget.acceptedFormats.contains(extension)) {
          setState(() {
            _errorMessage = 'Accepted formats: ${widget.acceptedFormats.join(', ')}';
          });
          return;
        }

        setState(() {
          _selectedFile = file;
          _isUploading = true;
          _uploadProgress = 0.0;
        });

        // Simulate upload progress
        for (int i = 0; i <= 100; i += 10) {
          await Future.delayed(const Duration(milliseconds: 50));
          setState(() {
            _uploadProgress = i / 100;
          });
        }

        setState(() {
          _isUploading = false;
        });

        widget.onImageSelected(file);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to pick image: $e';
        _isUploading = false;
      });
    }
  }
}
