import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/account/screens/all_accounts/responsive_screens/accounts_desktop.dart';
import 'package:alloy/features/account/screens/all_accounts/responsive_screens/accounts_mobile.dart';
import 'package:alloy/features/account/screens/all_accounts/responsive_screens/accounts_tablet.dart';
import 'package:flutter/material.dart';

class AccountsScreen extends StatelessWidget {
  const AccountsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(desktop: AccountsDesktop(), tablet: AccountsTablet(), mobile: AccountsMobile());
  }
}
