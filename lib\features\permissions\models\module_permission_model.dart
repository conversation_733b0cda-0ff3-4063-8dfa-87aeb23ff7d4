import 'package:alloy/utils/constants/enums.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Enum defining all application modules
enum AppModule {
  dashboard('Dashboard', 'Main dashboard and analytics'),
  accounts('Accounts', 'Customer and vendor management'),
  contacts('Contacts', 'Contact management'),
  deals('Deals', 'Quotations and deal management'),
  products('Products', 'Product catalog management'),
  categories('Categories', 'Product category management'),
  orders('Orders', 'Order processing and management'),
  inventory('Inventory', 'Stock and inventory management'),
  reports('Reports', 'Business reports and analytics'),
  settings('Settings', 'System configuration'),
  users('Users', 'User management'),
  humanResources('Human Resources', 'HR and employee management'),
  media('Media', 'Media and file management'),
  banners('Banners', 'Banner and promotional content'),
  brands('Brands', 'Brand management'),
  workers('Workers', 'Factory worker management'),
  profile('Profile', 'User profile management');

  const AppModule(this.displayName, this.description);
  final String displayName;
  final String description;
}

/// Enum defining permission types for each module
enum PermissionType {
  view('View', 'Can view/read data'),
  create('Create', 'Can create new records'),
  edit('Edit', 'Can modify existing records'),
  delete('Delete', 'Can delete records'),
  approve('Approve', 'Can approve/reject items'),
  export('Export', 'Can export data'),
  import('Import', 'Can import data'),
  manage('Manage', 'Full management access');

  const PermissionType(this.displayName, this.description);
  final String displayName;
  final String description;
}

/// Model representing a specific permission for a module
class ModulePermission {
  final AppModule module;
  final PermissionType permission;
  final bool granted;

  const ModulePermission({required this.module, required this.permission, required this.granted});

  /// Convert to map for Firestore
  Map<String, dynamic> toMap() {
    return {'module': module.name, 'permission': permission.name, 'granted': granted};
  }

  /// Create from map
  factory ModulePermission.fromMap(Map<String, dynamic> map) {
    return ModulePermission(
      module: AppModule.values.firstWhere((e) => e.name == map['module'], orElse: () => AppModule.profile),
      permission: PermissionType.values.firstWhere(
        (e) => e.name == map['permission'],
        orElse: () => PermissionType.view,
      ),
      granted: map['granted'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ModulePermission && other.module == module && other.permission == permission;
  }

  @override
  int get hashCode => module.hashCode ^ permission.hashCode;

  @override
  String toString() {
    return 'ModulePermission(module: $module, permission: $permission, granted: $granted)';
  }
}

/// Model representing user permissions across all modules
class UserPermissions {
  final String userId;
  final List<ModulePermission> permissions;
  final List<String> departments; // Departments user has access to
  final DateTime? createdAt;
  final DateTime? updatedAt;

  const UserPermissions({
    required this.userId,
    required this.permissions,
    this.departments = const [],
    this.createdAt,
    this.updatedAt,
  });

  /// Convert to map for Firestore
  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'permissions': permissions.map((p) => p.toMap()).toList(),
      'departments': departments,
      'createdAt': createdAt?.millisecondsSinceEpoch,
      'updatedAt': updatedAt?.millisecondsSinceEpoch,
    };
  }

  /// Create from map
  factory UserPermissions.fromMap(Map<String, dynamic> map) {
    return UserPermissions(
      userId: map['userId'] ?? '',
      permissions:
          (map['permissions'] as List<dynamic>?)
              ?.map((p) => ModulePermission.fromMap(p as Map<String, dynamic>))
              .toList() ??
          [],
      departments: List<String>.from(map['departments'] ?? []),
      createdAt: map['createdAt'] != null ? DateTime.fromMillisecondsSinceEpoch(map['createdAt']) : null,
      updatedAt: map['updatedAt'] != null ? DateTime.fromMillisecondsSinceEpoch(map['updatedAt']) : null,
    );
  }

  /// Create from Firestore document
  factory UserPermissions.fromSnapshot(DocumentSnapshot snapshot) {
    final data = snapshot.data() as Map<String, dynamic>;
    return UserPermissions.fromMap(data);
  }

  /// Check if user has specific permission for a module
  bool hasPermission(AppModule module, PermissionType permission) {
    return permissions.any((p) => p.module == module && p.permission == permission && p.granted);
  }

  /// Check if user has any permission for a module
  bool hasAnyPermissionForModule(AppModule module) {
    return permissions.any((p) => p.module == module && p.granted);
  }

  /// Check if user has access to a department
  bool hasAccessToDepartment(String department) {
    return departments.contains(department);
  }

  /// Get all permissions for a specific module
  List<PermissionType> getModulePermissions(AppModule module) {
    return permissions.where((p) => p.module == module && p.granted).map((p) => p.permission).toList();
  }

  /// Copy with method for updating permissions
  UserPermissions copyWith({
    String? userId,
    List<ModulePermission>? permissions,
    List<String>? departments,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UserPermissions(
      userId: userId ?? this.userId,
      permissions: permissions ?? this.permissions,
      departments: departments ?? this.departments,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'UserPermissions(userId: $userId, permissions: ${permissions.length}, departments: $departments)';
  }
}

/// Predefined permission templates for different user roles
class PermissionTemplates {
  /// Super Admin - Full access to everything
  static List<ModulePermission> get superAdmin {
    return AppModule.values
        .expand(
          (module) => PermissionType.values.map(
            (permission) => ModulePermission(module: module, permission: permission, granted: true),
          ),
        )
        .toList();
  }

  /// Management - High-level access with some restrictions
  static List<ModulePermission> get management {
    final permissions = <ModulePermission>[];

    for (final module in AppModule.values) {
      switch (module) {
        case AppModule.settings:
        case AppModule.users:
          // Limited settings access
          permissions.addAll([
            ModulePermission(module: module, permission: PermissionType.view, granted: true),
            ModulePermission(module: module, permission: PermissionType.edit, granted: true),
          ]);
          break;
        default:
          // Full access to other modules
          permissions.addAll(
            PermissionType.values.map(
              (permission) => ModulePermission(module: module, permission: permission, granted: true),
            ),
          );
      }
    }

    return permissions;
  }

  /// Admin - Full operational access, limited system access
  static List<ModulePermission> get admin {
    final permissions = <ModulePermission>[];

    for (final module in AppModule.values) {
      switch (module) {
        case AppModule.settings:
          // View-only settings access
          permissions.add(ModulePermission(module: module, permission: PermissionType.view, granted: true));
          break;
        case AppModule.users:
          // Limited user management
          permissions.addAll([
            ModulePermission(module: module, permission: PermissionType.view, granted: true),
            ModulePermission(module: module, permission: PermissionType.edit, granted: true),
          ]);
          break;
        default:
          // Full access to operational modules
          permissions.addAll(
            PermissionType.values.map(
              (permission) => ModulePermission(module: module, permission: permission, granted: true),
            ),
          );
      }
    }

    return permissions;
  }

  /// Manager - Department-level management access
  static List<ModulePermission> get manager {
    final permissions = <ModulePermission>[];

    for (final module in AppModule.values) {
      switch (module) {
        case AppModule.settings:
        case AppModule.users:
        case AppModule.media:
        case AppModule.banners:
          // No access to system modules
          break;
        case AppModule.deals:
        case AppModule.accounts:
        case AppModule.contacts:
          // Full access to core business modules
          permissions.addAll(
            PermissionType.values.map(
              (permission) => ModulePermission(module: module, permission: permission, granted: true),
            ),
          );
          break;
        default:
          // View and edit access to other modules
          permissions.addAll([
            ModulePermission(module: module, permission: PermissionType.view, granted: true),
            ModulePermission(module: module, permission: PermissionType.edit, granted: true),
            ModulePermission(module: module, permission: PermissionType.create, granted: true),
          ]);
      }
    }

    return permissions;
  }

  /// Regular User - Basic operational access
  static List<ModulePermission> get user {
    final permissions = <ModulePermission>[];

    for (final module in AppModule.values) {
      switch (module) {
        case AppModule.dashboard:
        case AppModule.accounts:
        case AppModule.contacts:
        case AppModule.deals:
        case AppModule.products:
        case AppModule.categories:
        case AppModule.profile:
          // Basic access to core modules
          permissions.addAll([
            ModulePermission(module: module, permission: PermissionType.view, granted: true),
            ModulePermission(module: module, permission: PermissionType.create, granted: true),
            ModulePermission(module: module, permission: PermissionType.edit, granted: true),
          ]);
          break;
        case AppModule.reports:
          // View-only reports access
          permissions.add(ModulePermission(module: module, permission: PermissionType.view, granted: true));
          break;
        default:
          // No access to other modules
          break;
      }
    }

    return permissions;
  }

  /// Get template based on user role
  static List<ModulePermission> getTemplateForRole(UserRole role) {
    switch (role) {
      case UserRole.Admin:
        return admin;
      case UserRole.Manager:
        return manager;
      case UserRole.SalesUser:
        return user;
      case UserRole.ProductionReviewer:
        return _getProductionReviewerTemplate();
      case UserRole.FactoryWorker:
        return _getFactoryWorkerTemplate();
      case UserRole.Accountant:
        return _getAccountantTemplate();
    }
  }

  static List<ModulePermission> _getProductionReviewerTemplate() {
    return [
      ModulePermission(module: AppModule.dashboard, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.deals, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.deals, permission: PermissionType.edit, granted: true),
      ModulePermission(module: AppModule.products, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.orders, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.orders, permission: PermissionType.edit, granted: true),
      ModulePermission(module: AppModule.profile, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.profile, permission: PermissionType.edit, granted: true),
    ];
  }

  static List<ModulePermission> _getFactoryWorkerTemplate() {
    return [
      ModulePermission(module: AppModule.dashboard, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.orders, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.products, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.profile, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.profile, permission: PermissionType.edit, granted: true),
    ];
  }

  static List<ModulePermission> _getAccountantTemplate() {
    return [
      ModulePermission(module: AppModule.dashboard, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.deals, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.accounts, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.orders, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.reports, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.reports, permission: PermissionType.export, granted: true),
      ModulePermission(module: AppModule.profile, permission: PermissionType.view, granted: true),
      ModulePermission(module: AppModule.profile, permission: PermissionType.edit, granted: true),
    ];
  }
}
