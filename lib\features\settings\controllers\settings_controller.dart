import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart'; // For image picking

import '../../../data/repositories/storage_repository.dart';
import '../../../utils/helpers/network_manager.dart';
import '../../../utils/popups/full_screen_loader.dart'; // TFullScreenLoader
import '../../../utils/popups/loaders.dart';
import '../models/settings_model.dart'; // SettingsModel
import '../repository/settings_repository.dart'; // SettingsRepository

class SettingsController extends GetxController {
  static SettingsController get instance => Get.find();

  final _settingsRepository = Get.put(SettingsRepository());
  final _storageRepository = Get.put(StorageRepository()); // For image upload

  // --- Reactive State for Settings ---
  final Rx<SettingsModel> globalSettings = SettingsModel.empty().obs;
  final RxBool isLoading = true.obs;
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  // --- TextEditingControllers for Form Fields ---
  // Company Info
  late TextEditingController companyName;
  late TextEditingController companyAddress;
  late TextEditingController companyPhone;
  late TextEditingController companyEmail;
  late TextEditingController defaultCurrency;
  late TextEditingController globalTaxRatePercentage;

  // Quotation/Invoice Numbering
  late TextEditingController nextQuotationNumberPrefix;
  late TextEditingController nextQuotationNumberStart;
  late TextEditingController nextInvoiceNumberPrefix;
  late TextEditingController nextInvoiceNumberStart;
  late TextEditingController defaultQuotationValidityDays;
  late TextEditingController defaultLeadTimeDays;

  // Material Rates
  final Map<String, TextEditingController> materialRateControllers = {};
  final List<String> materialTypes = ['GI', 'HDG_45microns', 'HDG_65microns', 'HDG_85microns', 'AL', 'SS', 'GI_Epoxy', 'HDG_Epoxy'];

  // User & Access
  late RxBool allowNewUserRegistration;
  late TextEditingController defaultNewUserRole;
  late RxBool requireEmailVerification;

  // Inventory
  late TextEditingController lowStockThreshold;

  // Logo URLs (for display and temporary holding before upload)
  final RxString companyLogoUrl = ''.obs;
  final RxString appLogoUrl = ''.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeControllers();
    fetchSettings();
  }

  @override
  void onClose() {
    // Dispose all TextEditingControllers
    companyName.dispose();
    companyAddress.dispose();
    companyPhone.dispose();
    companyEmail.dispose();
    defaultCurrency.dispose();
    globalTaxRatePercentage.dispose();
    nextQuotationNumberPrefix.dispose();
    nextQuotationNumberStart.dispose();
    nextInvoiceNumberPrefix.dispose();
    nextInvoiceNumberStart.dispose();
    defaultQuotationValidityDays.dispose();
    defaultLeadTimeDays.dispose();
    materialRateControllers.forEach((key, controller) => controller.dispose());
    defaultNewUserRole.dispose();
    lowStockThreshold.dispose();
    super.onClose();
  }

  /// Initializes all TextEditingControllers with default or empty values.
  void _initializeControllers() {
    companyName = TextEditingController();
    companyAddress = TextEditingController();
    companyPhone = TextEditingController();
    companyEmail = TextEditingController();
    defaultCurrency = TextEditingController();
    globalTaxRatePercentage = TextEditingController();
    nextQuotationNumberPrefix = TextEditingController();
    nextQuotationNumberStart = TextEditingController();
    nextInvoiceNumberPrefix = TextEditingController();
    nextInvoiceNumberStart = TextEditingController();
    defaultQuotationValidityDays = TextEditingController();
    defaultLeadTimeDays = TextEditingController();

    for (var type in materialTypes) {
      materialRateControllers[type] = TextEditingController();
    }

    allowNewUserRegistration = false.obs;
    defaultNewUserRole = TextEditingController();
    requireEmailVerification = false.obs;
    lowStockThreshold = TextEditingController();
  }

  /// Populates controllers with data from a SettingsModel.
  void _populateControllers(SettingsModel settings) {
    companyName.text = settings.companyName;
    companyAddress.text = settings.companyAddress;
    companyPhone.text = settings.companyPhone;
    companyEmail.text = settings.companyEmail;
    defaultCurrency.text = settings.defaultCurrency;
    globalTaxRatePercentage.text = settings.globalTaxRatePercentage.toString();

    nextQuotationNumberPrefix.text = settings.nextQuotationNumberPrefix;
    nextQuotationNumberStart.text = settings.nextQuotationNumberStart.toString();
    nextInvoiceNumberPrefix.text = settings.nextInvoiceNumberPrefix;
    nextInvoiceNumberStart.text = settings.nextInvoiceNumberStart.toString();
    defaultQuotationValidityDays.text = settings.defaultQuotationValidityDays.toString();
    defaultLeadTimeDays.text = settings.defaultLeadTimeDays.toString();

    settings.materialRatesPerKg.forEach((key, value) {
      if (materialRateControllers.containsKey(key)) {
        materialRateControllers[key]!.text = value.toString();
      }
    });

    allowNewUserRegistration.value = settings.allowNewUserRegistration;
    defaultNewUserRole.text = settings.defaultNewUserRole;
    requireEmailVerification.value = settings.requireEmailVerification;
    lowStockThreshold.text = settings.lowStockThreshold.toString();

    companyLogoUrl.value = settings.companyLogoUrl ?? '';
    appLogoUrl.value = settings.appLogoUrl ?? '';
  }

  /// Fetches global settings from Firestore and updates the UI.
  Future<void> fetchSettings() async {
    try {
      isLoading.value = true;
      final settings = await _settingsRepository.fetchSettings();
      globalSettings.value = settings;
      _populateControllers(settings);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Failed to load settings: $e');
      // If fetching fails, still populate with defaults
      globalSettings.value = SettingsModel.empty();
      _populateControllers(SettingsModel.empty());
    } finally {
      isLoading.value = false;
    }
  }

  /// Saves updated global settings to Firestore.
  Future<void> saveSettings() async {
    try {
      TFullScreenLoader.popUpCircular();
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      if (!formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'Validation Error', message: 'Please correct the errors in the form.');
        return;
      }

      // Collect material rates from controllers
      final Map<String, double> updatedMaterialRates = {};
      for (var entry in materialRateControllers.entries) {
        updatedMaterialRates[entry.key] = double.tryParse(entry.value.text) ?? 0.0;
      }

      final updatedSettings = SettingsModel(
        id: globalSettings.value.id, // Keep the fixed ID
        companyName: companyName.text.trim(),
        companyAddress: companyAddress.text.trim(),
        companyPhone: companyPhone.text.trim(),
        companyEmail: companyEmail.text.trim(),
        defaultCurrency: defaultCurrency.text.trim(),
        globalTaxRatePercentage: double.tryParse(globalTaxRatePercentage.text) ?? 0.0,
        nextQuotationNumberPrefix: nextQuotationNumberPrefix.text.trim(),
        nextQuotationNumberStart: int.tryParse(nextQuotationNumberStart.text) ?? 0,
        nextInvoiceNumberPrefix: nextInvoiceNumberPrefix.text.trim(),
        nextInvoiceNumberStart: int.tryParse(nextInvoiceNumberStart.text) ?? 0,
        defaultQuotationValidityDays: int.tryParse(defaultQuotationValidityDays.text) ?? 0,
        defaultLeadTimeDays: int.tryParse(defaultLeadTimeDays.text) ?? 0,
        materialRatesPerKg: updatedMaterialRates,
        allowNewUserRegistration: allowNewUserRegistration.value,
        defaultNewUserRole: defaultNewUserRole.text.trim(),
        requireEmailVerification: requireEmailVerification.value,
        lowStockThreshold: int.tryParse(lowStockThreshold.text) ?? 0,
        companyLogoUrl: companyLogoUrl.value.isEmpty ? null : companyLogoUrl.value, // Save current RxString value
        appLogoUrl: appLogoUrl.value.isEmpty ? null : appLogoUrl.value, // Save current RxString value
        createdAt: globalSettings.value.createdAt, // Preserve original creation date
        updatedAt: DateTime.now(), // Set new update timestamp
      );

      await _settingsRepository.updateSettings(updatedSettings);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Success', message: 'Settings updated successfully!');
      // Re-fetch to ensure globalSettings Rx is fully updated with timestamps from Firestore
      await fetchSettings();
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  /// --- Image Upload for Logos ---
  Future<void> uploadCompanyLogo() async {
    await _pickAndUploadImage((url) => companyLogoUrl.value = url, 'company_logos');
  }

  Future<void> uploadAppLogo() async {
    await _pickAndUploadImage((url) => appLogoUrl.value = url, 'app_logos');
  }

  Future<void> _pickAndUploadImage(Function(String) onUploadSuccess, String folder) async {
    try {
      TFullScreenLoader.popUpCircular();
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true, // Get file bytes
      );

      if (result != null && result.files.single.bytes != null) {
        final filePath = 'settings/$folder/${DateTime.now().millisecondsSinceEpoch}_${result.files.single.name}';
        final imageUrl = await _storageRepository.uploadFile(
          // Changed to uploadFile
          filePath,
          result.files.single.bytes!,
        );
        onUploadSuccess(imageUrl);
        TLoaders.successSnackBar(title: 'Success', message: 'Logo uploaded successfully!');
      } else {
        TLoaders.warningSnackBar(title: 'No Image Selected', message: 'Please select an image file.');
      }
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: 'Failed to upload logo: $e');
    } finally {
      TFullScreenLoader.stopLoading();
    }
  }
}
