import 'package:alloy/features/account/models/account_model.dart';
import 'package:alloy/features/account/screens/edit_account/widgets/edit_account_form.dart';
import 'package:flutter/material.dart';

class EditAccountScreen extends StatelessWidget {
  const EditAccountScreen({super.key, required this.account});

  final AccountModel account;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Edit ${account.name}')),
      body: SingleChildScrollView(
        child: Center(child: EditAccountForm(account: account)),
      ),
    );
  }
}
