import 'package:alloy/features/brand/models/brand_category_model.dart';
import 'package:alloy/features/brand/models/brand_model.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:alloy/utils/exceptions/firebase_exceptions.dart';
import 'package:alloy/utils/exceptions/format_exceptions.dart';
import 'package:alloy/utils/exceptions/platform_exceptions.dart';

class BrandRepository extends GetxController {
  static BrandRepository get instance => Get.find();

  final _db = FirebaseFirestore.instance;

  /// Get all brands
  Future<List<BrandModel>> getAllBrands() async {
    try {
      final snapshot = await _db.collection(TTexts.brands).get();
      final list = snapshot.docs.map((document) => BrandModel.fromSnapshot(document)).toList();
      return list;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Stream all brands from Firestore
  Stream<List<BrandModel>> streamAllBrands() {
    return _db.collection('brands').snapshots().map((snapshot) {
      return snapshot.docs.map((document) => BrandModel.fromSnapshot(document)).toList();
    });
  }

  /// Get all brand categories from the BrandCategory collection
  Future<List<BrandCategoryModel>> getAllBrandCategories() async {
    try {
      final brandCategoryQuery = await _db.collection(TTexts.brandCategory).get();
      final brandCategories = brandCategoryQuery.docs.map((doc) => BrandCategoryModel.fromSnapshot(doc)).toList();
      return brandCategories;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  //Get specific brand categories from the BrandCategory collection
  Future<List<BrandCategoryModel>> getCategoriesOfSpecificBrand(String brandId) async {
    try {
      final brandCategoryQuery = await _db.collection(TTexts.brandCategory).where('brandId', isEqualTo: brandId).get();

      final brandCategories = brandCategoryQuery.docs.map((doc) => BrandCategoryModel.fromSnapshot(doc)).toList();
      return brandCategories;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  // Create Brand
  Future<String> createBrand(BrandModel newBrand) async {
    try {
      final data = await _db.collection(TTexts.brands).add(newBrand.toJson());
      return data.id;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    }
  }

  // Create Brand Category Relationship
  Future<String> createBrandCategory(BrandCategoryModel newBrandCategory) async {
    try {
      final result = await _db.collection(TTexts.brandCategory).add(newBrandCategory.toJson());
      return result.id;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    }
  }

  // Update Brand
  Future<void> updateBrand(BrandModel brandToUpdate) async {
    try {
      await _db.collection(TTexts.brands).doc(brandToUpdate.id).update(brandToUpdate.toJson());
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    }
  }

  // Delete and existing Brand document and its associated brand categories
  Future<void> deleteBrand(BrandModel brand) async {
    try {
      final batch = _db.batch();

      // Delete the brand document
      batch.delete(_db.collection('brands').doc(brand.id));

      // Find and delete brand-category mappings
      final querySnapshot = await _db.collection('brandCategories').where('brandId', isEqualTo: brand.id).get();
      for (var doc in querySnapshot.docs) {
        batch.delete(doc.reference);
      }

      await batch.commit();
    } catch (e) {
      throw 'An unexpected error occurred: $e';
    }
  }

  // Delete a Brand Category Document in the 'brandCategory' collection
  Future<void> deleteBrandCategory(String brandCategoryId) async {
    try {
      await _db.collection(TTexts.brandCategory).doc(brandCategoryId).delete();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    }
  }
}
