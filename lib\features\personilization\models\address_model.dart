import 'package:alloy/utils/formatters/formatter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class AddressModel {
  String id;
  final String name;
  final String phoneNumber;
  final String street;
  final String city;
  final String state;
  final String postalCode;
  final String country;
  final DateTime? dateTime;
  bool selectedAddress;

  AddressModel({
    required this.id,
    required this.name,
    required this.phoneNumber,
    required this.street,
    required this.city,
    required this.state,
    required this.postalCode,
    required this.country,
    this.dateTime,
    this.selectedAddress = true,
  });

  String get formattedPhoneNo => TFormatter.formatPhoneNumber(phoneNumber);

  static AddressModel empty() =>
      AddressModel(id: '', name: '', phoneNumber: '', street: '', city: '', state: '', postalCode: '', country: '');

  // --- Method to convert an AddressModel instance to a Map (for Firestore) ---
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'phoneNumber': phoneNumber,
      'street': street,
      'city': city,
      'state': state,
      'postalCode': postalCode,
      'country': country,
      'dateTime': DateTime.now(),
      'selectedAddress': selectedAddress,
    };
  }

  // --- Static Factory Constructor to create an AddressModel from a Map ---
  factory AddressModel.fromMap(Map<String, dynamic> map) {
    return AddressModel(
      id: map['id'] as String? ?? '',
      name: map['name'] as String? ?? '',
      phoneNumber: map['phoneNumber'] as String? ?? '',
      street: map['street'] as String? ?? '',
      city: map['city'] as String? ?? '',
      state: map['state'] as String? ?? '',
      postalCode: map['postalCode'] as String? ?? '',
      country: map['country'] as String? ?? '',
      dateTime: (map['dateTime'] as Timestamp).toDate(),
      selectedAddress: map['selectedAddress'] as bool? ?? false,
    );
  }

  // --- Static Factory Constructor to create an AddressModel from a DocumentSnapshot ---
  factory AddressModel.fromDocumentSnapshot(DocumentSnapshot<Map<String, dynamic>> snapshot) {
    if (snapshot.data() != null) {
      final map = snapshot.data()!;
      return AddressModel(
        id: snapshot.id, // Use document ID for the model's ID
        name: map['name'] as String? ?? '',
        phoneNumber: map['phoneNumber'] as String? ?? '',
        street: map['street'] as String? ?? '',
        city: map['city'] as String? ?? '',
        state: map['state'] as String? ?? '',
        postalCode: map['postalCode'] as String? ?? '',
        country: map['country'] as String? ?? '',
        dateTime: (map['dateTime'] as Timestamp).toDate(),
        selectedAddress: map['selectedAddress'] as bool? ?? false,
      );
    } else {
      return AddressModel.empty();
    }
  }

  @override
  String toString() {
    return '$street, $city, $state $postalCode, $country';
  }
}
