import 'package:flutter/material.dart';
import 'package:iconsax/iconsax.dart';
import '../../../../routes/routes.dart';

enum MenuType { item, collapsible }

class MenuData {
  final String route;
  final String title;
  final IconData icon;

  final MenuType type;
  final List<MenuData>? children; // For sub-menu items

  MenuData({required this.route, required this.title, required this.icon, this.type = MenuType.item, this.children}) {
    if (type == MenuType.collapsible && children == null) {
      throw ArgumentError('Collapsible menu items must have children.');
    }
    if (type == MenuType.item && children != null) {
      throw ArgumentError('Simple menu items cannot have children.');
    }
  }

  // Predefined Menu Items
  static List<MenuData> get allMenuItems {
    return [
      MenuData(route: TRoutes.dashboard, title: 'Dashboard', icon: Iconsax.home_trend_up),
      MenuData(route: TRoutes.accounts, title: 'Accounts', icon: Iconsax.profile_2user),
      MenuData(route: TRoutes.contacts, title: 'Contacts', icon: Iconsax.call),
      MenuData(route: TRoutes.orders, title: 'Orders', icon: Iconsax.box),

      // Products Sub-Menu
      MenuData(
        route: TRoutes.productsMenu, // A parent route for Products
        title: 'Products Menu',
        icon: Iconsax.tag_2, // Or an appropriate Poduct icon
        type: MenuType.collapsible,
        children: [
          MenuData(route: TRoutes.products, title: 'Products', icon: Iconsax.shopping_bag),
          MenuData(route: TRoutes.categories, title: 'Categories', icon: Iconsax.category_2),
          MenuData(route: TRoutes.brands, title: 'Brands', icon: Iconsax.dcube),
          // Add more HR sub-items as needed
        ],
      ),
      // Human Resources Sub-Menu
      MenuData(
        route: TRoutes.humanResources, // A parent route for HR
        title: 'Human Resources',
        icon: Iconsax.people, // Or an appropriate HR icon
        type: MenuType.collapsible,
        children: [
          MenuData(route: TRoutes.users, title: 'Users', icon: Iconsax.user_octagon),
          MenuData(route: TRoutes.workers, title: 'Workers', icon: Iconsax.user_square),
          // Add more HR sub-items as needed
        ],
      ),
    ];
  }

  static List<MenuData> get otherMenuItems {
    return [
      MenuData(route: TRoutes.profile, title: 'Profile', icon: Iconsax.user),
      MenuData(route: TRoutes.settings, title: 'Settings', icon: Iconsax.setting_2),
      MenuData(route: TRoutes.logout, title: 'Logout', icon: Iconsax.logout),
    ];
  }
}
