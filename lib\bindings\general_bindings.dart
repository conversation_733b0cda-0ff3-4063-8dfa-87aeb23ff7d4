import 'package:alloy/common/layouts/sidebars/sidebar_controller.dart';
import 'package:alloy/features/account/controller/account_controller.dart';
import 'package:alloy/features/account/controller/create_account_controller.dart';
import 'package:alloy/features/account/repository/account_repository.dart';
import 'package:alloy/features/address/controller/address_controller.dart';
import 'package:alloy/features/address/repository/address_repository.dart';
import 'package:alloy/features/authentication/controllers/login_controller.dart';
import 'package:alloy/features/authentication/repository/authentication_repository.dart';
import 'package:alloy/features/authentication/repository/user_repository.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/features/contact/controller/contact_controller.dart';
import 'package:alloy/features/contact/controller/create_contact_controller.dart';
import 'package:alloy/features/contact/repository/contact_repository.dart';
import 'package:alloy/features/permissions/controllers/permission_controller.dart';
import 'package:alloy/features/permissions/repository/permission_repository.dart';
import 'package:alloy/features/permissions/services/permission_service.dart';
import 'package:alloy/features/products/controller/product_controller/create_product_controller.dart';
import 'package:alloy/features/products/controller/variant_controller/product_variant_manager_controller.dart';
import 'package:alloy/features/products/repository/product_variant_repository.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:get/get.dart';

import '../features/products/controller/variant_controller/product_variant_controller.dart';

class GeneralBindings extends Bindings {
  @override
  void dependencies() {
    /// -- Core
    /// // Put AuthenticationRepository so it's available immediately for auth checks.
    Get.put(AuthenticationRepository());
    // Lazy-put other controllers and services. `fenix: true` helps them survive hot reloads.
    Get.lazyPut(() => NetworkManager(), fenix: true);

    Get.put(LoginController(), permanent: true);
    Get.lazyPut(() => UserRepository(), fenix: true);
    Get.lazyPut(() => UserController(), fenix: true);
    Get.lazyPut(() => SidebarController(), fenix: true);
    Get.lazyPut(() => AddressController(), fenix: true);
    Get.lazyPut(() => AddressRepository(), fenix: true);
    Get.lazyPut(() => AccountRepository(), fenix: true);
    Get.put(AccountController(), permanent: true);
    Get.lazyPut(() => ContactRepository(), fenix: true);
    Get.put(ContactController(), permanent: true);
    Get.lazyPut(() => CreateContactController(), fenix: true);
    Get.lazyPut(() => CreateAccountController(), fenix: true);
    Get.lazyPut(() => CreateProductController(), fenix: true);
    Get.lazyPut(() => ProductVariantController(), fenix: true);
    Get.lazyPut(() => ProductVariantRepository(), fenix: true);
    Get.lazyPut(() => ProductVariantManagerController(), fenix: true);

    /// -- Permission System
    Get.lazyPut(() => PermissionRepository(), fenix: true);
    Get.lazyPut(() => PermissionController(), fenix: true);
    Get.lazyPut(() => PermissionService(), fenix: true);

    // Get.lazyPut(() => DashboardController(), fenix: true);
    // Get.lazyPut(() => MediaController(), fenix: true);
  }
}
