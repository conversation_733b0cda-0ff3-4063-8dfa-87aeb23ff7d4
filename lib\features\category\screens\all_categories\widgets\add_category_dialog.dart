import 'package:alloy/common/widgets/forms/enhanced_text_field.dart';
import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class AddCategoryDialog {
  static void show(BuildContext context) {
    final controller = Get.find<CategoryController>();
    final formKey = GlobalKey<FormState>();
    final categoryDescriptionController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) {
        return Dialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
          child: Container(
            width: 500,
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: TColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(Iconsax.add, color: TColors.primary, size: 20),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Add New Category',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
                          ),
                          Text(
                            'Create a new product category',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: TColors.darkGrey),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(Iconsax.close_circle, color: TColors.darkGrey),
                      style: IconButton.styleFrom(
                        backgroundColor: TColors.grey.withValues(alpha: 0.1),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Form
                Form(
                  key: formKey,
                  child: Column(
                    children: [
                      TEnhancedTextField(
                        controller: controller.categoryName,
                        labelText: 'Category Name',
                        hintText: 'Enter category name',
                        prefixIcon: Iconsax.category,
                        validator: (value) => TValidator.validateEmptyText('Category Name', value),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),
                      TEnhancedTextField(
                        controller: categoryDescriptionController,
                        labelText: 'Description (Optional)',
                        hintText: 'Enter category description',
                        prefixIcon: Iconsax.document_text,
                        maxLines: 3,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Actions
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () => Get.back(),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                      child: const Text('Cancel'),
                    ),
                    const SizedBox(width: 12),
                    ElevatedButton(
                      onPressed: () {
                        if (formKey.currentState!.validate()) {
                          controller.createCategory(categoryDescriptionController.text);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: TColors.primary,
                        foregroundColor: TColors.white,
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
                      ),
                      child: const Text('Create Category'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
