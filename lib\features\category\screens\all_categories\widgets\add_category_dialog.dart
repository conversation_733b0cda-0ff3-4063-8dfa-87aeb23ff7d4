import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddCategoryDialog {
  static void show(BuildContext context) {
    final controller = Get.find<CategoryController>();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Add New Category'),
          content: Form(
            key: formKey,
            child: TextFormField(
              controller: controller.categoryName,
              validator: (value) => TValidator.validateEmptyText('Category Name', value),
              decoration: const InputDecoration(labelText: 'Category Name'),
            ),
          ),
          actions: [
            TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
            ElevatedButton(
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  controller.createCategory();
                }
              },
              child: const Text('Save'),
            ),
          ],
        );
      },
    );
  }
}
