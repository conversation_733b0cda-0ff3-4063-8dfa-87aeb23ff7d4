import 'package:get/get.dart';

import '../../../data/abstract/base_data_table_controller.dart';
import '../models/address_model.dart';
import '../repository/address_repository.dart'; // AddressModel

class AddressController extends TBaseController<AddressModel> {
  static AddressController get instance => Get.find();

  final AddressRepository _addressRepository = AddressRepository.instance;

  // Reactive variable to hold the account ID for which addresses are being displayed
  final RxString currentAccountId = ''.obs;

  /// Sets the account ID and triggers fetching of addresses by re-listening to the stream.
  void loadAddressesForAccount(String accountId) {
    if (currentAccountId.value != accountId) {
      currentAccountId.value = accountId;
      listenToStream(); // Re-bind the stream for the new account ID
    }
  }

  @override
  Future<List<AddressModel>> fetchItems() async {
    // This method is not primarily used with the streaming approach.
    return [];
  }

  @override
  Stream<List<AddressModel>> streamItems() {
    if (currentAccountId.value.isEmpty) {
      return Stream.value([]); // Return empty list if no account ID is set
    }
    // Stream addresses for the current account ID
    return _addressRepository.streamAddressesForAccount(currentAccountId.value).distinct();
  }

  @override
  bool containsSearchQuery(AddressModel item, String searchText) {
    final searchLower = searchText.toLowerCase();
    // Search across various address fields
    return item.addressLine1.toLowerCase().contains(searchLower) ||
        (item.addressLine2?.toLowerCase().contains(searchLower) ?? false) ||
        (item.street?.toLowerCase().contains(searchLower) ?? false) ||
        item.city.toLowerCase().contains(searchLower) ||
        (item.state?.toLowerCase().contains(searchLower) ?? false) ||
        (item.zipCode?.toLowerCase().contains(searchLower) ?? false) ||
        item.country.toLowerCase().contains(searchLower) ||
        item.type.toLowerCase().contains(searchLower) ||
        (item.notes?.toLowerCase().contains(searchLower) ?? false);
  }

  @override
  Future<void> deleteItem(AddressModel item) {
    // Ensure the address is deleted from Firestore
    return _addressRepository.deleteAddress(item.id);
  }

  @override
  Comparable getComparableProperty(AddressModel item, int columnIndex) {
    // Implement sorting logic for AddressModel properties
    switch (columnIndex) {
      case 0:
        return item.addressLine1.toLowerCase(); // Address Line 1
      case 1:
        return item.city.toLowerCase(); // City
      case 2:
        return item.state?.toLowerCase() ?? ''; // State/Emirates
      case 3:
        return item.country.toLowerCase(); // Country
      case 4:
        return item.type.toLowerCase(); // Type (Shipping, Billing etc.)
      case 5:
        return item.isDefault ? 0 : 1; // Default status (0 for true, 1 for false to sort true first)
      default:
        return ''; // Fallback for undefined columns
    }
  }

  /// Sets a specific address as the default for its type for the current account.
  Future<void> setDefaultAddress(String addressId, String addressType) async {
    if (currentAccountId.value.isEmpty) {
      // Handle error: no account selected
      return;
    }
    await _addressRepository.setDefaultAddress(currentAccountId.value, addressId, addressType);
  }
}
