import 'package:alloy/common/widgets/data_table/paginated_data_table.dart';
import 'package:alloy/features/dashboard/table/table_source.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';

class DashboardOrderTable extends StatelessWidget {
  const DashboardOrderTable({super.key});

  @override
  Widget build(BuildContext context) {
    return TPaginatedDataTable(
      minWidth: 700,
      tableHeight: 500,
      dataRowHeight: TSizes.xl * 1.2,

      columns: [
        DataColumn(label: Text('Order ID')),
        DataColumn(label: Text('Order Date')),
        DataColumn(label: Text('Items')),
        DataColumn(label: Text('Status')),
        DataColumn(label: Text('Amount')),
      ],
      source: OrderRows(),
    );
  }
}
