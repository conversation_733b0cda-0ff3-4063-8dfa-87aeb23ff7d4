import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/features/products/controller/variant_controller/product_variant_manager_controller.dart';
import 'package:alloy/features/products/models/product_model.dart';

/// Visual drag-and-drop variant builder with live preview
class VisualVariantBuilder extends StatelessWidget {
  const VisualVariantBuilder({super.key, required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProductVariantManagerController>();

    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(Icons.auto_awesome, color: Theme.of(context).primaryColor),
              const SizedBox(width: TSizes.spaceBtwItems),
              Text(
                'Visual Variant Builder',
                style: Theme.of(
                  context,
                ).textTheme.headlineSmall?.copyWith(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),
          Text(
            'Drag attributes to create custom variant combinations',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
          ),
          const SizedBox(height: TSizes.spaceBtwSections),

          // Main Builder Area
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left Panel: Attribute Pools
                Expanded(flex: 2, child: _buildAttributePools(context, controller)),
                const SizedBox(width: TSizes.spaceBtwSections),

                // Right Panel: Variant Preview & Generation
                Expanded(flex: 3, child: _buildVariantPreview(context, controller)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAttributePools(BuildContext context, ProductVariantManagerController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Attribute Pools', style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: TSizes.spaceBtwItems),

        Expanded(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Materials Pool
                _buildAttributePool(
                  context,
                  title: 'Materials',
                  icon: Icons.layers,
                  color: Colors.blue,
                  options: controller.materialOptions,
                  selectedOptions: controller.selectedMaterials,
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Thicknesses Pool
                _buildAttributePool(
                  context,
                  title: 'Thicknesses',
                  icon: Icons.straighten,
                  color: Colors.green,
                  options: controller.thicknessOptions,
                  selectedOptions: controller.selectedThicknesses,
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Finishes Pool
                _buildAttributePool(
                  context,
                  title: 'Finishes',
                  icon: Icons.brush,
                  color: Colors.orange,
                  options: controller.finishOptions,
                  selectedOptions: controller.selectedFinishes,
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Lengths Pool
                _buildAttributePool(
                  context,
                  title: 'Lengths',
                  icon: Icons.straighten,
                  color: Colors.purple,
                  options: controller.lengthOptions,
                  selectedOptions: controller.selectedLengths,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAttributePool(
    BuildContext context, {
    required String title,
    required IconData icon,
    required Color color,
    required List<String> options,
    required RxList<String> selectedOptions,
  }) {
    return TRoundedContainer(
      backgroundColor: color.withValues(alpha: 0.1),
      borderColor: color.withValues(alpha: 0.3),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Pool Header
          Container(
            padding: const EdgeInsets.all(TSizes.sm),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(TSizes.cardRadiusMd),
                topRight: Radius.circular(TSizes.cardRadiusMd),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: TSizes.spaceBtwItems / 2),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(color: color, fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Obx(
                  () => Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(12)),
                    child: Text(
                      '${selectedOptions.length}',
                      style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Draggable Attribute Items
          Padding(
            padding: const EdgeInsets.all(TSizes.sm),
            child: Wrap(
              spacing: 6,
              runSpacing: 6,
              children: options
                  .map(
                    (option) => _buildDraggableAttribute(
                      context,
                      option: option,
                      color: color,
                      selectedOptions: selectedOptions,
                    ),
                  )
                  .toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDraggableAttribute(
    BuildContext context, {
    required String option,
    required Color color,
    required RxList<String> selectedOptions,
  }) {
    return Obx(() {
      final isSelected = selectedOptions.contains(option);

      return Draggable<String>(
        data: option,
        feedback: Material(
          elevation: 4,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(color: color, borderRadius: BorderRadius.circular(20)),
            child: Text(
              option,
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
        ),
        childWhenDragging: Opacity(opacity: 0.5, child: _buildAttributeChip(option, color, isSelected)),
        child: GestureDetector(
          onTap: () {
            if (isSelected) {
              selectedOptions.remove(option);
            } else {
              selectedOptions.add(option);
            }
          },
          child: _buildAttributeChip(option, color, isSelected),
        ),
      );
    });
  }

  Widget _buildAttributeChip(String option, Color color, bool isSelected) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isSelected ? color : Colors.white,
        border: Border.all(color: color),
        borderRadius: BorderRadius.circular(20),
        boxShadow: isSelected
            ? [BoxShadow(color: color.withValues(alpha: 0.3), blurRadius: 4, offset: const Offset(0, 2))]
            : null,
      ),
      child: Text(
        option,
        style: TextStyle(
          color: isSelected ? Colors.white : color,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildVariantPreview(BuildContext context, ProductVariantManagerController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Variant Preview', style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: TSizes.spaceBtwItems),

        Expanded(
          child: TRoundedContainer(
            backgroundColor: Colors.grey[50] ?? Colors.grey.shade50,
            child: Column(
              children: [
                // Preview Header with Stats
                _buildPreviewHeader(context, controller),
                const Divider(),

                // Drop Zone for Quick Selection
                _buildDropZone(context, controller),
                const Divider(),

                // Combination Matrix
                Expanded(child: _buildCombinationMatrix(context, controller)),

                // Generation Controls
                _buildGenerationControls(context, controller),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDropZone(BuildContext context, ProductVariantManagerController controller) {
    return Container(
      margin: const EdgeInsets.all(TSizes.md),
      child: DragTarget<String>(
        onAcceptWithDetails: (details) {
          // Handle dropped attribute
          final attribute = details.data;
          // Add logic to determine which category this attribute belongs to
          // and add it to the appropriate selected list
        },
        builder: (context, candidateData, rejectedData) {
          final isActive = candidateData.isNotEmpty;

          return AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            height: 80,
            decoration: BoxDecoration(
              color: isActive ? Colors.blue.withValues(alpha: 0.1) : Colors.grey[100],
              border: Border.all(color: isActive ? Colors.blue : Colors.grey[300]!, width: 2),
              borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    isActive ? Icons.add_circle : Icons.drag_indicator,
                    color: isActive ? Colors.blue : Colors.grey[500],
                    size: 32,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    isActive ? 'Drop to add attribute' : 'Drag attributes here for quick selection',
                    style: TextStyle(
                      color: isActive ? Colors.blue : Colors.grey[600],
                      fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPreviewHeader(BuildContext context, ProductVariantManagerController controller) {
    return Padding(
      padding: const EdgeInsets.all(TSizes.md),
      child: Obx(() {
        final totalCombinations = _calculateTotalCombinations(controller);

        return Row(
          children: [
            _buildStatCard(
              context,
              icon: Icons.auto_awesome,
              label: 'Total Combinations',
              value: totalCombinations.toString(),
              color: Colors.blue,
            ),
            const SizedBox(width: TSizes.spaceBtwItems),
            _buildStatCard(
              context,
              icon: Icons.check_circle,
              label: 'Selected',
              value: '0', // TODO: Implement selection tracking
              color: Colors.green,
            ),
          ],
        );
      }),
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(TSizes.sm),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(TSizes.cardRadiusSm),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: TSizes.spaceBtwItems / 2),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: color, fontWeight: FontWeight.bold),
            ),
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(color: color),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCombinationMatrix(BuildContext context, ProductVariantManagerController controller) {
    return Obx(() {
      final combinations = _generateCombinationPreviews(controller);

      if (combinations.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.drag_indicator, size: 64, color: Colors.grey[400]),
              const SizedBox(height: TSizes.spaceBtwItems),
              Text(
                'Drag attributes from the left panel\nto create variant combinations',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
      }

      return GridView.builder(
        padding: const EdgeInsets.all(TSizes.sm),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          childAspectRatio: 1.2,
          crossAxisSpacing: TSizes.spaceBtwItems,
          mainAxisSpacing: TSizes.spaceBtwItems,
        ),
        itemCount: combinations.length,
        itemBuilder: (context, index) {
          final combination = combinations[index];
          return _buildCombinationCard(context, combination);
        },
      );
    });
  }

  Widget _buildCombinationCard(BuildContext context, Map<String, String> combination) {
    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(TSizes.cardRadiusSm),
        border: Border.all(color: Colors.grey[300]!),
        boxShadow: [BoxShadow(color: Colors.grey.withValues(alpha: 0.1), blurRadius: 2, offset: const Offset(0, 1))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Combination attributes
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: combination.entries.map((entry) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 2),
                  child: Row(
                    children: [
                      Text(
                        '${entry.key}:',
                        style: Theme.of(
                          context,
                        ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold, color: Colors.grey[600]),
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          entry.value,
                          style: Theme.of(context).textTheme.bodySmall,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
            ),
          ),

          // Selection checkbox
          Row(
            children: [
              Checkbox(
                value: false, // TODO: Implement selection state
                onChanged: (value) {
                  // TODO: Implement selection logic
                },
                materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              Expanded(child: Text('Include', style: Theme.of(context).textTheme.bodySmall)),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildGenerationControls(BuildContext context, ProductVariantManagerController controller) {
    return Container(
      padding: const EdgeInsets.all(TSizes.md),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(TSizes.cardRadiusMd),
          bottomRight: Radius.circular(TSizes.cardRadiusMd),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: () {
                // Clear all selections
                controller.selectedMaterials.clear();
                controller.selectedThicknesses.clear();
                controller.selectedFinishes.clear();
                controller.selectedLengths.clear();
              },
              icon: const Icon(Icons.clear_all),
              label: const Text('Clear All'),
            ),
          ),
          const SizedBox(width: TSizes.spaceBtwItems),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: () {
                // Generate selected variants
                controller.generateAndSaveVariants(singleProduct: product);
              },
              icon: const Icon(Icons.auto_awesome),
              label: const Text('Generate Variants'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                foregroundColor: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  int _calculateTotalCombinations(ProductVariantManagerController controller) {
    final materials = controller.selectedMaterials.length;
    final thicknesses = controller.selectedThicknesses.length;
    final finishes = controller.selectedFinishes.length;
    final lengths = controller.selectedLengths.length;

    if (materials == 0 || thicknesses == 0 || finishes == 0 || lengths == 0) {
      return 0;
    }

    return materials * thicknesses * finishes * lengths;
  }

  List<Map<String, String>> _generateCombinationPreviews(ProductVariantManagerController controller) {
    final combinations = <Map<String, String>>[];

    for (final material in controller.selectedMaterials) {
      for (final thickness in controller.selectedThicknesses) {
        for (final finish in controller.selectedFinishes) {
          for (final length in controller.selectedLengths) {
            combinations.add({'Material': material, 'Thickness': thickness, 'Finish': finish, 'Length': length});
          }
        }
      }
    }

    return combinations;
  }
}
