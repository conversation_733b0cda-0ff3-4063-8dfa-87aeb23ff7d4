import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../controllers/deal_controller.dart';
import '../../../models/deal_model.dart';

class SalesPipelineWidget extends StatelessWidget {
  const SalesPipelineWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealController>();

    return Obx(() {
      final deals = controller.filteredItems;
      final pipelineData = _calculatePipelineData(deals);

      return Container(
        padding: const EdgeInsets.all(TSizes.md),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
          border: Border.all(color: Colors.grey.withValues(alpha: 0.2)),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Icon(Iconsax.chart, color: TColors.primary, size: 20),
                const SizedBox(width: TSizes.sm),
                Text(
                  'Sales Pipeline',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
                  decoration: BoxDecoration(
                    color: TColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  ),
                  child: Text(
                    '${deals.length} Deals',
                    style: Theme.of(
                      context,
                    ).textTheme.labelSmall?.copyWith(color: TColors.primary, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwItems),

            // Pipeline Stages
            Row(
              children: pipelineData.entries.map((entry) {
                final status = entry.key;
                final data = entry.value;
                final isLast = entry.key == pipelineData.keys.last;

                return Expanded(
                  child: Row(
                    children: [
                      Expanded(
                        child: _buildPipelineStage(
                          context,
                          status,
                          data['count'] as int,
                          data['value'] as double,
                          data['color'] as Color,
                        ),
                      ),
                      if (!isLast) ...[
                        const SizedBox(width: TSizes.xs),
                        Icon(Iconsax.arrow_right_3, size: 16, color: Colors.grey[400]),
                        const SizedBox(width: TSizes.xs),
                      ],
                    ],
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: TSizes.spaceBtwItems),

            // Summary Stats
            Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    context,
                    'Total Value',
                    'AED ${_calculateTotalValue(deals).toStringAsFixed(0)}',
                    Iconsax.money,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: TSizes.sm),
                Expanded(
                  child: _buildSummaryCard(
                    context,
                    'Avg Deal Size',
                    'AED ${_calculateAverageDealSize(deals).toStringAsFixed(0)}',
                    Iconsax.chart_1,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: TSizes.sm),
                Expanded(
                  child: _buildSummaryCard(
                    context,
                    'Conversion Rate',
                    '${_calculateConversionRate(deals).toStringAsFixed(1)}%',
                    Iconsax.percentage_circle,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    });
  }

  Widget _buildPipelineStage(BuildContext context, DealStatus status, int count, double value, Color color) {
    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(_getStatusIcon(status), color: color, size: 20),
          const SizedBox(height: TSizes.xs),
          Text(
            _getStatusDisplayName(status),
            style: Theme.of(context).textTheme.labelSmall?.copyWith(fontWeight: FontWeight.w600, color: color),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: TSizes.xs),
          Text(
            '$count',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: color),
          ),
          Text(
            'AED ${value.toStringAsFixed(0)}',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(BuildContext context, String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(height: TSizes.xs),
          Text(
            title,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.bold, color: color),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Map<DealStatus, Map<String, dynamic>> _calculatePipelineData(List<DealModel> deals) {
    final pipelineStages = [
      DealStatus.draft,
      DealStatus.pendingApproval,
      DealStatus.approved,
      DealStatus.clientApproved,
      DealStatus.closed,
    ];

    final Map<DealStatus, Map<String, dynamic>> data = {};

    for (final status in pipelineStages) {
      final statusDeals = deals.where((deal) => deal.status == status).toList();
      final totalValue = statusDeals.fold<double>(0, (sum, deal) => sum + deal.grandTotalAmount);

      data[status] = {'count': statusDeals.length, 'value': totalValue, 'color': _getStatusColor(status)};
    }

    return data;
  }

  double _calculateTotalValue(List<DealModel> deals) {
    return deals.fold<double>(0, (sum, deal) => sum + deal.grandTotalAmount);
  }

  double _calculateAverageDealSize(List<DealModel> deals) {
    if (deals.isEmpty) return 0;
    return _calculateTotalValue(deals) / deals.length;
  }

  double _calculateConversionRate(List<DealModel> deals) {
    if (deals.isEmpty) return 0;
    final closedDeals = deals
        .where((deal) => deal.status == DealStatus.closed || deal.status == DealStatus.clientApproved)
        .length;
    return (closedDeals / deals.length) * 100;
  }

  IconData _getStatusIcon(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Iconsax.document_text;
      case DealStatus.pendingApproval:
        return Iconsax.clock;
      case DealStatus.approved:
        return Iconsax.tick_circle;
      case DealStatus.clientApproved:
        return Iconsax.medal_star;
      case DealStatus.closed:
        return Iconsax.archive_tick;
      case DealStatus.rejected:
        return Iconsax.close_circle;
      case DealStatus.clientDeclined:
        return Iconsax.dislike;
      case DealStatus.unlockRequested:
        return Iconsax.unlock;
      case DealStatus.superseded:
        return Iconsax.refresh;
      case DealStatus.quotationGenerated:
        return Iconsax.document_download;
    }
  }

  String _getStatusDisplayName(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return 'Draft';
      case DealStatus.pendingApproval:
        return 'Pending';
      case DealStatus.approved:
        return 'Approved';
      case DealStatus.clientApproved:
        return 'Client Approved';
      case DealStatus.closed:
        return 'Closed';
      case DealStatus.rejected:
        return 'Rejected';
      case DealStatus.clientDeclined:
        return 'Client Declined';
      case DealStatus.unlockRequested:
        return 'Unlock Requested';
      case DealStatus.superseded:
        return 'Superseded';
      case DealStatus.quotationGenerated:
        return 'Quotation Generated';
    }
  }

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.draft:
        return Colors.grey;
      case DealStatus.pendingApproval:
        return Colors.orange;
      case DealStatus.approved:
        return Colors.green;
      case DealStatus.clientApproved:
        return Colors.teal;
      case DealStatus.closed:
        return Colors.blue;
      case DealStatus.rejected:
        return Colors.red;
      case DealStatus.clientDeclined:
        return Colors.red.shade700;
      case DealStatus.unlockRequested:
        return Colors.purple;
      case DealStatus.superseded:
        return Colors.brown;
      case DealStatus.quotationGenerated:
        return Colors.indigo;
    }
  }
}
