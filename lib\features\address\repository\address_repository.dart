import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart'; // For PlatformException
import 'package:get/get.dart';
import '../../../utils/constants/text_strings.dart'; // TTexts (for collection names)
import '../../../utils/exceptions/firebase_exceptions.dart';
import '../../../utils/exceptions/platform_exceptions.dart';
import '../models/address_model.dart'; // AddressModel
import '../../account/repository/account_repository.dart'; // NEW: Import AccountRepository

/// Repository for managing Address data in Firestore.
class AddressRepository extends GetxController {
  static AddressRepository get instance => Get.find();

  final FirebaseFirestore _db = FirebaseFirestore.instance;
  final AccountRepository _accountRepository = Get.find<AccountRepository>(); // NEW: Get instance of AccountRepository

  // Main collection reference for Addresses
  final CollectionReference<AddressModel> _addressesCollection = FirebaseFirestore.instance
      .collection(TTexts.addresses)
      .withConverter<AddressModel>(
        fromFirestore: (snapshot, options) => AddressModel.fromSnapshot(snapshot),
        toFirestore: (address, options) => address.toJson(),
      );

  /// Creates a new Address document in Firestore.
  Future<String> createAddress(AddressModel address) async {
    try {
      final docRef = await _addressesCollection.add(address);
      // If this is set as a default billing address, update the parent account
      if (address.isDefault && address.type == 'Billing') {
        await _updateAccountBillingLocation(address.accountId, address.country, address.state);
      }
      return docRef.id;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while creating address: $e';
    }
  }

  /// Fetches a single Address by its ID.
  Future<AddressModel> getAddressById(String addressId) async {
    try {
      final documentSnapshot = await _addressesCollection.doc(addressId).get();
      if (documentSnapshot.exists) {
        return documentSnapshot.data()!;
      } else {
        return AddressModel.empty(); // Return empty model if not found
      }
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while fetching address: $e';
    }
  }

  /// Streams all Addresses for a specific Account.
  /// This is crucial for displaying addresses linked to an account.
  Stream<List<AddressModel>> streamAddressesForAccount(String accountId) {
    return _addressesCollection
        .where('accountId', isEqualTo: accountId)
        .orderBy('isDefault', descending: true) // Show default addresses first
        .orderBy('type') // Then by type
        .snapshots()
        .map((snapshot) => snapshot.docs.map((doc) => doc.data()).toList());
  }

  /// Updates an existing Address document in Firestore.
  Future<void> updateAddress(AddressModel address) async {
    try {
      await _addressesCollection.doc(address.id).update(address.toJson());
      // If this is set as a default billing address, update the parent account
      if (address.isDefault && address.type == 'Billing') {
        await _updateAccountBillingLocation(address.accountId, address.country, address.state);
      }
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while updating address: $e';
    }
  }

  /// Deletes an Address document from Firestore.
  Future<void> deleteAddress(String addressId) async {
    try {
      // Before deleting, check if it's a default billing address for an account
      final addressToDelete = await getAddressById(addressId);
      await _addressesCollection.doc(addressId).delete();

      // If the deleted address was a default billing address, clear the account's fields
      if (addressToDelete.isDefault && addressToDelete.type == 'Billing') {
        await _updateAccountBillingLocation(addressToDelete.accountId, null, null);
      }
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while deleting address: $e';
    }
  }

  /// Sets a given address as default for its type and unsets any other default addresses
  /// of the same type for the same account.
  /// If the type is 'Billing', it also updates the AccountModel's country and emirates.
  Future<void> setDefaultAddress(String accountId, String addressId, String addressType) async {
    try {
      final batch = _db.batch();

      // 1. Unset current default address of the same type for this account
      final querySnapshot = await _addressesCollection
          .where('accountId', isEqualTo: accountId)
          .where('type', isEqualTo: addressType)
          .where('isDefault', isEqualTo: true)
          .get();

      for (var doc in querySnapshot.docs) {
        batch.update(doc.reference, {'isDefault': false});
      }

      // 2. Set the new address as default
      final newDefaultAddressRef = _addressesCollection.doc(addressId);
      batch.update(newDefaultAddressRef, {'isDefault': true});

      await batch.commit();

      // 3. If it's a billing address, update the AccountModel's denormalized fields
      if (addressType == 'Billing') {
        final newDefaultAddress = await getAddressById(addressId); // Fetch the newly set default address
        await _updateAccountBillingLocation(accountId, newDefaultAddress.country, newDefaultAddress.state);
      }
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong while setting default address: $e';
    }
  }

  /// Helper method to update the AccountModel's denormalized billing location.
  Future<void> _updateAccountBillingLocation(String accountId, String? country, String? emirates) async {
    try {
      await _accountRepository.updateAccountFields(accountId, {'country': country, 'emirates': emirates});
    } catch (e) {
      print('Error updating account billing location for account $accountId: $e');
      // Decide if you want to rethrow or just log this error.
      // For denormalization, a log might be sufficient as the primary address data is intact.
    }
  }
}
