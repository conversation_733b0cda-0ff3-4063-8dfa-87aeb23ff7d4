/* --
      LIST OF Enums
      They cannot be created inside a class.
-- */

enum BusinessType { customer, vendor, partner, other }

enum PriorityLevel { high, medium, low }

enum AccountStatus { lead, active, inactive }

enum AppRole { admin, user }

enum TransactionType { buy, sell }

/// User Roles for access control
enum UserRole {
  SalesUser, // Can create/manage their own quotations/deals
  Manager, // Can view all, approve others' quotations/deals
  Admin, // Full access to all modules and settings
  ProductionReviewer, // Can review deals for production, set final material/thickness
  FactoryWorker, // Can view production orders relevant to them
  Accountant, // Can view financial reports, invoices
  // Add more roles as needed
}

/// Status of a Quotation
enum DealStatus {
  Draft, // Still being edited, not submitted
  PendingApproval, // Submitted for approval by manager/admin
  Approved, // Approved by manager/admin
  Rejected, // Rejected by manager/admin
  UnlockRequested, // Sales user requested to unlock an approved/rejected quote
  ClientApproved, // Client has approved the quotation (becomes a Deal)
  ClientDeclined, // <PERSON><PERSON> has declined the quotation
  Superseded, // A new version of this deal has been created (for versioning)
  Closed, // Deal is finalized (either approved by client or declined/cancelled internally)
}

/// Type of Discount applied to a Quotation
enum DiscountType {
  None, // No discount
  Percentage, // Discount applied as a percentage of total
  Value, // Discount applied as a fixed monetary value
}

enum ProductSegment {
  lengths('Lengths'),
  accessories('Accessories'),
  hardware('Hardware');

  const ProductSegment(this.name);
  final String name; // Storing a display name for the enum value
}

/// Enum for Product Status (e.g., Active, Inactive, Discontinued)
enum ProductStatus {
  active('Active'),
  inactive('Inactive'),
  discontinued('Discontinued');

  const ProductStatus(this.name);
  final String name; // Storing a display name for the enum value
}

enum ProductVisibility { published, hidden }

enum TextSizes { small, medium, large }

enum ImageType { asset, network, memory, file }

enum MediaCategory { folders, banners, brands, categories, products, users }

enum OrderStatus { pending, processing, shipped, delivered, cancelled }

enum PaymentMethods { paypal, googlePay, applePay, visa, masterCard, creditCard, paystack, razorPay, paytm }
