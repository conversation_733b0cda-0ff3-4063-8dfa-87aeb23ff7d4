import 'package:pdf/pdf.dart' show PdfPageFormat;
import 'package:pdf/widgets.dart' as pw; // For PdfPageFormat, TableColumnWidth
import 'package:get/get.dart'; // For .capitalizeFirst! used in formatters

import '../../../features/account/models/account_model.dart';
import '../../../features/contact/models/contact_model.dart';
import '../../../utils/constants/enums.dart'; // For BusinessType, AccountStatus

/// Defines a function type for custom cell formatting.
/// Takes the raw field value and the entire model object, returns the formatted value.
typedef CellFormatter = dynamic Function(dynamic value, dynamic model);

/// Configuration for a single column in a report.
class ReportColumnConfig {
  final String fieldName; // The key in the model's Map representation (e.g., 'name', 'email')
  final String headerText; // The display text for the column header
  final pw.TableColumnWidth? pdfColumnWidth; // Optional: specific width for PDF column
  final CellFormatter? formatter; // Optional: custom formatter for the cell value

  ReportColumnConfig({required this.fieldName, required this.headerText, this.pdfColumnWidth, this.formatter});
}

/// Overall configuration for a report.
class ReportConfig {
  final String title; // Title of the report
  final String filenamePrefix; // Prefix for the generated file name
  final List<ReportColumnConfig> columns; // List of column definitions
  final PdfPageFormat? pdfPageFormat; // Optional: specific page format for PDF
  final bool isNestedReport; // Flag for reports needing special nested rendering (e.g., Accounts with Contacts)

  ReportConfig({required this.title, required this.filenamePrefix, required this.columns, this.pdfPageFormat, this.isNestedReport = false});

  // --- Static Getters for Predefined Report Configurations ---

  static ReportConfig get accountsReport {
    return ReportConfig(
      title: 'Accounts Report',
      filenamePrefix: 'Accounts',
      pdfPageFormat: PdfPageFormat.a4.landscape,
      columns: [
        ReportColumnConfig(fieldName: 'name', headerText: 'Name', pdfColumnWidth: const pw.FlexColumnWidth(2)),
        ReportColumnConfig(
          fieldName: 'businessType',
          headerText: 'Business Type',
          formatter: (value, model) {
            // Assume value is 'vendor', 'customer', etc.
            // this converts string to enum //may be we can avoid this in the future
            if (value is String) {
              return BusinessType.values
                  .firstWhere(
                    (e) => e.name == value,
                    orElse: () => BusinessType.other, // Default if not found
                  )
                  .name
                  .capitalizeFirst!;
            }
            return (value as BusinessType).name.capitalizeFirst!; // Fallback if it's already an enum
          },
        ),
        ReportColumnConfig(fieldName: 'country', headerText: 'Country', pdfColumnWidth: const pw.FlexColumnWidth(1)),
        ReportColumnConfig(fieldName: 'emirates', headerText: 'Emirates', pdfColumnWidth: const pw.FlexColumnWidth(1)),
        ReportColumnConfig(fieldName: 'phone', headerText: 'Phone', pdfColumnWidth: const pw.FlexColumnWidth(1.5), formatter: (value, model) => value ?? 'N/A'),
        ReportColumnConfig(
          fieldName: 'status',
          headerText: 'Status',
          pdfColumnWidth: const pw.FlexColumnWidth(1),
          // this converts string to enum //may be we can avoid this in the future
          formatter: (value, model) {
            if (value is String) {
              // Attempt to find the enum from its name string
              return AccountStatus.values
                  .firstWhere(
                    (e) => e.name == value,
                    orElse: () => AccountStatus.active, // Provide a fallback
                  )
                  .name
                  .capitalizeFirst!;
            }
            // Fallback if it's already an enum
            return (value as AccountStatus).name.capitalizeFirst!;
          },
        ),
        ReportColumnConfig(
          fieldName: 'handlerDetails',
          headerText: 'Handlers',
          pdfColumnWidth: const pw.FlexColumnWidth(2),
          formatter: (value, model) => (model as AccountModel).handlerDetails?.map((h) => h.fullName).join(', ') ?? 'N/A',
        ),
        ReportColumnConfig(
          fieldName: 'createdAt',
          headerText: 'Created At',
          pdfColumnWidth: const pw.FlexColumnWidth(1.5),
          formatter: (value, model) => (value as DateTime?)?.toLocal().toString().split('.')[0] ?? 'N/A',
        ),
      ],
    );
  }

  static ReportConfig get contactsReport {
    return ReportConfig(
      title: 'Contacts Report',
      filenamePrefix: 'Contacts',
      pdfPageFormat: PdfPageFormat.a4.landscape,
      columns: [
        ReportColumnConfig(fieldName: 'name', headerText: 'Name', pdfColumnWidth: const pw.FlexColumnWidth(1.5)),
        ReportColumnConfig(fieldName: 'title', headerText: 'Title', pdfColumnWidth: const pw.FlexColumnWidth(0.8), formatter: (value, model) => value ?? 'N/A'),
        ReportColumnConfig(fieldName: 'email', headerText: 'Email', pdfColumnWidth: const pw.FlexColumnWidth(1.5), formatter: (value, model) => value ?? 'N/A'),
        ReportColumnConfig(fieldName: 'phone', headerText: 'Phone', pdfColumnWidth: const pw.FlexColumnWidth(1)),
        ReportColumnConfig(
          fieldName: 'landline',
          headerText: 'Landline',
          pdfColumnWidth: const pw.FlexColumnWidth(1),
          formatter: (value, model) => value ?? 'N/A',
        ),
        ReportColumnConfig(
          fieldName: 'designation',
          headerText: 'Designation',
          pdfColumnWidth: const pw.FlexColumnWidth(1),
          formatter: (value, model) => value ?? 'N/A',
        ),
        ReportColumnConfig(
          fieldName: 'active',
          headerText: 'Active',
          pdfColumnWidth: const pw.FlexColumnWidth(0.6),
          formatter: (value, model) => (value as bool) ? 'Yes' : 'No',
        ),
        ReportColumnConfig(
          fieldName: 'accountDetails',
          headerText: 'Accounts',
          pdfColumnWidth: const pw.FlexColumnWidth(2),
          formatter: (value, model) => (model as ContactModel).accountDetails?.map((a) => a.name).join(', ') ?? 'N/A',
        ),
        ReportColumnConfig(
          fieldName: 'handlerDetails',
          headerText: 'Handlers',
          pdfColumnWidth: const pw.FlexColumnWidth(2),
          formatter: (value, model) => (model as ContactModel).handlerDetails?.map((h) => h.fullName).join(', ') ?? 'N/A',
        ),
        ReportColumnConfig(
          fieldName: 'createdAt',
          headerText: 'Created At',
          pdfColumnWidth: const pw.FlexColumnWidth(1.5),
          formatter: (value, model) => (value as DateTime?)?.toLocal().toString().split('.')[0] ?? 'N/A',
        ),
      ],
    );
  }

  static ReportConfig get accountsWithContactsReport {
    return ReportConfig(
      title: 'Accounts & Contacts Association Report',
      filenamePrefix: 'Accounts_Contacts',
      pdfPageFormat: PdfPageFormat.a4.portrait,
      // ############## Mark as isNested for special PDF report rendering (sucha as combined Accounts with Contacts) ############## //
      isNestedReport: true,
      columns: [
        // These columns are primarily for Excel flattening
        ReportColumnConfig(fieldName: 'name', headerText: 'Account Name'),
        ReportColumnConfig(
          fieldName: 'businessType',
          headerText: 'Business Type',
          formatter: (value, model) {
            // Assume value is 'vendor', 'customer', etc.
            // this converts string to enum //may be we can avoid this in the future
            if (value is String) {
              return BusinessType.values
                  .firstWhere(
                    (e) => e.name == value,
                    orElse: () => BusinessType.other, // Default if not found
                  )
                  .name
                  .capitalizeFirst!;
            }
            return (value as BusinessType).name.capitalizeFirst!; // Fallback if it's already an enum
          },
        ),
        ReportColumnConfig(fieldName: 'phone', headerText: 'Account Phone', formatter: (value, model) => value ?? 'N/A'),
        // this converts string to enum //may be we can avoid this in the future
        ReportColumnConfig(
          fieldName: 'status',
          headerText: 'Status',
          pdfColumnWidth: const pw.FlexColumnWidth(1),
          formatter: (value, model) {
            if (value is String) {
              // Attempt to find the enum from its name string
              return AccountStatus.values
                  .firstWhere(
                    (e) => e.name == value,
                    orElse: () => AccountStatus.active, // Provide a fallback
                  )
                  .name
                  .capitalizeFirst!;
            }
            // Fallback if it's already an enum
            return (value as AccountStatus).name.capitalizeFirst!;
          },
        ),
        // For Excel, we define "flattened" contact fields. PDF will use its custom _buildAccountWithContactsEntry.
        ReportColumnConfig(fieldName: 'contactName', headerText: 'Contact Name'), // Placeholder, actual value filled in ExcelService
        ReportColumnConfig(fieldName: 'contactDesignation', headerText: 'Contact Designation'),
        ReportColumnConfig(fieldName: 'contactPhone', headerText: 'Contact Phone'),
        ReportColumnConfig(fieldName: 'contactEmail', headerText: 'Contact Email'),
      ],
    );
  }

  // Add more static getters for other report types (e.g., sales orders) as needed
}
