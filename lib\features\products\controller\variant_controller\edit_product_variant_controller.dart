import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../utils/constants/enums.dart';
import '../../../../utils/helpers/network_manager.dart';
import '../../../../utils/helpers/product_variant_calculator.dart';
import '../../../../utils/popups/full_screen_loader.dart';
import '../../../../utils/popups/loaders.dart';
import '../../models/product_model.dart';
import '../../models/product_variant_model.dart';
import '../../repository/product_variant_repository.dart';
import 'product_variant_controller.dart';

/// Controller for editing an existing ProductVariantModel.
class EditProductVariantController extends GetxController {
  final _variantRepository = ProductVariantRepository.instance; // Get the instance
  final ProductVariantController _productVariantController =
      Get.find<ProductVariantController>(); // To refresh variant list

  // --- Form State
  final formKey = GlobalKey<FormState>();
  final isLoading = false.obs;

  // --- Product Variant Model being edited
  final ProductVariantModel initialVariant; // The variant passed to the controller
  final ProductModel parentProduct; // The parent product model (for dimensions, category, segment)

  // --- TextEditingControllers for form fields
  late TextEditingController sku;
  late TextEditingController quantityOnHand;
  late TextEditingController quantityOnOrder;
  late TextEditingController quantityInProduction;

  // --- Rx variables for attributes and status
  late RxString selectedMaterial;
  late RxString selectedThickness;
  late RxString selectedFinish;
  late RxString selectedLength;
  late Rx<ProductStatus> selectedStatus; // Reactive variable for variant status

  EditProductVariantController({required this.initialVariant, required this.parentProduct});

  @override
  void onInit() {
    super.onInit();
    // Initialize controllers and Rx variables with the initial variant's data
    sku = TextEditingController(text: initialVariant.sku);
    quantityOnHand = TextEditingController(text: initialVariant.quantityOnHand.toString());
    quantityOnOrder = TextEditingController(text: initialVariant.quantityOnOrder.toString());
    quantityInProduction = TextEditingController(text: initialVariant.quantityInProduction.toString());

    selectedMaterial = (initialVariant.attributes['Material'] ?? '').obs;
    selectedThickness = (initialVariant.attributes['Thickness'] ?? '').obs;
    selectedFinish = (initialVariant.attributes['Finish'] ?? '').obs;
    selectedLength = (initialVariant.attributes['Length'] ?? '').obs;
    selectedStatus = initialVariant.status.obs; // Initialize status
  }

  @override
  void onClose() {
    sku.dispose();
    quantityOnHand.dispose();
    quantityOnOrder.dispose();
    quantityInProduction.dispose();
    super.onClose();
  }

  /// --- Update Product Variant ---
  Future<void> updateProductVariant() async {
    try {
      // Start Loading
      TFullScreenLoader.popUpCircular();

      // Check Internet Connectivity
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Form Validation
      if (!formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'Validation Error', message: 'Please correct the errors in the form.');
        return;
      }

      // Basic validation for attributes (if empty, it's a problem)
      if (selectedMaterial.value.isEmpty ||
          selectedThickness.value.isEmpty ||
          selectedFinish.value.isEmpty ||
          selectedLength.value.isEmpty) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'Attributes Required', message: 'Please select all variant attributes.');
        return;
      }

      // Re-calculate weight and SQM based on potentially updated thickness/length
      final double thicknessMm = double.tryParse(selectedThickness.value.replaceAll('mm', '').trim()) ?? 0.0;
      final double lengthM = double.tryParse(selectedLength.value.replaceAll('m', '').trim()) ?? 0.0;

      final Map<String, double> calculatedValues = ProductVariantCalculator.calculateWeightAndSqm(
        productCategoryName: parentProduct.category?.name ?? '',
        widthMm: parentProduct.width,
        heightMm: parentProduct.height,
        lengthM: lengthM,
        thicknessMm: thicknessMm,
        material: selectedMaterial.value,
        productSegment: parentProduct.segment,
      );

      final double newWeightKg = calculatedValues['factoryWeightKg'] ?? 0.0;
      final double newSqm = calculatedValues['factorySqm'] ?? 0.0;

      // Create the updated Product Variant Model
      final updatedVariant = initialVariant.copyWith(
        sku: sku.text.trim(),
        attributes: {
          'Material': selectedMaterial.value,
          'Thickness': selectedThickness.value,
          'Finish': selectedFinish.value,
          'Length': selectedLength.value,
        },
        status: selectedStatus.value, // Include updated status
        weight: newWeightKg, // Assign re-calculated weight
        sqm: newSqm, // Assign re-calculated SQM
        quantityOnHand: int.tryParse(quantityOnHand.text) ?? 0,
        quantityOnOrder: int.tryParse(quantityOnOrder.text) ?? 0,
        quantityInProduction: int.tryParse(quantityInProduction.text) ?? 0,
        updatedAt: DateTime.now(), // Update timestamp
      );

      // Update variant in Firestore via ProductVariantRepository
      await _variantRepository.updateVariant(updatedVariant);

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(
        title: 'Success',
        message: 'Product variant "${updatedVariant.sku}" updated successfully!',
      );

      // Refresh the main product variants list
      _productVariantController.loadVariantsForProduct(parentProduct.id); // Re-load variants for this product
      Get.back(); // Go back to the previous screen (ProductVariantsScreen)
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }
}
