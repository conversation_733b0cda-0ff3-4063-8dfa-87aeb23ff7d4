import 'package:alloy/features/authentication/repository/authentication_repository.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SignupController extends GetxController {
  static SignupController get instance => Get.find();

  // Form Key for validation
  final formKey = GlobalKey<FormState>();

  // Text Editing Controllers
  final email = TextEditingController();
  final password = TextEditingController();
  final firstName = TextEditingController();
  final lastName = TextEditingController(); // Nullable
  final userName = TextEditingController(); // "name" or "username"
  final department = TextEditingController();
  final phoneNumber = TextEditingController(); // Nullable

  // Role selection
  final selectedRole = AppRole.user.obs; // Default role

  // Observable for password visibility
  final hidePassword = true.obs;

  /// --- SIGNUP ---
  Future<void> signup() async {
    try {
      // Start Loading
      TFullScreenLoader.openLoadingDialog('We are processing your information...', 'assets/images/animations/loading.json'); // Replace with your animation

      // Check Internet Connectivity
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'No Internet Connection', message: 'Please check your internet connection and try again.');
        return;
      }

      // Form Validation
      if (!formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Create UserModel
      final user = UserModel(
        email: email.text.trim(),
        firstName: firstName.text.trim(),
        lastName: lastName.text.trim(), // Nullable, trim handles empty
        userName: userName.text.trim(),
        phoneNumber: phoneNumber.text.trim(), // Nullable
        profilePicture: '', // Default empty, can be set later
        department: department.text.trim(),
        roles: [UserRole.Admin], // TODO to modify  the user role screen [selectedRole.value], // Changed to a list containing the selected role
        // id, createdAt, updatedAt will be handled by repository/Firebase
      );

      // Register user in Firebase Auth & Save user data in Firestore
      final authRepo = AuthenticationRepository.instance;
      await authRepo.registerWithEmailAndPassword(email.text.trim(), password.text.trim(), user);

      // Stop Loading
      TFullScreenLoader.stopLoading();

      // Show Success Message
      TLoaders.successSnackBar(title: 'Congratulations', message: "${user.fullName}'s account has been created! You can now login.");

      // Navigate to Login Screen
      Get.offAllNamed(TRoutes.login);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  @override
  void onClose() {
    email.dispose();
    password.dispose();
    firstName.dispose();
    lastName.dispose();
    userName.dispose();
    department.dispose();
    phoneNumber.dispose();
    super.onClose();
  }
}
