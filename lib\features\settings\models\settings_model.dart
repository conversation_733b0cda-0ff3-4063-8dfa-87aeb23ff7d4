import 'package:cloud_firestore/cloud_firestore.dart';

class SettingsModel {
  String id; // Typically a fixed ID like 'global_settings'

  // --- Material Rates (Per KG) ---
  Map<String, double> materialRatesPerKg; // e.g., {'GI': 0.5, 'HDG_45microns': 0.7, 'AL': 2.0, 'SS': 3.0, 'GI_Epoxy': 0.8, 'HDG_Epoxy': 0.9}

  // --- Quotation & Invoice Settings ---
  String nextQuotationNumberPrefix;
  int nextQuotationNumberStart;
  String nextInvoiceNumberPrefix;
  int nextInvoiceNumberStart;
  int defaultQuotationValidityDays;
  int defaultLeadTimeDays;

  // --- General Company Information ---
  String companyName;
  String companyAddress;
  String companyPhone;
  String companyEmail;
  String defaultCurrency;
  double globalTaxRatePercentage;
  String? companyLogoUrl; // NEW: URL for company logo
  String? appLogoUrl; // NEW: URL for app logo

  // --- User & Access Settings ---
  bool allowNewUserRegistration;
  String defaultNewUserRole; // e.g., 'employee', 'viewer'
  bool requireEmailVerification;

  // --- Inventory & Product Settings ---
  int lowStockThreshold;

  // Timestamps
  DateTime? createdAt;
  DateTime? updatedAt;

  SettingsModel({
    this.id = 'global_settings', // Default fixed ID
    required this.materialRatesPerKg,
    this.nextQuotationNumberPrefix = 'Q-',
    this.nextQuotationNumberStart = 1001,
    this.nextInvoiceNumberPrefix = 'INV-',
    this.nextInvoiceNumberStart = 20240001,
    this.defaultQuotationValidityDays = 30,
    this.defaultLeadTimeDays = 7,
    this.companyName = 'Your Company Name',
    this.companyAddress = 'Your Company Address',
    this.companyPhone = '+971 5X XXX XXXX',
    this.companyEmail = '<EMAIL>',
    this.defaultCurrency = 'AED',
    this.globalTaxRatePercentage = 5.0, // Default 5% VAT
    this.companyLogoUrl, // NEW: Initialize companyLogoUrl
    this.appLogoUrl, // NEW: Initialize appLogoUrl
    this.allowNewUserRegistration = false,
    this.defaultNewUserRole = 'employee',
    this.requireEmailVerification = false,
    this.lowStockThreshold = 10,
    this.createdAt,
    this.updatedAt,
  });

  /// Empty Helper Function (for initial state or if settings not found)
  static SettingsModel empty() => SettingsModel(materialRatesPerKg: {});

  /// Convert SettingsModel to JSON format for Firestore.
  Map<String, dynamic> toJson() {
    return {
      'materialRatesPerKg': materialRatesPerKg,
      'nextQuotationNumberPrefix': nextQuotationNumberPrefix,
      'nextQuotationNumberStart': nextQuotationNumberStart,
      'nextInvoiceNumberPrefix': nextInvoiceNumberPrefix,
      'nextInvoiceNumberStart': nextInvoiceNumberStart,
      'defaultQuotationValidityDays': defaultQuotationValidityDays,
      'defaultLeadTimeDays': defaultLeadTimeDays,
      'companyName': companyName,
      'companyAddress': companyAddress,
      'companyPhone': companyPhone,
      'companyEmail': companyEmail,
      'defaultCurrency': defaultCurrency,
      'globalTaxRatePercentage': globalTaxRatePercentage,
      'companyLogoUrl': companyLogoUrl, // NEW: Include companyLogoUrl
      'appLogoUrl': appLogoUrl, // NEW: Include appLogoUrl
      'allowNewUserRegistration': allowNewUserRegistration,
      'defaultNewUserRole': defaultNewUserRole,
      'requireEmailVerification': requireEmailVerification,
      'lowStockThreshold': lowStockThreshold,
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(), // Always update on save
    };
  }

  /// Factory method to create a SettingsModel from a Firestore DocumentSnapshot.
  factory SettingsModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (!document.exists || document.data() == null) {
      // Return a default empty model if document doesn't exist or is empty
      return SettingsModel.empty();
    }

    final data = document.data()!;
    return SettingsModel(
      id: document.id,
      materialRatesPerKg: Map<String, double>.from(data['materialRatesPerKg'] ?? {}),
      nextQuotationNumberPrefix: data['nextQuotationNumberPrefix'] ?? 'Q-',
      nextQuotationNumberStart: (data['nextQuotationNumberStart'] as num?)?.toInt() ?? 1001,
      nextInvoiceNumberPrefix: data['nextInvoiceNumberPrefix'] ?? 'INV-',
      nextInvoiceNumberStart: (data['nextInvoiceNumberStart'] as num?)?.toInt() ?? 20240001,
      defaultQuotationValidityDays: (data['defaultQuotationValidityDays'] as num?)?.toInt() ?? 30,
      defaultLeadTimeDays: (data['defaultLeadTimeDays'] as num?)?.toInt() ?? 7,
      companyName: data['companyName'] ?? 'Your Company Name',
      companyAddress: data['companyAddress'] ?? 'Your Company Address',
      companyPhone: data['companyPhone'] ?? '+971 5X XXX XXXX',
      companyEmail: data['companyEmail'] ?? '<EMAIL>',
      defaultCurrency: data['defaultCurrency'] ?? 'AED',
      globalTaxRatePercentage: (data['globalTaxRatePercentage'] as num?)?.toDouble() ?? 5.0,
      companyLogoUrl: data['companyLogoUrl'] as String?, // NEW: Parse companyLogoUrl
      appLogoUrl: data['appLogoUrl'] as String?, // NEW: Parse appLogoUrl
      allowNewUserRegistration: data['allowNewUserRegistration'] ?? false,
      defaultNewUserRole: data['defaultNewUserRole'] ?? 'employee',
      requireEmailVerification: data['requireEmailVerification'] ?? false,
      lowStockThreshold: (data['lowStockThreshold'] as num?)?.toInt() ?? 10,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }
}
