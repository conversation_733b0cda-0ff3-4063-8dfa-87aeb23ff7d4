import 'package:alloy/common/widgets/data_table/paginated_data_table.dart';
import 'package:alloy/features/account/controller/account_controller.dart';
import 'package:alloy/features/account/models/account_model.dart';
import 'package:alloy/features/account/screens/all_accounts/data_table/table_source.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AccountsTable extends StatelessWidget {
  const AccountsTable({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AccountController>();
    final isMobile = TDeviceUtils.isMobileScreen(context);

    return Obx(() {
      // This is just to trigger the Obx.
      Text(controller.filteredItems.length.toString());
      Text(controller.selectedRows.length.toString());

      return TPaginatedDataTable(
        minWidth: 1200,
        sortAscending: controller.sortAscending.value,
        sortColumnIndex: controller.sortColumnIndex.value,
        columns: [
          DataColumn2(
            label: Text('Name'),

            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (AccountModel account) => account.name.toLowerCase()),
          ),
          DataColumn2(
            label: Text('Business'),
            fixedWidth: isMobile ? null : 120,
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (AccountModel account) => account.businessType.name.toLowerCase(),
            ),
          ),
          DataColumn2(
            label: Text('Emirates'),
            fixedWidth: isMobile ? null : 120,
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (AccountModel account) => account.emirates?.toLowerCase() ?? '',
            ),
          ),
          DataColumn2(label: Text('Phone'), fixedWidth: isMobile ? null : 120),
          DataColumn2(label: Text('Status'), fixedWidth: isMobile ? null : 120),
          DataColumn2(label: Text('Handlers'), fixedWidth: isMobile ? null : 120),
          DataColumn2(label: Text('Contacts'), fixedWidth: isMobile ? null : 120),
          DataColumn2(label: Text('Action'), fixedWidth: isMobile ? null : 100),
        ],
        source: AccountsRows(),
      );
    });
  }
}
