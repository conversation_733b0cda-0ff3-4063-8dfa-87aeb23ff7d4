import 'package:alloy/common/layouts/headers/header.dart';
import 'package:alloy/common/layouts/sidebars/sidebar.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:flutter/material.dart';

class TabletLayout extends StatelessWidget {
  TabletLayout({super.key, this.body});

  final Widget? body;

  final GlobalKey<ScaffoldState> scaffoldKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    // Check if we're already inside a Scaffold
    // if (context.findAncestorWidgetOfExactType<Scaffold>() != null) {
    //   return body ??
    //       TRoundedContainer(width: double.infinity, height: 500, backgroundColor: Colors.blue.withOpacity(0.2));
    // }

    return Scaffold(
      key: scaffoldKey,
      drawer: TSideBar(),
      // Scaffold key toggle for tablet layout
      appBar: THeader(scaffoldKey: scaffoldKey),
      body:
          body ?? TRoundedContainer(width: double.infinity, height: 500, backgroundColor: Colors.blue.withOpacity(0.2)),
    );
  }
}
