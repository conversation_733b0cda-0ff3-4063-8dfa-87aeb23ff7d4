name: alloy
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  firebase_core: ^3.14.0
  get: ^4.7.2
  get_storage: ^2.1.1
  firebase_auth: ^5.6.0
  intl: ^0.20.2
  firebase_storage: ^12.4.7
  connectivity_plus: ^6.1.4
  iconsax: ^0.0.8
  data_table_2: ^2.6.0
  cached_network_image: ^3.4.1
  lottie: ^3.1.0
  shimmer: ^3.0.0
  cloud_firestore: ^5.6.9
  dynamic_path_url_strategy: ^1.0.0
  fl_chart: ^1.0.0
  flutter_dropzone: ^4.2.1
  universal_html: ^2.2.4
  clipboard: ^0.1.3
  excel: ^4.0.6
  file_picker: ^10.2.0
  http: ^1.4.0

# File Packages
  pdf: ^3.11.3
  printing: ^5.14.2
  csv: ^6.0.0
  path_provider: ^2.1.5
  file_saver: ^0.3.0
  url_launcher: ^6.3.1
  popup_menu_2: ^0.1.3
  custom_pop_up_menu: ^1.2.4
  image_picker: ^1.1.2
  flutter_svg: ^2.2.0


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  #--------------- LOCAL ASSETS ------------------#
  assets:
    - assets/logos/
    - assets/images/content/
    - assets/images/animations/


  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  ##--------------- LOCAL FONTS ------------------#
  fonts:
    - family: Urbanist
      fonts:
        - asset: assets/fonts/urbanist/Urbanist-Light.ttf
          weight: 300
        - asset: assets/fonts/urbanist/Urbanist-Regular.ttf
          weight: 400
        - asset: assets/fonts/urbanist/Urbanist-Italic.ttf
          style: italic
        - asset: assets/fonts/urbanist/Urbanist-Medium.ttf
          weight: 500
        - asset: assets/fonts/urbanist/Urbanist-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: assets/fonts/urbanist/Urbanist-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/urbanist/Urbanist-Bold.ttf
          weight: 800
