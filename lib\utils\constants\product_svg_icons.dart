import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart'; // Import flutter_svg

/// A utility class to provide SVG icons for different product types.
class ProductSvgIcons {
  // Common properties for the SVG icons
  static const double _iconSize = 40.0;
  static const Color _iconColor = Colors.blueGrey; // A neutral color for the icons

  /// Returns an SVG icon for a Cable Tray.
  static SvgPicture cableTray({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="${size ?? _iconSize}" height="${size ?? _iconSize}" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="5" y="15" width="40" height="20" rx="3" fill="#E0E0E0" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="2"/>
        <rect x="5" y="13" width="40" height="4" rx="1" fill="#BBBBBB"/>
        <rect x="5" y="35" width="40" height="4" rx="1" fill="#BBBBBB"/>
        <!-- Perforations -->
        <circle cx="12" cy="25" r="2" fill="#FFFFFF"/>
        <circle cx="20" cy="25" r="2" fill="#FFFFFF"/>
        <circle cx="28" cy="25" r="2" fill="#FFFFFF"/>
        <circle cx="36" cy="25" r="2" fill="#FFFFFF"/>
      </svg>
      ''',
      width: size ?? _iconSize,
      height: size ?? _iconSize,
    );
  }

  /// Returns an SVG icon for a Cable Ladder.
  static SvgPicture cableLadder({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="${size ?? _iconSize}" height="${size ?? _iconSize}" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- Side rails -->
        <rect x="10" y="10" width="4" height="30" rx="1" fill="#BBBBBB" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="1"/>
        <rect x="36" y="10" width="4" height="30" rx="1" fill="#BBBBBB" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="1"/>
        <!-- Rungs -->
        <rect x="10" y="15" width="30" height="2" rx="0.5" fill="#BBBBBB" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="0.5"/>
        <rect x="10" y="24" width="30" height="2" rx="0.5" fill="#BBBBBB" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="0.5"/>
        <rect x="10" y="33" width="30" height="2" rx="0.5" fill="#BBBBBB" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="0.5"/>
      </svg>
      ''',
      width: size ?? _iconSize,
      height: size ?? _iconSize,
    );
  }

  /// Returns an SVG icon for a Channel.
  static SvgPicture channel({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="${size ?? _iconSize}" height="${size ?? _iconSize}" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
        <!-- U-shaped channel -->
        <path d="M 10 40 L 10 10 L 40 10 L 40 40 L 35 40 L 35 15 L 15 15 L 15 40 Z" fill="#E0E0E0" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="2"/>
      </svg>
      ''',
      width: size ?? _iconSize,
      height: size ?? _iconSize,
    );
  }

  /// Returns an SVG icon for Trunking.
  static SvgPicture trunking({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="${size ?? _iconSize}" height="${size ?? _iconSize}" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
        <rect x="5" y="15" width="40" height="20" rx="3" fill="#E0E0E0" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="2"/>
        <rect x="5" y="13" width="40" height="4" rx="1" fill="#BBBBBB"/>
        <rect x="5" y="35" width="40" height="4" rx="1" fill="#BBBBBB"/>
        <!-- Lid line -->
        <line x1="5" y1="25" x2="45" y2="25" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="1"/>
      </svg>
      ''',
      width: size ?? _iconSize,
      height: size ?? _iconSize,
    );
  }

  /// Returns a generic placeholder icon if no specific product type matches.
  static SvgPicture placeholder({double? size, Color? color}) {
    return SvgPicture.string(
      '''
      <svg width="${size ?? _iconSize}" height="${size ?? _iconSize}" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="25" cy="25" r="20" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="2"/>
        <line x1="15" y1="15" x2="35" y2="35" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="2"/>
        <line x1="35" y1="15" x2="15" y2="35" stroke="${color?.toHex() ?? _iconColor.toHex()}" stroke-width="2"/>
      </svg>
      ''',
      width: size ?? _iconSize,
      height: size ?? _iconSize,
    );
  }

  /// Helper to convert Color to Hex string for SVG fill/stroke.
  /// This is a simple conversion; for full color support, you might need a more robust method.
  static String _toHex(Color color) {
    return '#${color.value.toRadixString(16).substring(2).toUpperCase()}';
  }
}

// Extension to add toHex() method to Color class
extension ColorToHex on Color {
  String toHex() {
    return '#${value.toRadixString(16).substring(2).toUpperCase()}';
  }
}
