import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:data_table_2/data_table_2.dart'; // Ensure data_table_2 is imported
import 'package:iconsax/iconsax.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../controller/variant_controller/product_variant_controller.dart';
import '../../../models/product_model.dart';
import '../../edit_product_variant/edit_product_variant_screen.dart'; // Iconsax for action icons

class ProductVariantsRows extends DataTableSource {
  final controller = Get.find<ProductVariantController>();

  final ProductModel parentProduct; // Need the parent product to pass to edit screen

  ProductVariantsRows({required this.parentProduct});

  @override
  DataRow2? getRow(int index) {
    if (index >= controller.filteredItems.length) {
      return null; // Handle out of bounds
    }

    final variant = controller.filteredItems[index];

    return DataRow2(
      // Selection logic (if you implement multi-select for variants)
      selected: controller.selectedRows.length > index ? controller.selectedRows[index] : false,
      onSelectChanged: (isSelected) {
        if (controller.selectedRows.length > index) {
          controller.selectedRows[index] = isSelected ?? false;
          notifyListeners(); // Notify listeners to rebuild the table if selection changes
        }
      },
      cells: [
        DataCell(Text(variant.attributes['Material'] ?? '')),
        DataCell(Text(variant.attributes['Thickness'] ?? '')),
        DataCell(Text(variant.attributes['Finish'] ?? '')),
        DataCell(Text(variant.attributes['Length'] ?? '')),
        DataCell(Text(variant.weight?.toStringAsFixed(2) ?? 'N/A')), // Display weight, 2 decimal places
        DataCell(Text(variant.sqm?.toStringAsFixed(3) ?? 'N/A')), // Display sqm, 3 decimal places
        DataCell(Text(variant.quantityOnHand.toString())),
        DataCell(Text(variant.quantityOnOrder.toString())),
        DataCell(Text(variant.quantityInProduction.toString())),
        DataCell(
          Text(
            variant.sku,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: Theme.of(Get.context!).textTheme.labelSmall!.apply(color: Colors.blueAccent),
          ),
        ),

        DataCell(
          Row(
            mainAxisSize: MainAxisSize.min, // Keep action buttons compact
            children: [
              IconButton(
                onPressed: () {
                  // Navigate to EditProductVariantScreen, passing the variant and its parent product
                  Get.to(() => EditProductVariantScreen(variant: variant, parentProduct: parentProduct));
                  Get.snackbar('Edit Variant', 'Edit functionality for SKU: ${variant.sku} not yet implemented.');
                },
                icon: const Icon(Iconsax.edit),
              ),
              IconButton(
                onPressed: () =>
                    controller.confirmDeleteItem(variant), // Assuming confirmDeleteItem handles variant deletion
                icon: const Icon(Iconsax.trash, color: TColors.error),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  bool get isRowCountApproximate => false; // We know the exact row count

  @override
  int get rowCount => controller.filteredItems.length; // Number of rows based on filtered items

  @override
  int get selectedRowCount => controller.selectedRows.where((isSelected) => isSelected).length; // Count of selected rows
}
