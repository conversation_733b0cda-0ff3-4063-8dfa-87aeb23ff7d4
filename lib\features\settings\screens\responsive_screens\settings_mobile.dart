import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart'; // For icons

// Assuming these imports are correct for your project
import '../../../../common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import '../../../../common/widgets/containers/rounded_container.dart'; // TRoundedContainer
import '../../../../common/widgets/images/t_rounded_image.dart';
import '../../../../utils/constants/enums.dart';
import '../../../../utils/constants/image_strings.dart';
import '../../../../utils/constants/sizes.dart'; // TSizes
import '../../../../utils/constants/colors.dart'; // TColors
import '../../../../utils/validators/validation.dart'; // TValidator
import '../../controllers/settings_controller.dart';

class SettingsMobile extends StatelessWidget {
  const SettingsMobile({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SettingsController()); // Get or put the controller

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'App Settings',
                breadcrumbItems: [TBreadcrumbItem(text: 'Settings')],
              ),

              const SizedBox(height: TSizes.spaceBtwSections),

              // Main Content Area
              Obx(() {
                if (controller.isLoading.value) {
                  return const Center(child: CircularProgressIndicator());
                }

                return Form(
                  key: controller.formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // --- Section: Company Information & Logos ---
                      TRoundedContainer(
                        padding: const EdgeInsets.all(TSizes.defaultSpace),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Company Information', style: Theme.of(context).textTheme.headlineSmall),
                            const SizedBox(height: TSizes.spaceBtwSections),

                            // Company Name, Address, Phone, Email (single column on mobile)
                            TextFormField(
                              controller: controller.companyName,
                              decoration: const InputDecoration(labelText: 'Company Name', prefixIcon: Icon(Iconsax.building)),
                              validator: (value) => TValidator.validateEmptyText('Company Name', value),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.companyAddress,
                              decoration: const InputDecoration(labelText: 'Company Address', prefixIcon: Icon(Iconsax.location)),
                              validator: (value) => TValidator.validateEmptyText('Company Address', value),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.companyPhone,
                              decoration: const InputDecoration(labelText: 'Company Phone', prefixIcon: Icon(Iconsax.call)),
                              keyboardType: TextInputType.phone,
                              validator: (value) => TValidator.validateEmptyText('Company Phone', value),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.companyEmail,
                              decoration: const InputDecoration(labelText: 'Company Email', prefixIcon: Icon(Iconsax.sms)),
                              keyboardType: TextInputType.emailAddress,
                              validator: (value) => TValidator.validateEmail(value),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.defaultCurrency,
                              decoration: const InputDecoration(labelText: 'Default Currency', prefixIcon: Icon(Iconsax.dollar_square)),
                              validator: (value) => TValidator.validateEmptyText('Default Currency', value),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.globalTaxRatePercentage,
                              decoration: const InputDecoration(labelText: 'Global Tax Rate (%)', prefixIcon: Icon(Iconsax.percentage_square)),
                              keyboardType: TextInputType.number,
                              validator: (value) => TValidator.validateEmptyText(value, 'Tax Rate'),
                            ),
                            const SizedBox(height: TSizes.spaceBtwSections),

                            // Logos (stacked vertically on mobile)
                            Text('Company Logos', style: Theme.of(context).textTheme.titleMedium),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            _buildLogoUploadSection(
                              context,
                              'Company Logo',
                              controller.companyLogoUrl.value,
                              controller.uploadCompanyLogo,
                              TImages.bonnAppLogo, // Pass default asset for company logo
                            ),
                            const SizedBox(height: TSizes.spaceBtwSections), // More space between logo sections
                            _buildLogoUploadSection(
                              context,
                              'App Logo',
                              controller.appLogoUrl.value,
                              controller.uploadAppLogo,
                              TImages.bonnAppLogo, // Pass default asset for app logo
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // --- Section: Quotation & Invoice Numbering ---
                      TRoundedContainer(
                        padding: const EdgeInsets.all(TSizes.defaultSpace),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Quotation & Invoice Numbering', style: Theme.of(context).textTheme.headlineSmall),
                            const SizedBox(height: TSizes.spaceBtwSections),
                            // Stacked vertically on mobile
                            TextFormField(
                              controller: controller.nextQuotationNumberPrefix,
                              decoration: const InputDecoration(labelText: 'Quotation Prefix', prefixIcon: Icon(Iconsax.tag)),
                              validator: (value) => TValidator.validateEmptyText('Quotation Prefix', value),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.nextQuotationNumberStart,
                              decoration: const InputDecoration(labelText: 'Quotation Start #', prefixIcon: Icon(Iconsax.hashtag)),
                              keyboardType: TextInputType.number,
                              validator: (value) => TValidator.validateEmptyText(value, 'Quotation Start Number'),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.nextInvoiceNumberPrefix,
                              decoration: const InputDecoration(labelText: 'Invoice Prefix', prefixIcon: Icon(Iconsax.tag_2)),
                              validator: (value) => TValidator.validateEmptyText('Invoice Prefix', value),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.nextInvoiceNumberStart,
                              decoration: const InputDecoration(labelText: 'Invoice Start #', prefixIcon: Icon(Iconsax.hashtag)),
                              keyboardType: TextInputType.number,
                              validator: (value) => TValidator.validateEmptyText(value, 'Invoice Start Number'),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.defaultQuotationValidityDays,
                              decoration: const InputDecoration(labelText: 'Quotation Validity (Days)', prefixIcon: Icon(Iconsax.calendar_1)),
                              keyboardType: TextInputType.number,
                              validator: (value) => TValidator.validateEmptyText(value, 'Quotation Validity Days'),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.defaultLeadTimeDays,
                              decoration: const InputDecoration(labelText: 'Default Lead Time (Days)', prefixIcon: Icon(Iconsax.clock)),
                              keyboardType: TextInputType.number,
                              validator: (value) => TValidator.validateEmptyText(value, 'Default Lead Time Days'),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // --- Section: Material Rates (Per KG) ---
                      TRoundedContainer(
                        padding: const EdgeInsets.all(TSizes.defaultSpace),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Material Rates (Per KG)', style: Theme.of(context).textTheme.headlineSmall),
                            const SizedBox(height: TSizes.spaceBtwSections),
                            // Single column for material rates on mobile
                            Column(
                              children: controller.materialTypes.map((materialType) {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: TSizes.spaceBtwInputFields),
                                  child: TextFormField(
                                    controller: controller.materialRateControllers[materialType],
                                    decoration: InputDecoration(labelText: '$materialType Rate', prefixIcon: const Icon(Iconsax.dollar_circle)),
                                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                                    validator: (value) => TValidator.validateEmptyText(value, '$materialType Rate'),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // --- Section: User & Access Settings ---
                      TRoundedContainer(
                        padding: const EdgeInsets.all(TSizes.defaultSpace),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('User & Access Settings', style: Theme.of(context).textTheme.headlineSmall),
                            const SizedBox(height: TSizes.spaceBtwSections),
                            Obx(
                              () => SwitchListTile(
                                title: Text('Allow New User Registration', style: Theme.of(context).textTheme.titleMedium),
                                subtitle: const Text('Enable/disable self-registration for new users.'),
                                value: controller.allowNewUserRegistration.value,
                                onChanged: (value) => controller.allowNewUserRegistration.value = value,
                                activeColor: TColors.primary,
                              ),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            TextFormField(
                              controller: controller.defaultNewUserRole,
                              decoration: const InputDecoration(
                                labelText: 'Default New User Role',
                                hintText: 'e.g., employee, viewer',
                                prefixIcon: Icon(Iconsax.user_tag),
                              ),
                              validator: (value) => TValidator.validateEmptyText('Default New User Role', value),
                            ),
                            const SizedBox(height: TSizes.spaceBtwInputFields),
                            Obx(
                              () => SwitchListTile(
                                title: Text('Require Email Verification', style: Theme.of(context).textTheme.titleMedium),
                                subtitle: const Text('Require users to verify their email after registration.'),
                                value: controller.requireEmailVerification.value,
                                onChanged: (value) => controller.requireEmailVerification.value = value,
                                activeColor: TColors.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // --- Section: Inventory & Product Settings ---
                      TRoundedContainer(
                        padding: const EdgeInsets.all(TSizes.defaultSpace),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('Inventory Settings', style: Theme.of(context).textTheme.headlineSmall),
                            const SizedBox(height: TSizes.spaceBtwSections),
                            TextFormField(
                              controller: controller.lowStockThreshold,
                              decoration: const InputDecoration(
                                labelText: 'Low Stock Threshold',
                                hintText: 'Quantity to flag as low stock',
                                prefixIcon: Icon(Iconsax.box_time),
                              ),
                              keyboardType: TextInputType.number,
                              validator: (value) => TValidator.validateEmptyText(value, 'Low Stock Threshold'),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Save Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(onPressed: () => controller.saveSettings(), child: const Text('Save Settings')),
                      ),
                    ],
                  ),
                );
              }),
            ],
          ),
        ),
      ),
    );
  }

  /// Helper widget to build logo upload sections.
  Widget _buildLogoUploadSection(BuildContext context, String title, String? imageUrl, VoidCallback onUploadPressed, String defaultAssetPath) {
    // Determine if a network image URL is available
    final bool isNetworkImageAvailable = imageUrl != null && imageUrl.isNotEmpty;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: TSizes.spaceBtwItems),
        TRoundedContainer(
          width: double.infinity,
          height: 120, // Fixed height for logo preview
          backgroundColor: TColors.lightGrey,
          radius: TSizes.cardRadiusMd,
          padding: const EdgeInsets.all(TSizes.sm),
          child: isNetworkImageAvailable
              ? TRoundedImage(
                  image: imageUrl, // Use the network URL
                  fit: BoxFit.contain,
                  borderRadius: TSizes.cardRadiusMd,
                  imageType: ImageType.network, // Use ImageType.network
                )
              : TRoundedImage(
                  image: defaultAssetPath, // Use the default asset path
                  imageType: ImageType.asset, // Use ImageType.asset
                  fit: BoxFit.contain,
                  borderRadius: TSizes.cardRadiusMd,
                ),
        ),
        const SizedBox(height: TSizes.spaceBtwItems),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(onPressed: onUploadPressed, icon: const Icon(Iconsax.cloud_add), label: const Text('Upload')),
        ),
      ],
    );
  }
}
