import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../utils/popups/loaders.dart';
import '../models/address_model.dart';

class AddEditAddressController extends GetxController {
  static AddEditAddressController get instance => Get.find();

  final formKey = GlobalKey<FormState>();

  // Text controllers for address fields
  final addressLine1 = TextEditingController();
  final addressLine2 = TextEditingController();
  final street = TextEditingController();
  final city = TextEditingController();
  final state = TextEditingController(); // For Emirates
  final zipCode = TextEditingController();
  final country = TextEditingController();
  final notes = TextEditingController();

  // Reactive variables for dropdowns/checkboxes
  final RxString type = 'Shipping'.obs; // Default type
  final RxBool isDefault = false.obs;

  // Store the original address if editing
  AddressModel? originalAddress;

  @override
  void onClose() {
    addressLine1.dispose();
    addressLine2.dispose();
    street.dispose();
    city.dispose();
    state.dispose();
    zipCode.dispose();
    country.dispose();
    notes.dispose();
    super.onClose();
  }

  /// Initializes the form fields with an existing address for editing, or clears them for new creation.
  void initializeForm(AddressModel? address) {
    originalAddress = address;
    if (address != null) {
      addressLine1.text = address.addressLine1;
      addressLine2.text = address.addressLine2 ?? '';
      street.text = address.street ?? '';
      city.text = address.city;
      state.text = address.state ?? '';
      zipCode.text = address.zipCode ?? '';
      country.text = address.country;
      notes.text = address.notes ?? '';
      type.value = address.type;
      isDefault.value = address.isDefault;
    } else {
      // Clear fields for a new address
      addressLine1.clear();
      addressLine2.clear();
      street.clear();
      city.clear();
      state.clear();
      zipCode.clear();
      country.clear();
      notes.clear();
      type.value = 'Shipping'; // Reset to default type
      isDefault.value = false; // Reset to non-default
    }
  }

  /// Validates the form and returns an AddressModel.
  /// If successful, returns the AddressModel; otherwise, returns null.
  AddressModel? validateAndSaveAddress({required String accountId, required String createdByUserId}) {
    if (!formKey.currentState!.validate()) {
      TLoaders.warningSnackBar(title: 'Invalid Form', message: 'Please correct the errors in the address form.');
      return null;
    }

    // Create a new AddressModel or update the existing one
    final newAddress = AddressModel(
      id: originalAddress?.id ?? '', // Keep original ID if editing, otherwise empty for new
      accountId: accountId, // This will be set by the parent account controller
      addressLine1: addressLine1.text.trim(),
      addressLine2: addressLine2.text.trim().isEmpty ? null : addressLine2.text.trim(),
      street: street.text.trim().isEmpty ? null : street.text.trim(),
      city: city.text.trim(),
      state: state.text.trim().isEmpty ? null : state.text.trim(),
      zipCode: zipCode.text.trim().isEmpty ? null : zipCode.text.trim(),
      country: country.text.trim(),
      notes: notes.text.trim().isEmpty ? null : notes.text.trim(),
      type: type.value,
      isDefault: isDefault.value,
      createdAt: originalAddress?.createdAt ?? DateTime.now(), // Preserve creation date if editing
      updatedAt: DateTime.now(),
      createdByUserId: originalAddress?.createdByUserId ?? createdByUserId, // Preserve or set
      updatedByUserId: createdByUserId, // Always update with current user
    );

    return newAddress;
  }
}
