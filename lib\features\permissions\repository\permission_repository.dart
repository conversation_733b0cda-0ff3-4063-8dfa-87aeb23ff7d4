import 'package:alloy/features/permissions/models/module_permission_model.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/exceptions/firebase_exceptions.dart';
import 'package:alloy/utils/exceptions/platform_exceptions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

/// Repository for managing user permissions in Firestore
class PermissionRepository extends GetxController {
  static PermissionRepository get instance => Get.find();

  /// Firestore instance
  final FirebaseFirestore _db = FirebaseFirestore.instance;

  /// Collection reference for user permissions
  CollectionReference get _permissionsCollection => _db.collection('user_permissions');

  /// Get user permissions by user ID
  Future<UserPermissions?> getUserPermissions(String userId) async {
    try {
      final doc = await _permissionsCollection.doc(userId).get();
      if (doc.exists) {
        return UserPermissions.fromSnapshot(doc);
      }
      return null;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Stream user permissions for real-time updates
  Stream<UserPermissions?> streamUserPermissions(String userId) {
    try {
      return _permissionsCollection.doc(userId).snapshots().map((doc) {
        if (doc.exists) {
          return UserPermissions.fromSnapshot(doc);
        }
        return null;
      });
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Create or update user permissions
  Future<void> saveUserPermissions(UserPermissions permissions) async {
    try {
      final data = permissions.toMap();
      data['updatedAt'] = FieldValue.serverTimestamp();

      await _permissionsCollection.doc(permissions.userId).set(data, SetOptions(merge: true));
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Create default permissions for a new user based on their role
  Future<void> createDefaultPermissions(
    String userId,
    List<UserRole> roles, {
    List<String> departments = const [],
  }) async {
    try {
      // Get the highest privilege role to determine permissions
      final highestRole = _getHighestPrivilegeRole(roles);

      // Get permission template for the role
      final permissions = PermissionTemplates.getTemplateForRole(highestRole);

      final userPermissions = UserPermissions(
        userId: userId,
        permissions: permissions,
        departments: departments,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await saveUserPermissions(userPermissions);
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Update specific permission for a user
  Future<void> updateUserPermission(String userId, AppModule module, PermissionType permission, bool granted) async {
    try {
      final userPermissions = await getUserPermissions(userId);
      if (userPermissions == null) {
        throw 'User permissions not found';
      }

      // Find and update the specific permission
      final updatedPermissions = userPermissions.permissions.map((p) {
        if (p.module == module && p.permission == permission) {
          return ModulePermission(module: module, permission: permission, granted: granted);
        }
        return p;
      }).toList();

      // If permission doesn't exist, add it
      if (!updatedPermissions.any((p) => p.module == module && p.permission == permission)) {
        updatedPermissions.add(ModulePermission(module: module, permission: permission, granted: granted));
      }

      final updated = userPermissions.copyWith(permissions: updatedPermissions, updatedAt: DateTime.now());

      await saveUserPermissions(updated);
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Update user's department access
  Future<void> updateUserDepartments(String userId, List<String> departments) async {
    try {
      final userPermissions = await getUserPermissions(userId);
      if (userPermissions == null) {
        throw 'User permissions not found';
      }

      final updated = userPermissions.copyWith(departments: departments, updatedAt: DateTime.now());

      await saveUserPermissions(updated);
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Delete user permissions
  Future<void> deleteUserPermissions(String userId) async {
    try {
      await _permissionsCollection.doc(userId).delete();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Get all users with their permissions (for admin management)
  Future<List<UserPermissions>> getAllUserPermissions() async {
    try {
      final snapshot = await _permissionsCollection.get();
      return snapshot.docs.map((doc) => UserPermissions.fromSnapshot(doc)).toList();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Stream all user permissions for real-time admin management
  Stream<List<UserPermissions>> streamAllUserPermissions() {
    try {
      return _permissionsCollection.snapshots().map((snapshot) {
        return snapshot.docs.map((doc) => UserPermissions.fromSnapshot(doc)).toList();
      });
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Bulk update permissions for multiple users
  Future<void> bulkUpdatePermissions(List<UserPermissions> permissionsList) async {
    try {
      final batch = _db.batch();

      for (final permissions in permissionsList) {
        final data = permissions.toMap();
        data['updatedAt'] = FieldValue.serverTimestamp();

        final docRef = _permissionsCollection.doc(permissions.userId);
        batch.set(docRef, data, SetOptions(merge: true));
      }

      await batch.commit();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Apply role template to user permissions
  Future<void> applyRoleTemplate(String userId, UserRole role, {List<String>? departments}) async {
    try {
      final permissions = PermissionTemplates.getTemplateForRole(role);

      final userPermissions = UserPermissions(
        userId: userId,
        permissions: permissions,
        departments: departments ?? [],
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await saveUserPermissions(userPermissions);
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Helper method to determine the highest privilege role
  UserRole _getHighestPrivilegeRole(List<UserRole> roles) {
    // Define role hierarchy (higher index = higher privilege)
    const roleHierarchy = [
      UserRole.FactoryWorker,
      UserRole.SalesUser,
      UserRole.Accountant,
      UserRole.ProductionReviewer,
      UserRole.Manager,
      UserRole.Admin,
    ];

    UserRole highestRole = UserRole.SalesUser; // Default

    for (final role in roles) {
      final currentIndex = roleHierarchy.indexOf(role);
      final highestIndex = roleHierarchy.indexOf(highestRole);

      if (currentIndex > highestIndex) {
        highestRole = role;
      }
    }

    return highestRole;
  }

  /// Check if user has permission (used by permission service)
  Future<bool> checkUserPermission(String userId, AppModule module, PermissionType permission) async {
    try {
      final userPermissions = await getUserPermissions(userId);
      return userPermissions?.hasPermission(module, permission) ?? false;
    } catch (e) {
      // Return false on error for security
      return false;
    }
  }

  /// Check if user has any permission for module
  Future<bool> checkUserModuleAccess(String userId, AppModule module) async {
    try {
      final userPermissions = await getUserPermissions(userId);
      return userPermissions?.hasAnyPermissionForModule(module) ?? false;
    } catch (e) {
      // Return false on error for security
      return false;
    }
  }
}
