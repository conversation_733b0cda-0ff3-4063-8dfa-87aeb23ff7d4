import 'package:flutter/material.dart';
import '../../../utils/constants/colors.dart';

/// A customized choice chip that can act like a radio button.
class TChoiceChip extends StatelessWidget {
  /// Create a chip that acts like a radio button.
  ///
  /// Parameters:
  ///   - text: The label text for the chip.
  ///   - selected: Whether the chip is currently selected.
  ///   - onSelected: Callback function when the chip is selected.
  const TChoiceChip({super.key, required this.text, required this.selected, this.onSelected, required this.color});

  final String text;
  final Color color;
  final bool selected;
  final void Function(bool)? onSelected;

  @override
  Widget build(BuildContext context) {
    return Theme(
      // Use a transparent canvas color to match the existing styling.
      data: Theme.of(context).copyWith(canvasColor: Colors.transparent),
      child: ChoiceChip(
        // Use this function to get Colors as a Chip
        // avatar: THelperFunctions.getColor(text) != null ? TCircularContainer(width: 50, height: 50, backgroundColor: THelperFunctions.getColor(text)!) : null,
        selected: selected,
        onSelected: onSelected,
        //backgroundColor: color,
        selectedColor: color,
        labelStyle: TextStyle(color: selected ? TColors.white : null),
        // shape: THelperFunctions.getColor(text) != null ? const CircleBorder() : null,
        label: Text(text),
        // padding: THelperFunctions.getColor(text) != null ? const EdgeInsets.all(0) : null,
        // labelPadding: THelperFunctions.getColor(text) != null ? const EdgeInsets.all(0) : null,
      ),
    );
  }
}
