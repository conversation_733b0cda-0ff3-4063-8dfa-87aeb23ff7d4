import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/features/brand/screens/create_brand/widgets/create_brand_form.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';

class CreateBrandTablet extends StatelessWidget {
  const CreateBrandTablet({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Create Brand',
                breadcrumbItems: [
                  TBreadcrumbItem(text: 'Brands', route: TRoutes.createBrand),
                  TBreadcrumbItem(text: 'Create Brand'),
                ],
                showBackButton: true,
              ),
              SizedBox(height: TSizes.spaceBtwSections),

              // Form
              CreateBrandForm(),
            ],
          ),
        ), // Padding
      ), // SingleChildScrollView
    ); // Scaffold
  }
}
