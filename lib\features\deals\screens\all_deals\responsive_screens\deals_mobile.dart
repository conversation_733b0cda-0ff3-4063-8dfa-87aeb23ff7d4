import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/containers/rounded_container.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_controller.dart';
import '../../../models/deal_model.dart';
import '../widgets/deals_filter_panel.dart';
import '../widgets/sales_pipeline_widget.dart';

class DealsMobile extends StatelessWidget {
  const DealsMobile({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DealController());

    return Scaffold(
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(TSizes.defaultSpace),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Iconsax.document, size: 24),
                    const SizedBox(width: TSizes.spaceBtwItems),
                    Text('Deals', style: Theme.of(context).textTheme.headlineMedium),
                  ],
                ),
                const SizedBox(height: TSizes.spaceBtwItems),

                // Search Bar
                TextField(
                  onChanged: (query) => controller.searchQuery(query),
                  decoration: InputDecoration(
                    hintText: 'Search deals...',
                    prefixIcon: const Icon(Iconsax.search_normal),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.borderRadiusMd)),
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwItems),

                // Filter Panel
                const DealsFilterPanel(),
                const SizedBox(height: TSizes.spaceBtwItems),

                // Sales Pipeline
                const SalesPipelineWidget(),
              ],
            ),
          ),

          // List
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (controller.filteredItems.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Iconsax.document, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: TSizes.spaceBtwItems),
                      Text(
                        'No deals found',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: TSizes.spaceBtwItems),
                      ElevatedButton.icon(
                        onPressed: () => Get.toNamed('/create-deal'),
                        icon: const Icon(Iconsax.add),
                        label: const Text('Create Deal'),
                      ),
                    ],
                  ),
                );
              }

              return ListView.separated(
                padding: const EdgeInsets.all(TSizes.defaultSpace),
                itemCount: controller.filteredItems.length,
                separatorBuilder: (_, __) => const SizedBox(height: TSizes.spaceBtwItems),
                itemBuilder: (_, index) {
                  final deal = controller.filteredItems[index];
                  return _buildDealCard(context, deal, controller);
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Get.toNamed('/create-deal'),
        child: const Icon(Iconsax.add),
      ),
    );
  }

  Widget _buildDealCard(BuildContext context, DealModel deal, DealController controller) {
    final statusColor = _getStatusColor(deal.status);
    final isDark = THelperFunctions.isDarkMode(context);
    final daysSinceCreated = _calculateDaysSinceCreated(deal);
    final progressPercentage = _calculateProgressPercentage(deal);

    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.md),
      backgroundColor: isDark ? TColors.dark : TColors.white,
      showBorder: true,
      borderColor: statusColor.withValues(alpha: 0.2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row with Deal Number and Status
          Row(
            children: [
              // Deal Icon
              Container(
                padding: const EdgeInsets.all(TSizes.xs),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                ),
                child: Icon(_getStatusIcon(deal.status), size: 20, color: statusColor),
              ),
              const SizedBox(width: TSizes.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          deal.dealNumber,
                          style: Theme.of(
                            context,
                          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: statusColor),
                        ),
                        if (deal.version > 1) ...[
                          const SizedBox(width: TSizes.xs),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(10),
                            ),
                            child: Text(
                              'v${deal.version}',
                              style: Theme.of(
                                context,
                              ).textTheme.labelSmall?.copyWith(color: Colors.blue, fontWeight: FontWeight.bold),
                            ),
                          ),
                        ],
                      ],
                    ),
                    Text(
                      deal.clientName,
                      style: Theme.of(
                        context,
                      ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600], fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ),
              // Status Badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                ),
                child: Text(
                  deal.status.name.toUpperCase(),
                  style: Theme.of(
                    context,
                  ).textTheme.labelSmall?.copyWith(color: statusColor, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Progress Bar Section
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Progress',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  Text(
                    '${progressPercentage.toInt()}%',
                    style: Theme.of(
                      context,
                    ).textTheme.labelMedium?.copyWith(fontWeight: FontWeight.bold, color: statusColor),
                  ),
                ],
              ),
              const SizedBox(height: TSizes.xs),
              LinearProgressIndicator(
                value: progressPercentage / 100,
                backgroundColor: Colors.grey.withValues(alpha: 0.2),
                valueColor: AlwaysStoppedAnimation<Color>(statusColor),
                minHeight: 6,
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Key Metrics Row
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(context, 'Sales Person', deal.salesPersonName, Iconsax.user, Colors.blue),
              ),
              Expanded(
                child: _buildMetricItem(
                  context,
                  'Days Active',
                  '$daysSinceCreated days',
                  Iconsax.calendar,
                  Colors.orange,
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.sm),
          Row(
            children: [
              Expanded(
                child: _buildMetricItem(
                  context,
                  'Total Value',
                  'AED ${deal.grandTotalAmount.toStringAsFixed(0)}',
                  Iconsax.money,
                  Colors.green,
                ),
              ),
              Expanded(
                child: _buildMetricItem(
                  context,
                  'Valid Until',
                  _formatValidityDate(deal.validityDate),
                  Iconsax.clock,
                  _getValidityColor(deal.validityDate),
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Actions Row
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: controller.canEditDeal(deal) ? () => Get.toNamed('/edit-deal', arguments: deal) : null,
                  icon: Icon(controller.canEditDeal(deal) ? Iconsax.edit : Iconsax.eye, size: 16),
                  label: Text(controller.canEditDeal(deal) ? 'Edit' : 'View'),
                  style: OutlinedButton.styleFrom(side: BorderSide(color: statusColor.withValues(alpha: 0.5))),
                ),
              ),
              const SizedBox(width: TSizes.sm),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => Get.toNamed('/deal-details', arguments: deal),
                  icon: const Icon(Iconsax.document_text, size: 16),
                  label: const Text('Details'),
                  style: ElevatedButton.styleFrom(backgroundColor: statusColor, foregroundColor: Colors.white),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Helper method to build metric items
  Widget _buildMetricItem(BuildContext context, String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 14, color: color),
              const SizedBox(width: TSizes.xs),
              Expanded(
                child: Text(
                  label,
                  style: Theme.of(
                    context,
                  ).textTheme.labelSmall?.copyWith(color: Colors.grey[600], fontWeight: FontWeight.w500),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 2),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold, color: color),
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  // Calculate days since deal was created
  int _calculateDaysSinceCreated(DealModel deal) {
    if (deal.createdAt == null) return 0;
    return DateTime.now().difference(deal.createdAt!).inDays;
  }

  // Calculate progress percentage based on deal status
  double _calculateProgressPercentage(DealModel deal) {
    // Use the progressPercentage field if available, otherwise calculate based on status
    if (deal.progressPercentage > 0) {
      return deal.progressPercentage;
    }

    switch (deal.status) {
      case DealStatus.Draft:
        return 10.0;
      case DealStatus.PendingApproval:
        return 30.0;
      case DealStatus.Approved:
        return 60.0;
      case DealStatus.ClientApproved:
        return 90.0;
      case DealStatus.Closed:
        return 100.0;
      case DealStatus.Rejected:
      case DealStatus.ClientDeclined:
        return 0.0;
      case DealStatus.UnlockRequested:
        return 45.0;
      case DealStatus.Superseded:
        return 100.0;
    }
  }

  // Get appropriate icon for deal status
  IconData _getStatusIcon(DealStatus status) {
    switch (status) {
      case DealStatus.Draft:
        return Iconsax.document_text;
      case DealStatus.PendingApproval:
        return Iconsax.clock;
      case DealStatus.Approved:
        return Iconsax.tick_circle;
      case DealStatus.Rejected:
        return Iconsax.close_circle;
      case DealStatus.UnlockRequested:
        return Iconsax.unlock;
      case DealStatus.ClientApproved:
        return Iconsax.medal_star;
      case DealStatus.ClientDeclined:
        return Iconsax.dislike;
      case DealStatus.Superseded:
        return Iconsax.refresh;
      case DealStatus.Closed:
        return Iconsax.archive_tick;
    }
  }

  // Format validity date
  String _formatValidityDate(DateTime validityDate) {
    final now = DateTime.now();
    final difference = validityDate.difference(now).inDays;

    if (difference < 0) {
      return 'Expired';
    } else if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Tomorrow';
    } else if (difference <= 7) {
      return '$difference days';
    } else {
      return '${(difference / 7).ceil()} weeks';
    }
  }

  // Get color for validity date
  Color _getValidityColor(DateTime validityDate) {
    final now = DateTime.now();
    final difference = validityDate.difference(now).inDays;

    if (difference < 0) {
      return Colors.red;
    } else if (difference <= 3) {
      return Colors.orange;
    } else if (difference <= 7) {
      return Colors.amber;
    } else {
      return Colors.green;
    }
  }

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.Draft:
        return Colors.grey;
      case DealStatus.PendingApproval:
        return Colors.orange;
      case DealStatus.Approved:
        return Colors.green;
      case DealStatus.Rejected:
        return Colors.red;
      case DealStatus.UnlockRequested:
        return Colors.purple;
      case DealStatus.ClientApproved:
        return Colors.teal;
      case DealStatus.ClientDeclined:
        return Colors.red.shade700;
      case DealStatus.Superseded:
        return Colors.brown;
      case DealStatus.Closed:
        return Colors.blue;
    }
  }
}
