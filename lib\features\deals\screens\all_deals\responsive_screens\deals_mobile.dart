import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/containers/rounded_container.dart';

import '../../../../../utils/constants/colors.dart';
import '../../../../../utils/constants/enums.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/helpers/helper_functions.dart';
import '../../../controllers/deal_controller.dart';
import '../../../models/deal_model.dart';
import '../widgets/deals_filter_panel.dart';

class DealsMobile extends StatelessWidget {
  const DealsMobile({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DealController());

    return Scaffold(
      body: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(TSizes.defaultSpace),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Iconsax.document, size: 24),
                    const SizedBox(width: TSizes.spaceBtwItems),
                    Text('Deals', style: Theme.of(context).textTheme.headlineMedium),
                  ],
                ),
                const SizedBox(height: TSizes.spaceBtwItems),

                // Search Bar
                TextField(
                  onChanged: (query) => controller.searchQuery(query),
                  decoration: InputDecoration(
                    hintText: 'Search deals...',
                    prefixIcon: const Icon(Iconsax.search_normal),
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(TSizes.borderRadiusMd)),
                  ),
                ),
                const SizedBox(height: TSizes.spaceBtwItems),

                // Filter Panel
                const DealsFilterPanel(),
              ],
            ),
          ),

          // List
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (controller.filteredItems.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Iconsax.document, size: 64, color: Colors.grey[400]),
                      const SizedBox(height: TSizes.spaceBtwItems),
                      Text(
                        'No deals found',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Colors.grey[600]),
                      ),
                      const SizedBox(height: TSizes.spaceBtwItems),
                      ElevatedButton.icon(
                        onPressed: () => Get.toNamed('/create-deal'),
                        icon: const Icon(Iconsax.add),
                        label: const Text('Create Deal'),
                      ),
                    ],
                  ),
                );
              }

              return ListView.separated(
                padding: const EdgeInsets.all(TSizes.defaultSpace),
                itemCount: controller.filteredItems.length,
                separatorBuilder: (_, __) => const SizedBox(height: TSizes.spaceBtwItems),
                itemBuilder: (_, index) {
                  final deal = controller.filteredItems[index];
                  return _buildDealCard(context, deal, controller);
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => Get.toNamed('/create-deal'),
        child: const Icon(Iconsax.add),
      ),
    );
  }

  Widget _buildDealCard(BuildContext context, DealModel deal, DealController controller) {
    final statusColor = _getStatusColor(deal.status);
    final isDark = THelperFunctions.isDarkMode(context);

    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.md),
      backgroundColor: isDark ? TColors.dark : TColors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header Row
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      deal.dealNumber,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    Text(
                      deal.clientName,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
                decoration: BoxDecoration(
                  color: statusColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(TSizes.borderRadiusSm),
                  border: Border.all(color: statusColor.withValues(alpha: 0.3)),
                ),
                child: Text(
                  deal.status.name.toUpperCase(),
                  style: Theme.of(
                    context,
                  ).textTheme.labelSmall?.copyWith(color: statusColor, fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Details
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sales Person',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(color: Colors.grey[600]),
                    ),
                    Text(deal.salesPersonName, style: Theme.of(context).textTheme.bodySmall),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total Amount',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(color: Colors.grey[600]),
                    ),
                    Text(
                      'AED ${deal.grandTotalAmount.toStringAsFixed(2)}',
                      style: Theme.of(
                        context,
                      ).textTheme.bodySmall?.copyWith(fontWeight: FontWeight.bold, color: TColors.primary),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          // Actions
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => Get.toNamed('/edit-deal', arguments: deal),
                  icon: const Icon(Iconsax.edit, size: 16),
                  label: const Text('Edit'),
                ),
              ),
              const SizedBox(width: TSizes.spaceBtwItems),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => Get.toNamed('/deal-details', arguments: deal),
                  icon: const Icon(Iconsax.eye, size: 16),
                  label: const Text('View'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(DealStatus status) {
    switch (status) {
      case DealStatus.Draft:
        return Colors.grey;
      case DealStatus.PendingApproval:
        return Colors.orange;
      case DealStatus.Approved:
        return Colors.green;
      case DealStatus.Rejected:
        return Colors.red;
      case DealStatus.UnlockRequested:
        return Colors.purple;
      case DealStatus.Superseded:
        return Colors.brown;
      default:
        return Colors.grey;
    }
  }
}
