import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:flutter/material.dart';

import '../../models/product_model.dart';
import 'responsive_screens/edit_product_desktop.dart';

class EditProductScreen extends StatelessWidget {
  final ProductModel product; // Accept product via constructor
  const EditProductScreen({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    // Retrieve the product from arguments
    // final ProductModel product = Get.arguments as ProductModel; // Requires runtime cast and null check

    return TSiteTemplate(
      desktop: EditProductDesktop(product: product), // Pass to desktop layout
      tablet: EditProductDesktop(product: product),
      mobile: EditProductDesktop(product: product),
    );
  }
}
