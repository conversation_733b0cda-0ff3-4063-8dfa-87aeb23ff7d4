import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:flutter/material.dart';

import '../../models/product_model.dart';
import 'responsive_screens/enhanced_edit_product_desktop.dart';

class EditProductScreen extends StatelessWidget {
  final ProductModel product; // Accept product via constructor
  const EditProductScreen({super.key, required this.product});

  @override
  Widget build(BuildContext context) {
    return TSiteTemplate(
      desktop: EnhancedEditProductDesktop(product: product), // Enhanced form for all screen sizes
      tablet: EnhancedEditProductDesktop(product: product),
      mobile: EnhancedEditProductDesktop(product: product),
    );
  }
}
