import 'dart:typed_data';
import 'package:alloy/common/widgets/loaders/circular_loader.dart';
import 'package:alloy/common/media/repository/media_repository.dart';
import 'package:alloy/common/media/models/image_model.dart';
import 'package:alloy/common/media/widgets/media_content.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:alloy/utils/popups/dialogs.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dropzone/flutter_dropzone.dart';
import 'package:get/get.dart';

class MediaController extends GetxController {
  static MediaController get instance => Get.find();

  late DropzoneViewController dropzoneViewController;

  final RxBool showImagesUploaderSection = false.obs;
  final RxBool loading = false.obs;

  final int initialLoadCount = 20;
  final int loadMoreCount = 20;

  // To be used with the dropdown widget to select the media category
  final Rx<MediaCategory> selectedPath = MediaCategory.folders.obs;
  final RxList<ImageModel> selectedImagesToUpload = <ImageModel>[].obs;

  final RxList<ImageModel> allBannerImages = <ImageModel>[].obs;
  final RxList<ImageModel> allBrandImages = <ImageModel>[].obs;
  final RxList<ImageModel> allCategoryImages = <ImageModel>[].obs;
  final RxList<ImageModel> allProductImages = <ImageModel>[].obs;
  final RxList<ImageModel> allUserImages = <ImageModel>[].obs;
  final RxList<ImageModel> allImages = <ImageModel>[].obs;

  final MediaRepository mediaRepository = Get.put(MediaRepository());

  // Get Images
  Future<void> getMediaImages() async {
    try {
      loading.value = true;

      RxList<ImageModel> targetList = <ImageModel>[].obs;

      if (selectedPath.value == MediaCategory.banners && allBannerImages.isEmpty) {
        targetList = allBannerImages;
      } else if (selectedPath.value == MediaCategory.brands && allBrandImages.isEmpty) {
        targetList = allBrandImages;
      } else if (selectedPath.value == MediaCategory.categories && allCategoryImages.isEmpty) {
        targetList = allCategoryImages;
      } else if (selectedPath.value == MediaCategory.products && allProductImages.isEmpty) {
        targetList = allProductImages;
      } else if (selectedPath.value == MediaCategory.users && allUserImages.isEmpty) {
        targetList = allUserImages;
      }
      final images = await mediaRepository.fetchImagesFromDatabase(selectedPath.value.name, initialLoadCount);
      targetList.assignAll(images);

      loading.value = false;
    } catch (e) {
      loading.value = false;
      TLoaders.errorSnackBar(title: 'Oh Snap!!!', message: e.toString());
    }
  }

  // Get Images
  Future<void> loadMoreImages() async {
    try {
      loading.value = true;

      RxList<ImageModel> targetList = <ImageModel>[].obs;

      if (selectedPath.value == MediaCategory.banners && allBannerImages.isEmpty) {
        targetList = allBannerImages;
      } else if (selectedPath.value == MediaCategory.brands && allBrandImages.isEmpty) {
        targetList = allBrandImages;
      } else if (selectedPath.value == MediaCategory.categories && allCategoryImages.isEmpty) {
        targetList = allCategoryImages;
      } else if (selectedPath.value == MediaCategory.products && allProductImages.isEmpty) {
        targetList = allProductImages;
      } else if (selectedPath.value == MediaCategory.users && allUserImages.isEmpty) {
        targetList = allUserImages;
      }

      final images = await mediaRepository.loadMoreImagesFromDatabase(
        selectedPath.value.name,
        initialLoadCount,
        targetList.last.createdAt ?? DateTime.now(),
        // extract the date of the last loaded image from the original loaded images
      );
      targetList.addAll(images);

      loading.value = false;
    } catch (e) {
      loading.value = false;
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: 'Unable to fetch images. somthing went wrong. Try again');
    }
  }

  Future<void> selecLocalImages() async {
    final files = await dropzoneViewController.pickFiles(multiple: true, mime: ['image/png', 'image/jpeg', 'image/jpg']);
    if (files.isNotEmpty) {
      for (var file in files) {
        // Retrieve file data as Uint8List
        final bytes = await dropzoneViewController.getFileData(file);
        // Extract file metadata
        final filename = await dropzoneViewController.getFilename(file);
        final mimeType = await dropzoneViewController.getFileMIME(file);
        final image = ImageModel(url: '', contentType: mimeType, folder: '', filename: filename, localImageToDisplay: Uint8List.fromList(bytes));
        selectedImagesToUpload.add(image);
      }
    }
  }

  void uploadImagesConfirmation() {
    if (selectedPath.value == MediaCategory.folders) {
      TLoaders.warningSnackBar(title: 'Invalid Folder', message: 'Please select a media category.');
      return;
    }

    TDialogs.defaultDialog(
      context: Get.context!,
      onConfirm: () => uploadImages(),
      confirmText: 'Confirm',
      title: 'Upload Images',
      content: 'Are you sure you want to upload these images in ${selectedPath.value.name.toUpperCase()} folder?',
    );
  }

  /// Upload Images
  Future<void> uploadImages() async {
    try {
      // Remove confirmation box
      Get.back();
      // Start Loader
      uploadImagesLoader();
      // Get the selected category
      MediaCategory selectedCategory = selectedPath.value;
      // Get the corresponding list to update
      RxList<ImageModel> targetList;
      // Check the selected category and update the corresponding list
      switch (selectedCategory) {
        case MediaCategory.banners:
          targetList = allBannerImages;
          break;
        case MediaCategory.brands:
          targetList = allBrandImages;
          break;
        case MediaCategory.categories:
          targetList = allCategoryImages;
          break;
        case MediaCategory.products:
          targetList = allProductImages;
          break;
        case MediaCategory.users:
          targetList = allUserImages;
          break;
        default:
          return;
      }
      // Upload and add images to the target list
      // Using a reverse loop to avoid 'Concurrent modification during iteration' error
      for (int i = selectedImagesToUpload.length - 1; i >= 0; i--) {
        var selectedImage = selectedImagesToUpload[i];

        // Upload Image to the Storage
        final ImageModel uploadedImage = await mediaRepository.uploadImageFileInStorage(
          fileData: selectedImage.localImageToDisplay!,
          mimeType: selectedImage.contentType!,
          path: getSelectedPath(),
          imageName: selectedImage.filename,
        );
        // Upload Image to the Firestore
        uploadedImage.mediaCategory = selectedCategory.name;
        final id = await mediaRepository.uploadImageFileInFirestore(uploadedImage);
        uploadedImage.id = id;
        // Remove uploaded image from the list
        selectedImagesToUpload.removeAt(i);
        // Add uploaded image to the target list to display the lastest images
        targetList.add(uploadedImage);
      }
      // Stop Loader after successful upload
      TFullScreenLoader.stopLoading();
    } catch (e, stackTrace) {
      // Stop Loader in case of an error
      TFullScreenLoader.stopLoading();

      // !!! IMPORTANT: Print the full error and stack trace for debugging !!!
      debugPrint('Error uploading images: $e');
      debugPrint('Stack trace: $stackTrace');

      // Show a warning snack-bar for the error
      TLoaders.warningSnackBar(
        title: 'Error Uploading Images',
        // Display the actual error message if it's user-friendly, otherwise keep generic
        message: e.toString().contains('firebase') || e.toString().contains('Firebase')
            ? 'A Firebase related error occurred. Check console for details.'
            : 'Something went wrong while uploading your images. Details: ${e.toString()}',
      );
    }
  }

  // --- The uploadImagesLoader function ---
  void uploadImagesLoader() {
    showDialog(
      context: Get.context!, // Use Get.context for GetX-managed contexts
      barrierDismissible: false,
      builder: (context) => PopScope(
        // PopScope for controlling back button behavior
        canPop: false, // Prevents dialog from being dismissed by back button
        child: AlertDialog(
          title: const Text('Uploading Images'),
          content: Column(
            mainAxisSize: MainAxisSize.min, // Make column content wrap its children
            children: [
              Image.asset(
                TImages.uploadingImageIllustration, // Path to your uploading image/GIF
                height: 300,
                width: 300,
              ),
              const SizedBox(height: TSizes.spaceBtwItems),

              const Text('Sit Tight, Your images are uploading...'), // Message to the user
            ],
          ),
        ),
      ),
    );
  }

  // --- The getSelectedPath function ---
  String getSelectedPath() {
    String path = '';
    switch (selectedPath.value) {
      case MediaCategory.banners:
        path = TTexts.bannersStoragePath;
        break;
      case MediaCategory.brands:
        path = TTexts.brandsStoragePath;
        break;
      case MediaCategory.categories:
        path = TTexts.categoriesStoragePath;
        break;

      case MediaCategory.products:
        path = TTexts.productsStoragePath;
        break;
      case MediaCategory.users:
        path = TTexts.usersStoragePath;
        break;
      default:
        path = 'Others';
        break;
    }
    return path;
  }

  /// Popup Confirmation to remove cloud image
  void removeCloudImageConfirmation(ImageModel image) {
    TDialogs.defaultDialog(
      context: Get.context!,
      onConfirm: () {
        Get.back();
        removeCloudImage(image);
      },
      confirmText: 'Confirm',
      title: 'Remove Image',
      content: 'Are you sure you want to remove this image?',
    );
  }

  void removeCloudImage(ImageModel image) async {
    try {
      // Close the removeClouldImageConfirmaion() Dialog
      Get.back();

      // Show Loader
      Get.defaultDialog(
        title: 'Deleting Image',
        backgroundColor: Colors.transparent,
        content: const PopScope(canPop: false, child: SizedBox(width: 150, height: 150, child: TCircularLoader())),
        barrierDismissible: false,
      );

      // Delete Image
      await mediaRepository.deleteFileFromDatabase(image);

      // Get the corresponding list to update
      RxList<ImageModel> targetList;
      switch (selectedPath.value) {
        case MediaCategory.banners:
          targetList = allBannerImages;
          break;
        case MediaCategory.brands:
          targetList = allBrandImages;
          break;
        case MediaCategory.categories:
          targetList = allCategoryImages;
          break;
        case MediaCategory.products:
          targetList = allProductImages;
          break;
        case MediaCategory.users:
          targetList = allUserImages;
          break;
        default:
          return;
      }
      // Remove deleted image from the Rx list
      targetList.remove(image);
      update();

      // Stop Loader
      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: "${image.filename} Deleted", message: 'Image deleted successfully.');
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Error deleting image', message: e.toString());
    }
  }

  // Images Selection Bottom Sheet
  Future<List<ImageModel>?> selectImagesFromMedia({List<String>? selectedUrls, bool allowSelection = true, bool multipleSelection = false}) async {
    showImagesUploaderSection.value = true;

    List<ImageModel>? selectedImages = await Get.bottomSheet<List<ImageModel>>(
      isScrollControlled: true,
      backgroundColor: TColors.primaryBackground,
      FractionallySizedBox(
        heightFactor: 1,
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(TSizes.defaultSpace),
            child: Column(
              children: [MediaContent(alreadySelectedUrls: selectedUrls ?? [], allowSelection: allowSelection, allowMultipleSelection: multipleSelection)],
            ),
          ),
        ),
      ),
    );

    return selectedImages;
  }
}
