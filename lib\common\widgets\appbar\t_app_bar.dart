import 'package:alloy/common/widgets/appbar/logout_button.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TAppBar extends StatelessWidget implements PreferredSizeWidget {
  const TAppBar({
    super.key,
    this.title,
    this.showBackArrow = false,
    this.actions,
    this.addLogoutButton = true, // New property to optionally add logout
  });

  final Widget? title;
  final bool showBackArrow;
  final List<Widget>? actions;
  final bool addLogoutButton;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: TSizes.md),
      child: AppBar(
        automaticallyImplyLeading: false,
        leading: showBackArrow ? IconButton(onPressed: () => Get.back(), icon: const Icon(Icons.arrow_back_ios)) : null,
        title: title,
        actions: [
          ...(actions ?? []), // Existing actions
          if (addLogoutButton) const LogoutButton(), // Conditionally add logout button
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(TDeviceUtils.getAppBarHeight());
}
