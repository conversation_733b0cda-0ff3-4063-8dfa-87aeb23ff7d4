import 'package:alloy/features/products/controller/variant_controller/product_variant_manager_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../../../utils/validators/validation.dart';
import '../../../models/product_model.dart';

class ProductVariantCreationForm extends StatelessWidget {
  const ProductVariantCreationForm({super.key, required this.product}); // Now requires a product

  final ProductModel product; // The product for which variants are being created

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProductVariantManagerController>();

    return SingleChildScrollView(
      child: TRoundedContainer(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Form(
          key: controller.formKey, // Associate form key if you add validation to these fields
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Generate New Variants', style: Theme.of(context).textTheme.headlineSmall),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Materials Selection (Chips)
              AttributeSelectionWidget(
                title: 'Materials',
                options: controller.materialOptions,
                selectedOptions: controller.selectedMaterials,
                isMultiSelect: true,
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Thicknesses Selection (Chips + Custom Input)
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AttributeSelectionWidget(
                    title: 'Thicknesses',
                    options: controller.thicknessOptions,
                    selectedOptions: controller.selectedThicknesses,
                    isMultiSelect: true,
                  ),
                  const SizedBox(height: TSizes.spaceBtwInputFields),
                  TextFormField(
                    controller: controller.customThicknessController,
                    decoration: const InputDecoration(
                      labelText: 'Custom Thickness (e.g., 0.56mm)',
                      hintText: 'Enter custom thickness',
                      suffixIcon: Icon(Icons.straighten),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(decimal: true),
                    validator: (value) {
                      if (value != null && value.isNotEmpty) {
                        final cleanedValue = value.replaceAll('mm', '').trim();
                        if (!TValidator.isNumeric(cleanedValue)) {
                          // Use TValidator.isNumeric
                          return 'Please enter a valid number for thickness.';
                        }
                      }
                      return null;
                    },
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Finishes Selection (Chips)
              AttributeSelectionWidget(title: 'Finishes', options: controller.finishOptions, selectedOptions: controller.selectedFinishes, isMultiSelect: true),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Lengths Selection (Chips)
              AttributeSelectionWidget(title: 'Lengths', options: controller.lengthOptions, selectedOptions: controller.selectedLengths, isMultiSelect: true),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Quantities (Nullable, if user wants to pre-fill initial stock)
              Text('Initial Quantities (Optional)', style: Theme.of(context).textTheme.titleMedium),
              const SizedBox(height: TSizes.spaceBtwInputFields),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: controller.quantityOnHandController,
                      decoration: const InputDecoration(labelText: 'On Hand'),
                      keyboardType: TextInputType.number,
                      validator: TValidator.validateNumber,
                    ),
                  ),
                  const SizedBox(width: TSizes.spaceBtwInputFields),
                  Expanded(
                    child: TextFormField(
                      controller: controller.quantityOnOrderController,
                      decoration: const InputDecoration(labelText: 'On Order'),
                      keyboardType: TextInputType.number,
                      validator: TValidator.validateNumber,
                    ),
                  ),
                  const SizedBox(width: TSizes.spaceBtwInputFields),
                  Expanded(
                    child: TextFormField(
                      controller: controller.quantityInProductionController,
                      decoration: const InputDecoration(labelText: 'In Production'),
                      keyboardType: TextInputType.number,
                      validator: TValidator.validateNumber,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Create Variants Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  // Now calls the unified method, passing the specific product
                  onPressed: () => controller.generateAndSaveVariants(singleProduct: product),
                  child: const Text('Generate & Add Variants'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// --- Basic AttributeSelectionWidget (if you don't have one) ---
// lib/common/widgets/chips/attribute_selection_widget.dart
// You might already have a more sophisticated version.
// This is a minimal implementation to make the form compile.
class AttributeSelectionWidget extends StatelessWidget {
  const AttributeSelectionWidget({
    super.key,
    required this.title,
    required this.options,
    required this.selectedOptions,
    this.isMultiSelect = true, // Default to multi-select for chips
  });

  final String title;
  final List<String> options;
  final RxList<String> selectedOptions;
  final bool isMultiSelect;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: Theme.of(context).textTheme.titleMedium),
        const SizedBox(height: TSizes.spaceBtwItems),
        Obx(
          () => Wrap(
            spacing: 8.0, // horizontal spacing between chips
            runSpacing: 8.0, // vertical spacing between lines of chips
            children: options.map((option) {
              final isSelected = selectedOptions.contains(option);
              return ChoiceChip(
                label: Text(option),
                selected: isSelected,
                onSelected: (selected) {
                  if (isMultiSelect) {
                    if (selected) {
                      selectedOptions.add(option);
                    } else {
                      selectedOptions.remove(option);
                    }
                  } else {
                    // Single select behavior
                    selectedOptions.clear();
                    if (selected) {
                      selectedOptions.add(option);
                    }
                  }
                },
                selectedColor: Theme.of(context).primaryColor, // Example selected color
                backgroundColor: Theme.of(context).chipTheme.backgroundColor, // Default chip color
                labelStyle: TextStyle(color: isSelected ? Colors.white : Theme.of(context).textTheme.bodyMedium!.color), // Text color
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
