import 'package:alloy/features/permissions/controllers/permission_controller.dart';
import 'package:alloy/features/permissions/models/module_permission_model.dart';
import 'package:alloy/features/permissions/repository/permission_repository.dart';
import 'package:get/get.dart';

/// Service class for permission-related business logic
class PermissionService {
  static PermissionService get instance => Get.find();

  final _permissionController = PermissionController.instance;
  final _permissionRepository = PermissionRepository.instance;

  /// Check if current user can access a specific route
  bool canAccessRoute(String route) {
    return _permissionController.hasRouteAccess(route);
  }

  /// Check if current user has permission for a module action
  bool hasPermission(AppModule module, PermissionType permission) {
    return _permissionController.hasPermission(module, permission);
  }

  /// Check if current user has any access to a module
  bool hasModuleAccess(AppModule module) {
    return _permissionController.hasModuleAccess(module);
  }

  /// Check if current user can view a specific module
  bool canView(AppModule module) {
    return hasPermission(module, PermissionType.view);
  }

  /// Check if current user can create in a specific module
  bool canCreate(AppModule module) {
    return hasPermission(module, PermissionType.create);
  }

  /// Check if current user can edit in a specific module
  bool canEdit(AppModule module) {
    return hasPermission(module, PermissionType.edit);
  }

  /// Check if current user can delete in a specific module
  bool canDelete(AppModule module) {
    return hasPermission(module, PermissionType.delete);
  }

  /// Check if current user can approve in a specific module
  bool canApprove(AppModule module) {
    return hasPermission(module, PermissionType.approve);
  }

  /// Check if current user can export from a specific module
  bool canExport(AppModule module) {
    return hasPermission(module, PermissionType.export);
  }

  /// Check if current user can import to a specific module
  bool canImport(AppModule module) {
    return hasPermission(module, PermissionType.import);
  }

  /// Check if current user can manage a specific module
  bool canManage(AppModule module) {
    return hasPermission(module, PermissionType.manage);
  }

  /// Check if current user has department access
  bool hasDepartmentAccess(String department) {
    return _permissionController.hasDepartmentAccess(department);
  }

  /// Check if current user is admin
  bool get isAdmin => _permissionController.isAdmin;

  /// Check if current user is manager or above
  bool get isManagerOrAbove => _permissionController.isManagerOrAbove;

  /// Check if current user is super admin
  bool get isSuperAdmin => _permissionController.isSuperAdmin;

  /// Get available departments for current user
  List<String> get availableDepartments => _permissionController.availableDepartments;

  /// Check if user can perform bulk operations on a module
  bool canPerformBulkOperations(AppModule module) {
    return _permissionController.canPerformBulkOperations(module);
  }

  /// Check if user can export data from module
  bool canExportData(AppModule module) {
    return _permissionController.canExportData(module);
  }

  /// Check if user can import data to module
  bool canImportData(AppModule module) {
    return _permissionController.canImportData(module);
  }

  /// Get permissions for current user for a specific module
  List<PermissionType> getModulePermissions(AppModule module) {
    return _permissionController.getModulePermissions(module);
  }

  /// Check if current user can access user management
  bool canAccessUserManagement() {
    return canView(AppModule.users);
  }

  /// Check if current user can manage users
  bool canManageUsers() {
    return canManage(AppModule.users);
  }

  /// Check if current user can access settings
  bool canAccessSettings() {
    return canView(AppModule.settings);
  }

  /// Check if current user can manage settings
  bool canManageSettings() {
    return canManage(AppModule.settings);
  }

  /// Check if current user can access dashboard
  bool canAccessDashboard() {
    return canView(AppModule.dashboard);
  }

  /// Check if current user can access reports
  bool canAccessReports() {
    return canView(AppModule.reports);
  }

  /// Check if current user can access deals
  bool canAccessDeals() {
    return canView(AppModule.deals);
  }

  /// Check if current user can create deals
  bool canCreateDeals() {
    return canCreate(AppModule.deals);
  }

  /// Check if current user can approve deals
  bool canApproveDeals() {
    return canApprove(AppModule.deals);
  }

  /// Check if current user can access accounts
  bool canAccessAccounts() {
    return canView(AppModule.accounts);
  }

  /// Check if current user can create accounts
  bool canCreateAccounts() {
    return canCreate(AppModule.accounts);
  }

  /// Check if current user can access contacts
  bool canAccessContacts() {
    return canView(AppModule.contacts);
  }

  /// Check if current user can create contacts
  bool canCreateContacts() {
    return canCreate(AppModule.contacts);
  }

  /// Check if current user can access products
  bool canAccessProducts() {
    return canView(AppModule.products);
  }

  /// Check if current user can create products
  bool canCreateProducts() {
    return canCreate(AppModule.products);
  }

  /// Check if current user can access categories
  bool canAccessCategories() {
    return canView(AppModule.categories);
  }

  /// Check if current user can access orders
  bool canAccessOrders() {
    return canView(AppModule.orders);
  }

  /// Check if current user can access inventory
  bool canAccessInventory() {
    return canView(AppModule.inventory);
  }

  /// Check if current user can access human resources
  bool canAccessHumanResources() {
    return canView(AppModule.humanResources);
  }

  /// Check if current user can access workers
  bool canAccessWorkers() {
    return canView(AppModule.workers);
  }

  /// Check if current user can access media
  bool canAccessMedia() {
    return canView(AppModule.media);
  }

  /// Check if current user can access banners
  bool canAccessBanners() {
    return canView(AppModule.banners);
  }

  /// Check if current user can access brands
  bool canAccessBrands() {
    return canView(AppModule.brands);
  }

  /// Check if current user can access profile
  bool canAccessProfile() {
    return canView(AppModule.profile);
  }

  /// Check if user has permission for a specific action on a specific resource
  /// This is useful for fine-grained permission checking
  Future<bool> checkUserPermissionAsync(String userId, AppModule module, PermissionType permission) async {
    return await _permissionRepository.checkUserPermission(userId, module, permission);
  }

  /// Check if user has any permission for a module (async version)
  Future<bool> checkUserModuleAccessAsync(String userId, AppModule module) async {
    return await _permissionRepository.checkUserModuleAccess(userId, module);
  }

  /// Get permission summary for display purposes
  Map<AppModule, List<PermissionType>> getUserPermissionSummary(String userId) {
    return _permissionController.getUserPermissionSummary(userId);
  }

  /// Check if current user can perform a specific action based on business rules
  bool canPerformAction(String action, {AppModule? module, String? department}) {
    // Define action to permission mapping
    const actionPermissionMap = {
      'view_dashboard': {AppModule.dashboard: PermissionType.view},
      'create_account': {AppModule.accounts: PermissionType.create},
      'edit_account': {AppModule.accounts: PermissionType.edit},
      'delete_account': {AppModule.accounts: PermissionType.delete},
      'create_contact': {AppModule.contacts: PermissionType.create},
      'edit_contact': {AppModule.contacts: PermissionType.edit},
      'delete_contact': {AppModule.contacts: PermissionType.delete},
      'create_deal': {AppModule.deals: PermissionType.create},
      'edit_deal': {AppModule.deals: PermissionType.edit},
      'approve_deal': {AppModule.deals: PermissionType.approve},
      'create_product': {AppModule.products: PermissionType.create},
      'edit_product': {AppModule.products: PermissionType.edit},
      'manage_users': {AppModule.users: PermissionType.manage},
      'manage_settings': {AppModule.settings: PermissionType.manage},
      'export_data': {AppModule.reports: PermissionType.export},
      'import_data': {AppModule.accounts: PermissionType.import}, // Default to accounts, can be overridden
    };

    final actionMapping = actionPermissionMap[action];
    if (actionMapping == null) return false;

    final targetModule = module ?? actionMapping.keys.first;
    final requiredPermission = actionMapping[targetModule]!;

    // Check basic permission
    bool hasBasicPermission = hasPermission(targetModule, requiredPermission);

    // Check department access if specified
    if (department != null && hasBasicPermission) {
      hasBasicPermission = hasDepartmentAccess(department);
    }

    return hasBasicPermission;
  }

  /// Get all modules that current user has access to
  List<AppModule> getAccessibleModules() {
    final accessibleModules = <AppModule>[];
    
    for (final module in AppModule.values) {
      if (hasModuleAccess(module)) {
        accessibleModules.add(module);
      }
    }
    
    return accessibleModules;
  }

  /// Get all permissions current user has for a specific module
  List<PermissionType> getAvailablePermissions(AppModule module) {
    return getModulePermissions(module);
  }

  /// Check if current user can access admin features
  bool canAccessAdminFeatures() {
    return isAdmin || canManageUsers() || canManageSettings();
  }

  /// Check if current user can access management features
  bool canAccessManagementFeatures() {
    return isManagerOrAbove || canApproveDeals() || canManageUsers();
  }
}
