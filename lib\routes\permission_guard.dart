import 'package:alloy/features/permissions/controllers/permission_controller.dart';
import 'package:alloy/features/permissions/models/module_permission_model.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Middleware for checking module-based permissions on routes
class PermissionGuard extends GetMiddleware {
  final AppModule? requiredModule;
  final PermissionType? requiredPermission;
  final bool requireAnyPermission;

  PermissionGuard({this.requiredModule, this.requiredPermission, this.requireAnyPermission = true});

  @override
  int? get priority => 3; // Run after AuthGuard (1) and AdminGuard (2)

  @override
  RouteSettings? redirect(String? route) {
    try {
      final permissionController = PermissionController.instance;

      // If no specific module is required, check route-based access
      if (requiredModule == null) {
        if (!permissionController.hasRouteAccess(route ?? '')) {
          _showAccessDeniedMessage();
          return const RouteSettings(name: TRoutes.profile);
        }
        return null;
      }

      // Check specific module and permission requirements
      bool hasAccess = false;

      if (requiredPermission != null) {
        // Check for specific permission
        hasAccess = permissionController.hasPermission(requiredModule!, requiredPermission!);
      } else if (requireAnyPermission) {
        // Check for any permission in the module
        hasAccess = permissionController.hasModuleAccess(requiredModule!);
      }

      if (!hasAccess) {
        _showAccessDeniedMessage();
        return const RouteSettings(name: TRoutes.profile);
      }

      return null;
    } catch (e) {
      // On error, deny access for security
      _showAccessDeniedMessage();
      return const RouteSettings(name: TRoutes.profile);
    }
  }

  void _showAccessDeniedMessage() {
    TLoaders.warningSnackBar(title: 'Access Denied', message: 'You do not have permission to access this page.');
  }
}

/// Specific permission guards for common use cases

/// Guard for dashboard access
class DashboardGuard extends PermissionGuard {
  DashboardGuard() : super(requiredModule: AppModule.dashboard, requiredPermission: PermissionType.view);
}

/// Guard for user management access
class UserManagementGuard extends PermissionGuard {
  UserManagementGuard() : super(requiredModule: AppModule.users, requiredPermission: PermissionType.view);
}

/// Guard for settings access
class SettingsGuard extends PermissionGuard {
  SettingsGuard() : super(requiredModule: AppModule.settings, requiredPermission: PermissionType.view);
}

/// Guard for media management access
class MediaGuard extends PermissionGuard {
  MediaGuard() : super(requiredModule: AppModule.media, requiredPermission: PermissionType.view);
}

/// Guard for banner management access
class BannerGuard extends PermissionGuard {
  BannerGuard() : super(requiredModule: AppModule.banners, requiredPermission: PermissionType.view);
}

/// Guard for accounts module access
class AccountsGuard extends PermissionGuard {
  AccountsGuard() : super(requiredModule: AppModule.accounts, requiredPermission: PermissionType.view);
}

/// Guard for creating accounts
class CreateAccountGuard extends PermissionGuard {
  CreateAccountGuard() : super(requiredModule: AppModule.accounts, requiredPermission: PermissionType.create);
}

/// Guard for contacts module access
class ContactsGuard extends PermissionGuard {
  ContactsGuard() : super(requiredModule: AppModule.contacts, requiredPermission: PermissionType.view);
}

/// Guard for creating contacts
class CreateContactGuard extends PermissionGuard {
  CreateContactGuard() : super(requiredModule: AppModule.contacts, requiredPermission: PermissionType.create);
}

/// Guard for deals module access
class DealsGuard extends PermissionGuard {
  DealsGuard() : super(requiredModule: AppModule.deals, requiredPermission: PermissionType.view);
}

/// Guard for creating deals
class CreateDealGuard extends PermissionGuard {
  CreateDealGuard() : super(requiredModule: AppModule.deals, requiredPermission: PermissionType.create);
}

/// Guard for editing deals
class EditDealGuard extends PermissionGuard {
  EditDealGuard() : super(requiredModule: AppModule.deals, requiredPermission: PermissionType.edit);
}

/// Guard for products module access
class ProductsGuard extends PermissionGuard {
  ProductsGuard() : super(requiredModule: AppModule.products, requiredPermission: PermissionType.view);
}

/// Guard for creating products
class CreateProductGuard extends PermissionGuard {
  CreateProductGuard() : super(requiredModule: AppModule.products, requiredPermission: PermissionType.create);
}

/// Guard for categories module access
class CategoriesGuard extends PermissionGuard {
  CategoriesGuard() : super(requiredModule: AppModule.categories, requiredPermission: PermissionType.view);
}

/// Guard for orders module access
class OrdersGuard extends PermissionGuard {
  OrdersGuard() : super(requiredModule: AppModule.orders, requiredPermission: PermissionType.view);
}

/// Guard for inventory module access
class InventoryGuard extends PermissionGuard {
  InventoryGuard() : super(requiredModule: AppModule.inventory, requiredPermission: PermissionType.view);
}

/// Guard for reports module access
class ReportsGuard extends PermissionGuard {
  ReportsGuard() : super(requiredModule: AppModule.reports, requiredPermission: PermissionType.view);
}

/// Guard for human resources access
class HumanResourcesGuard extends PermissionGuard {
  HumanResourcesGuard() : super(requiredModule: AppModule.humanResources, requiredPermission: PermissionType.view);
}

/// Guard for workers management access
class WorkersGuard extends PermissionGuard {
  WorkersGuard() : super(requiredModule: AppModule.workers, requiredPermission: PermissionType.view);
}

/// Guard for brands module access
class BrandsGuard extends PermissionGuard {
  BrandsGuard() : super(requiredModule: AppModule.brands, requiredPermission: PermissionType.view);
}

/// Guard for import operations
class ImportGuard extends PermissionGuard {
  final AppModule module;

  ImportGuard(this.module) : super(requiredModule: module, requiredPermission: PermissionType.import);
}

/// Guard for export operations
class ExportGuard extends PermissionGuard {
  final AppModule module;

  ExportGuard(this.module) : super(requiredModule: module, requiredPermission: PermissionType.export);
}

/// Guard for approval operations
class ApprovalGuard extends PermissionGuard {
  final AppModule module;

  ApprovalGuard(this.module) : super(requiredModule: module, requiredPermission: PermissionType.approve);
}

/// Guard for management operations
class ManagementGuard extends PermissionGuard {
  final AppModule module;

  ManagementGuard(this.module) : super(requiredModule: module, requiredPermission: PermissionType.manage);
}

/// Helper class for creating permission guards
class PermissionGuards {
  /// Create a guard for specific module and permission
  static PermissionGuard create(AppModule module, PermissionType permission) {
    return PermissionGuard(requiredModule: module, requiredPermission: permission);
  }

  /// Create a guard that requires any permission for a module
  static PermissionGuard createForModule(AppModule module) {
    return PermissionGuard(requiredModule: module, requireAnyPermission: true);
  }

  /// Create a guard for route-based access (uses route-to-module mapping)
  static PermissionGuard createForRoute() {
    return PermissionGuard();
  }
}
