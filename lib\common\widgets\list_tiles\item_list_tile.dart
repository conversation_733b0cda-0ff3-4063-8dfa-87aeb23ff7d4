import 'package:alloy/common/widgets/data_table/table_action_buttons.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';

class TItemListTile extends StatelessWidget {
  const TItemListTile({
    super.key,
    this.leading,
    required this.title,
    this.subTitle,
    this.trailing,
    this.onEditPressed,
    this.onDeletePressed,
  });

  final Widget? leading;
  final String title;
  final String? subTitle;
  final Widget? trailing;
  final VoidCallback? onEditPressed;
  final VoidCallback? onDeletePressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(TSizes.sm),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(TSizes.cardRadiusLg),
      ),
      child: Row(
        children: [
          if (leading != null) ...[leading!, const SizedBox(width: TSizes.spaceBtwItems)],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(title, style: Theme.of(context).textTheme.titleMedium),
                if (subTitle != null) Text(subTitle!, style: Theme.of(context).textTheme.labelMedium),
              ],
            ),
          ),
          if (trailing != null) ...[const SizedBox(width: TSizes.spaceBtwItems), trailing!],
          TTableActionButtons(onEditPressed: onEditPressed, onDeletePressed: onDeletePressed),
        ],
      ),
    );
  }
}
