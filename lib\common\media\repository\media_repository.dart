import 'dart:io';
import 'package:alloy/common/media/models/image_model.dart';
import 'package:alloy/utils/exceptions/firebase_exceptions.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:alloy/utils/exceptions/format_exceptions.dart';
import 'package:alloy/utils/exceptions/platform_exceptions.dart';

class MediaRepository extends GetxController {
  static MediaRepository instance = Get.find();

  // Firebase Storage Instance
  final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Upload any Image using Uint8List (compatible with DropzoneFileInterface)
  Future<ImageModel> uploadImageFileInStorage({required Uint8List fileData, required String mimeType, required String path, required String imageName}) async {
    try {
      // Reference to the storage location
      final Reference ref = _storage.ref('$path/$imageName');
      // Upload file using Uint8List
      final UploadTask uploadTask = ref.putData(fileData, SettableMetadata(contentType: mimeType));
      // Wait for the upload to complete
      final TaskSnapshot snapshot = await uploadTask.whenComplete(() => {});
      // Get download URL
      final String downloadURL = await snapshot.ref.getDownloadURL();
      // Fetch metadata
      final FullMetadata metadata = await ref.getMetadata();
      return ImageModel.fromFirebaseMetadata(metadata, path, imageName, downloadURL);
    } on FirebaseException catch (e) {
      throw e.message!;
    } on SocketException catch (e) {
      throw e.message;
    } on PlatformException catch (e) {
      throw e.message!;
    } catch (e) {
      throw e.toString();
    }
  }

  /// Upload Image data in Firestore
  Future<String> uploadImageFileInFirestore(ImageModel image) async {
    try {
      final data = await FirebaseFirestore.instance.collection('images').add(image.toJson());
      return data.id;
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Fetch Images from Firestore based on media category and load count
  Future<List<ImageModel>> fetchImagesFromDatabase(String mediaCategory, int loadCount) async {
    try {
      final querySnapshot = await FirebaseFirestore.instance
          .collection('images')
          .where('mediaCategory', isEqualTo: mediaCategory)
          .orderBy('createdAt', descending: true)
          .limit(loadCount)
          .get();
      return querySnapshot.docs.map((doc) => ImageModel.fromSnapshot(doc)).toList();
    } on FirebaseException catch (e) {
      // --- ADD THIS LINE FOR DETAILED DEBUGGING ---
      print('FirebaseException Code: ${e.code}');
      print('FirebaseException Message: ${e.message}');
      print('FirebaseException StackTrace: ${e.stackTrace}');
      // --- END ADDITION ---
      throw "Firebase Exception ${TFirebaseException(e.code).message}";
    } on FormatException catch (_) {
      throw const TFormatException();
    } on PlatformException catch (e) {
      throw "Platform Exception ${TPlatformException(e.code).message}";
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Load more Images from Firestore based on media category, load count and last fetched data
  Future<List<ImageModel>> loadMoreImagesFromDatabase(String mediaCategory, int loadCount, DateTime lastFetchedDate) async {
    try {
      final querySnapshot = await FirebaseFirestore.instance
          .collection('images')
          .where('mediaCategory', isEqualTo: mediaCategory)
          .orderBy('createdAt', descending: true)
          .startAfter([lastFetchedDate])
          .limit(loadCount)
          .get();
      return querySnapshot.docs.map((doc) => ImageModel.fromSnapshot(doc)).toList();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  // Delete file from Firebase Storage and corresponsing document from Firestore
  Future<void> deleteFileFromDatabase(ImageModel image) async {
    try {
      // Not deleting using URL because it might have accesscode changed
      // So using fullPath to delete
      await _storage.ref(image.fullPath).delete();
      await FirebaseFirestore.instance.collection('images').doc(image.id).delete();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on FormatException catch (_) {
      throw const TFormatException();
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw e.toString();
    }
  }
}
