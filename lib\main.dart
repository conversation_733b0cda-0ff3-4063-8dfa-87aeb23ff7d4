import 'package:alloy/app.dart';
import 'package:alloy/firebase_options.dart';
import 'package:dynamic_path_url_strategy/dynamic_path_url_strategy.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize GetStorage
  await GetStorage.init();

  // Initialize Firebase
  // await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform).then((value) {
  //   // Ensure GeneralBindings is available for Get.find() to work // in case menu item is not highlighted
  //   // Get.put(SidebarController()); // Explicitly put it here or ensure it's in GeneralBindings
  //   Get.put(AuthenticationRepository()); // This will then call screenRedirect
  // });

  // -- Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Remove # sign from url
  setPathUrlStrategy();

  runApp(const MyApp());
}
