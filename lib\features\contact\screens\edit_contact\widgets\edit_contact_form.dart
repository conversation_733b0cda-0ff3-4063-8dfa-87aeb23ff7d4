import 'package:alloy/common/widgets/forms/enhanced_text_field.dart';
import 'package:alloy/common/widgets/forms/form_section.dart';
import 'package:alloy/features/contact/controller/edit_contact_controller.dart';
import 'package:alloy/common/widgets/chips/rounded_choice_chips.dart';
import 'package:alloy/features/contact/screens/create_contact/widgets/account_selection_dialog.dart';
import 'package:alloy/features/contact/screens/create_contact/widgets/handler_selection_dialog.dart';
import 'package:alloy/features/contact/models/contact_model.dart';

import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class EditContactForm extends StatelessWidget {
  const EditContactForm({super.key, required this.contact});

  final ContactModel contact;

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EditContactController());
    controller.initializeForm(contact);

    return Center(
      child: Container(
        constraints: const BoxConstraints(maxWidth: 800),
        decoration: BoxDecoration(
          color: TColors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(color: TColors.darkGrey.withValues(alpha: 0.1), blurRadius: 10, offset: const Offset(0, 4)),
          ],
        ),
        child: Form(
          key: controller.formKey,
          child: Column(
            children: [
              // Header Section
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: TColors.primary.withValues(alpha: 0.05),
                  borderRadius: const BorderRadius.only(topLeft: Radius.circular(16), topRight: Radius.circular(16)),
                ),
                child: Column(
                  children: [
                    Icon(Iconsax.edit, size: 48, color: TColors.primary),
                    const SizedBox(height: 12),
                    Text(
                      'Edit Contact',
                      style: Theme.of(
                        context,
                      ).textTheme.headlineSmall?.copyWith(color: TColors.primary, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Update contact information and assignments',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: TColors.darkGrey),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Form Content
              Padding(
                padding: const EdgeInsets.all(24),
                child: Column(
                  children: [
                    // Section 1: Basic Information
                    TFormSection(
                      title: 'Basic Information',
                      icon: Iconsax.user,
                      children: [
                        TEnhancedTextField(
                          controller: controller.name,
                          labelText: 'Full Name',
                          hintText: 'Enter contact full name',
                          prefixIcon: Iconsax.user,
                          validator: (value) => TValidator.validateEmptyText('Name', value),
                        ),
                        TEnhancedTextField(
                          controller: controller.title,
                          labelText: 'Title',
                          hintText: 'Enter contact title (optional)',
                          prefixIcon: Iconsax.crown,
                        ),
                        TEnhancedTextField(
                          controller: controller.designation,
                          labelText: 'Designation',
                          hintText: 'Enter contact designation (optional)',
                          prefixIcon: Iconsax.briefcase,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Section 2: Contact Information
                    TFormSection(
                      title: 'Contact Information',
                      icon: Iconsax.call,
                      children: [
                        TEnhancedTextField(
                          controller: controller.email,
                          labelText: 'Email Address',
                          hintText: 'Enter email address (optional)',
                          prefixIcon: Iconsax.sms,
                          validator: (value) => TValidator.validateEmail(value),
                        ),
                        TEnhancedTextField(
                          controller: controller.phone,
                          labelText: 'Phone Number',
                          hintText: 'Enter phone number',
                          prefixIcon: Iconsax.call,
                          validator: (value) => TValidator.validatePhoneNumber(value),
                        ),
                        TEnhancedTextField(
                          controller: controller.landline,
                          labelText: 'Landline',
                          hintText: 'Enter landline number (optional)',
                          prefixIcon: Iconsax.call_calling,
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Section 3: Account & Handler Assignment
                    TFormSection(
                      title: 'Account & Handler Assignment',
                      icon: Iconsax.people,
                      children: [
                        // Account Selection
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Iconsax.building, size: 20, color: TColors.primary),
                                const SizedBox(width: 8),
                                Text(
                                  'Associated Accounts',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                                ),
                                const Spacer(),
                                TextButton.icon(
                                  onPressed: () => _showAccountSelectionDialog(context, controller),
                                  icon: const Icon(Iconsax.add_circle, size: 18),
                                  label: const Text('Select Accounts'),
                                  style: TextButton.styleFrom(
                                    foregroundColor: TColors.primary,
                                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: TColors.lightGrey.withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: TColors.borderPrimary.withValues(alpha: 0.1)),
                              ),
                              child: Obx(
                                () => controller.selectedAccounts.isEmpty
                                    ? Text(
                                        'No accounts selected',
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: TColors.darkGrey,
                                          fontStyle: FontStyle.italic,
                                        ),
                                      )
                                    : Wrap(
                                        spacing: 8,
                                        runSpacing: 8,
                                        children: controller.selectedAccounts
                                            .map(
                                              (account) => TChoiceChip(
                                                text: account.name,
                                                selected: true,
                                                color: TColors.primary,
                                                onSelected: (_) => controller.selectedAccounts.remove(account),
                                              ),
                                            )
                                            .toList(),
                                      ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),

                        // Handler Selection
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Iconsax.user_tag, size: 20, color: TColors.success),
                                const SizedBox(width: 8),
                                Text(
                                  'Assigned Handlers',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                                ),
                                const Spacer(),
                                TextButton.icon(
                                  onPressed: () => _showHandlerSelectionDialog(context, controller),
                                  icon: const Icon(Iconsax.add_circle, size: 18),
                                  label: const Text('Select Handlers'),
                                  style: TextButton.styleFrom(
                                    foregroundColor: TColors.success,
                                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: TColors.lightGrey.withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: TColors.borderPrimary.withValues(alpha: 0.1)),
                              ),
                              child: Obx(
                                () => controller.selectedHandlers.isEmpty
                                    ? Text(
                                        'No handlers assigned',
                                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                          color: TColors.darkGrey,
                                          fontStyle: FontStyle.italic,
                                        ),
                                      )
                                    : Wrap(
                                        spacing: 8,
                                        runSpacing: 8,
                                        children: controller.selectedHandlers
                                            .map(
                                              (handler) => TChoiceChip(
                                                text: handler.fullName,
                                                selected: true,
                                                color: TColors.success,
                                                onSelected: (_) => controller.selectedHandlers.remove(handler),
                                              ),
                                            )
                                            .toList(),
                                      ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(height: 24),

                    // Section 4: Status Settings
                    TFormSection(
                      title: 'Status Settings',
                      icon: Iconsax.status,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: TColors.lightGrey.withValues(alpha: 0.3),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: TColors.borderPrimary.withValues(alpha: 0.1)),
                          ),
                          child: Row(
                            children: [
                              Icon(Iconsax.status, color: TColors.primary, size: 20),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Contact Status',
                                      style: Theme.of(
                                        context,
                                      ).textTheme.titleSmall?.copyWith(fontWeight: FontWeight.w600),
                                    ),
                                    Text(
                                      'Enable or disable this contact',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: TColors.darkGrey),
                                    ),
                                  ],
                                ),
                              ),
                              Obx(
                                () => Switch(
                                  value: controller.active.value,
                                  onChanged: (value) => controller.active.value = value,
                                  activeColor: TColors.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 32),

                    // Action Buttons
                    Row(
                      children: [
                        Expanded(
                          child: OutlinedButton(
                            onPressed: () => Get.back(),
                            style: OutlinedButton.styleFrom(
                              foregroundColor: TColors.darkGrey,
                              side: BorderSide(color: TColors.borderPrimary),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: () => controller.updateContact(contact),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: TColors.primary,
                              foregroundColor: TColors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Update Contact'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAccountSelectionDialog(BuildContext context, EditContactController controller) {
    Get.dialog(
      AccountSelectionDialog(
        initialSelectedAccounts: controller.selectedAccounts.toList(),
        onAccountsSelected: (selectedAccounts) {
          controller.selectedAccounts.assignAll(selectedAccounts);
        },
      ),
    );
  }

  void _showHandlerSelectionDialog(BuildContext context, EditContactController controller) {
    Get.dialog(
      HandlerSelectionDialog(
        initialSelectedHandlers: controller.selectedHandlers.toList(),
        onHandlersSelected: (selectedHandlers) {
          controller.selectedHandlers.assignAll(selectedHandlers);
        },
      ),
    );
  }
}
