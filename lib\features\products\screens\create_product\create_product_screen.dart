import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/products/screens/create_product/responsive_screens/create_product_desktop.dart';
import 'package:flutter/material.dart';

class CreateProductScreen extends StatelessWidget {
  const CreateProductScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(
      desktop: CreateProductDesktop(),
      tablet: CreateProductDesktop(),
      mobile: CreateProductDesktop(), // For now, use desktop layout for all
    );
  }
}
