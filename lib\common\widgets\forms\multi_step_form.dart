import 'package:flutter/material.dart';
import '../../../utils/constants/sizes.dart';
import '../containers/rounded_container.dart';

/// Multi-step form wizard with progress indicators and navigation
class TMultiStepForm extends StatefulWidget {
  const TMultiStepForm({
    super.key,
    required this.steps,
    required this.onCompleted,
    this.onStepChanged,
    this.initialStep = 0,
    this.showProgressIndicator = true,
    this.allowStepNavigation = true,
    this.validateOnNext = true,
    this.showStepNumbers = true,
    this.progressIndicatorType = ProgressIndicatorType.linear,
  });

  final List<FormStep> steps;
  final VoidCallback onCompleted;
  final void Function(int step)? onStepChanged;
  final int initialStep;
  final bool showProgressIndicator;
  final bool allowStepNavigation;
  final bool validateOnNext;
  final bool showStepNumbers;
  final ProgressIndicatorType progressIndicatorType;

  @override
  State<TMultiStepForm> createState() => _TMultiStepFormState();
}

class _TMultiStepFormState extends State<TMultiStepForm> {
  late int _currentStep;
  final List<bool> _stepValidationStatus = [];

  @override
  void initState() {
    super.initState();
    _currentStep = widget.initialStep;
    _stepValidationStatus.addAll(List.filled(widget.steps.length, false));
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.showProgressIndicator) _buildProgressIndicator(),
        const SizedBox(height: TSizes.spaceBtwSections),
        Expanded(child: _buildCurrentStep()),
        const SizedBox(height: TSizes.spaceBtwSections),
        _buildNavigationButtons(),
      ],
    );
  }

  Widget _buildProgressIndicator() {
    switch (widget.progressIndicatorType) {
      case ProgressIndicatorType.linear:
        return _buildLinearProgress();
      case ProgressIndicatorType.dots:
        return _buildDotsProgress();
      case ProgressIndicatorType.stepper:
        return _buildStepperProgress();
    }
  }

  Widget _buildLinearProgress() {
    final progress = (_currentStep + 1) / widget.steps.length;
    final theme = Theme.of(context);

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Step ${_currentStep + 1} of ${widget.steps.length}',
              style: theme.textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
            ),
            Text(
              '${(progress * 100).toInt()}%',
              style: theme.textTheme.bodyMedium?.copyWith(color: theme.primaryColor, fontWeight: FontWeight.w600),
            ),
          ],
        ),
        const SizedBox(height: TSizes.spaceBtwItems),
        LinearProgressIndicator(
          value: progress,
          backgroundColor: theme.dividerColor,
          valueColor: AlwaysStoppedAnimation<Color>(theme.primaryColor),
        ),
      ],
    );
  }

  Widget _buildDotsProgress() {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(widget.steps.length, (index) {
        final isActive = index == _currentStep;
        final isCompleted = index < _currentStep;
        final isClickable = widget.allowStepNavigation && (isCompleted || _stepValidationStatus[index]);

        return GestureDetector(
          onTap: isClickable ? () => _goToStep(index) : null,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 4),
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isActive
                  ? theme.primaryColor
                  : isCompleted
                  ? theme.primaryColor.withValues(alpha: 0.7)
                  : theme.dividerColor,
            ),
          ),
        );
      }),
    );
  }

  Widget _buildStepperProgress() {
    final theme = Theme.of(context);

    return Row(
      children: List.generate(widget.steps.length, (index) {
        final isActive = index == _currentStep;
        final isCompleted = index < _currentStep;
        final isClickable = widget.allowStepNavigation && (isCompleted || _stepValidationStatus[index]);

        return Expanded(
          child: Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: isClickable ? () => _goToStep(index) : null,
                  child: Column(
                    children: [
                      Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: isActive
                              ? theme.primaryColor
                              : isCompleted
                              ? theme.primaryColor.withValues(alpha: 0.7)
                              : theme.dividerColor,
                        ),
                        child: Center(
                          child: isCompleted
                              ? const Icon(Icons.check, color: Colors.white, size: 16)
                              : Text(
                                  '${index + 1}',
                                  style: TextStyle(
                                    color: isActive || isCompleted ? Colors.white : theme.textTheme.bodyMedium?.color,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 14,
                                  ),
                                ),
                        ),
                      ),
                      const SizedBox(height: TSizes.xs),
                      Text(
                        widget.steps[index].title,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: isActive ? theme.primaryColor : theme.textTheme.bodySmall?.color,
                          fontWeight: isActive ? FontWeight.w600 : FontWeight.normal,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
              if (index < widget.steps.length - 1)
                Container(
                  height: 2,
                  width: 20,
                  color: isCompleted ? theme.primaryColor.withValues(alpha: 0.7) : theme.dividerColor,
                ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildCurrentStep() {
    final currentFormStep = widget.steps[_currentStep];

    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            currentFormStep.title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600),
          ),
          if (currentFormStep.subtitle != null) ...[
            const SizedBox(height: TSizes.xs),
            Text(
              currentFormStep.subtitle!,
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Theme.of(context).textTheme.bodySmall?.color),
            ),
          ],
          const SizedBox(height: TSizes.spaceBtwSections),
          Expanded(child: currentFormStep.content),
        ],
      ),
    );
  }

  Widget _buildNavigationButtons() {
    final theme = Theme.of(context);
    final isFirstStep = _currentStep == 0;
    final isLastStep = _currentStep == widget.steps.length - 1;

    return Row(
      children: [
        if (!isFirstStep)
          Expanded(
            child: OutlinedButton(onPressed: _goToPreviousStep, child: const Text('Previous')),
          ),
        if (!isFirstStep) const SizedBox(width: TSizes.spaceBtwItems),
        Expanded(
          flex: isFirstStep ? 1 : 1,
          child: ElevatedButton(
            onPressed: isLastStep ? _completeForm : _goToNextStep,
            child: Text(isLastStep ? 'Complete' : 'Next'),
          ),
        ),
      ],
    );
  }

  void _goToStep(int step) {
    if (step >= 0 && step < widget.steps.length) {
      setState(() {
        _currentStep = step;
      });
      widget.onStepChanged?.call(step);
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      _goToStep(_currentStep - 1);
    }
  }

  void _goToNextStep() {
    if (widget.validateOnNext) {
      final isValid = widget.steps[_currentStep].validator?.call() ?? true;
      _stepValidationStatus[_currentStep] = isValid;

      if (!isValid) return;
    }

    if (_currentStep < widget.steps.length - 1) {
      _goToStep(_currentStep + 1);
    }
  }

  void _completeForm() {
    if (widget.validateOnNext) {
      final isValid = widget.steps[_currentStep].validator?.call() ?? true;
      _stepValidationStatus[_currentStep] = isValid;

      if (!isValid) return;
    }

    widget.onCompleted();
  }
}

/// Individual step in the multi-step form
class FormStep {
  final String title;
  final String? subtitle;
  final Widget content;
  final bool Function()? validator;
  final bool isOptional;

  FormStep({required this.title, this.subtitle, required this.content, this.validator, this.isOptional = false});
}

enum ProgressIndicatorType { linear, dots, stepper }
