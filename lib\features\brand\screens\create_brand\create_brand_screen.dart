import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/brand/screens/create_brand/responsive_screens/create_brand_desktop.dart';
import 'package:alloy/features/brand/screens/create_brand/responsive_screens/create_brand_mobile.dart';
import 'package:alloy/features/brand/screens/create_brand/responsive_screens/create_brand_tablet.dart';
import 'package:flutter/material.dart';

class CreateBrandScreen extends StatelessWidget {
  const CreateBrandScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(
      useLayout: true,
      desktop: CreateBrandDesktop(),
      tablet: CreateBrandTablet(),
      mobile: CreateBrandMobile(),
    );
  }
}
