import 'package:alloy/features/authentication/repository/authentication_repository.dart';
import 'package:alloy/features/authentication/repository/user_repository.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/image_strings.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class LoginController extends GetxController {
  static LoginController get instance => Get.find();

  final _authRepository = AuthenticationRepository.instance;

  final hidePassword = true.obs;
  final rememberMe = true.obs;
  final localStorage = GetStorage();

  final email = TextEditingController();
  final password = TextEditingController();
  final loginFormKey = GlobalKey<FormState>();

  // Focus Node for password field
  final FocusNode passwordFocusNode = FocusNode(); // <<< NEW

  @override
  void onInit() {
    email.text = localStorage.read('REMEMBER_ME_EMAIL') ?? '';
    password.text = localStorage.read('REMEMBER_ME_PASSWORD') ?? '';

    // For testing purposes, you might keep these commented out or remove them for production
    // email.text = '<EMAIL>';
    // password.text = 'Ds@123';
    super.onInit();
  }

  // Register Admin
  void registerAdmin() async {
    try {
      // Start Loader
      TFullScreenLoader.openLoadingDialog('Registering Admin', TImages.defaultLoaderAnimation);

      // Check Internet Connectivity
      if (!await NetworkManager.instance.isConnected()) {
        TLoaders.customToast(message: 'No Internet Connection');
        TFullScreenLoader.stopLoading();
        return;
      }

      // Register Admin
      // await _authRepository.registerWithEmailAndPassword(TTexts.adminEmail, TTexts.adminPassword, UserModel(email: TTexts.adminEmail, firstName: 'Admin', department: 'IT', role: AppRole.admin)); // Example for one-time admin registration

      // Create admin record in the Firestore
      final userRepository = UserRepository.instance;
      await userRepository.createUser(
        UserModel(
          id: FirebaseAuth.instance.currentUser!.uid,
          email: TTexts.adminEmail,
          firstName: 'Admin',
          lastName: 'Admin',
          userName: 'Admin',
          department: 'IT',
          phoneNumber: '0000000000',
          roles: [UserRole.Admin], // Changed to list of UserRole
          createdAt: DateTime.now(),
        ),
      );

      // Stop Loader
      TFullScreenLoader.stopLoading();
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  // Login
  void loginWithEmailAndPassword() async {
    try {
      // Start Loader
      TFullScreenLoader.openLoadingDialog('Logging In', TImages.defaultLoaderAnimation);

      // Check Internet Connectivity
      if (!await NetworkManager.instance.isConnected()) {
        TLoaders.customToast(message: 'No Internet Connection');
        TFullScreenLoader.stopLoading();
        return;
      }

      // Form Validation
      if (!loginFormKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Save Data if Remember Me is Checked
      if (rememberMe.value) {
        localStorage.write('REMEMBER_ME_EMAIL', email.text.trim());
        localStorage.write('REMEMBER_ME_PASSWORD', password.text.trim());
      }

      // Login
      await _authRepository.loginWithEmailAndPassword("${email.text.trim()}@gmail.com", password.text.trim());

      // Fetch User Data
      final user = await UserController.instance.getUserData();

      // Stop Loader
      TFullScreenLoader.stopLoading();

      // Redirect based on user role
      if (user.roles.contains(UserRole.Admin)) {
        Get.offAllNamed(TRoutes.dashboard); // Redirect admin to Dashboard
      } else {
        Get.offAllNamed(TRoutes.profile); // Redirect non-admin to Profile Screen
      }
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'NOh Snap!', message: e.toString());
    }
  }

  @override
  void onClose() {
    // Dispose of controllers and focus nodes
    email.dispose();
    password.dispose();
    passwordFocusNode.dispose(); // <<< DISPOSE FOCUS NODE
    super.onClose();
  }
}
