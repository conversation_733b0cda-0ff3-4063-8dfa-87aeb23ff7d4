import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../common/widgets/data_table/table_header.dart';
import '../../../../../utils/constants/sizes.dart';
import '../../../controllers/deal_controller.dart';
import '../data_table/deals_table.dart';
import '../widgets/deals_filter_panel.dart';
import '../widgets/sales_pipeline_widget.dart';

class DealsDesktop extends StatelessWidget {
  const DealsDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DealController());

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Page Header
              Row(
                children: [
                  Icon(Iconsax.document, size: 28),
                  const SizedBox(width: TSizes.spaceBtwItems),
                  Text('Deal Management', style: Theme.of(context).textTheme.headlineMedium),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Filter Panel
              const DealsFilterPanel(),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Sales Pipeline
              const SalesPipelineWidget(),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Table Body
              TRoundedContainer(
                child: Column(
                  children: [
                    // Table Header
                    TTableHeader(
                      hintText: 'Search Deals (Client, Deal Number, Sales Person)',
                      searchOnChanged: (query) => controller.searchQuery(query),
                      actions: [
                        ElevatedButton.icon(
                          onPressed: () => Get.toNamed('/create-deal'),
                          icon: const Icon(Iconsax.add),
                          label: const Text('Create New Deal'),
                        ),
                      ],
                    ),
                    const SizedBox(height: TSizes.spaceBtwItems),

                    // Table
                    Obx(() {
                      // Show Loader
                      if (controller.isLoading.value) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(TSizes.defaultSpace),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }

                      // Show Empty State
                      if (controller.filteredItems.isEmpty) {
                        return SizedBox(
                          height: 400,
                          child: Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Iconsax.document, size: 64, color: Colors.grey[400]),
                                const SizedBox(height: TSizes.spaceBtwItems),
                                Text(
                                  'No deals found',
                                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: Colors.grey[600]),
                                ),
                                const SizedBox(height: TSizes.spaceBtwItems / 2),
                                Text(
                                  'Create your first deal to get started',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
                                ),
                                const SizedBox(height: TSizes.spaceBtwItems),
                                ElevatedButton.icon(
                                  onPressed: () => Get.toNamed('/create-deal'),
                                  icon: const Icon(Iconsax.add),
                                  label: const Text('Create Deal'),
                                ),
                              ],
                            ),
                          ),
                        );
                      }

                      // Show Table
                      return const DealsTable();
                    }),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
