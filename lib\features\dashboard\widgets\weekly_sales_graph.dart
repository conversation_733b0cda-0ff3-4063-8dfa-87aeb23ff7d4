import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/controllers/dashboard/dashboard_controller.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TWeeklySalesGraph extends StatelessWidget {
  const TWeeklySalesGraph({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(DashboardController());

    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('Weekly Sales', style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: TSizes.spaceBtwSections),

          /// Graph
          SizedBox(
            height: 400, // Fixed height for the chart
            child: Obx(
              // Use Obx to react to changes in controller.weeklySales
              () => BarChart(
                BarChartData(
                  titlesData: buildFlTitlesData(), // Defines axis titles
                  borderData: FlBorderData(
                    show: true,
                    border: const Border(
                      top: BorderSide.none,
                      right: BorderSide.none,
                      // You might want to define left and bottom borders explicitly
                      // left: BorderSide(color: Colors.black, width: 1),
                      // bottom: BorderSide(color: Colors.black, width: 1),
                    ),
                  ),
                  gridData: FlGridData(
                    show: true,
                    drawHorizontalLine: true,
                    drawVerticalLine: false, // Only horizontal grid lines
                    horizontalInterval: 200, // Interval for horizontal grid lines
                  ),
                  barGroups: controller.weeklySales
                      .asMap()
                      .entries
                      .map(
                        (entry) => BarChartGroupData(
                          x: entry.key, // x-axis value (day index)
                          barRods: [
                            BarChartRodData(
                              toY: entry.value, // y-axis value (sales amount)
                              width: 30, // Width of each bar
                              color: TColors.primary, // Bar color
                              borderRadius: BorderRadius.circular(TSizes.sm), // Rounded corners for bars
                            ),
                          ],
                        ),
                      )
                      .toList(), // Convert the iterable to a List<BarChartGroupData>
                  groupsSpace: TSizes.spaceBtwItems, // Space between bar groups
                  barTouchData: BarTouchData(
                    touchTooltipData: BarTouchTooltipData(
                      getTooltipColor: (_) => TColors.secondary,
                      // tooltipBgColor: TColors.secondary, // Tooltip background color

                      /// THIS MAY BE NOT REQIRED
                      getTooltipItem: (group, groupIndex, rod, rodIndex) {
                        String day;
                        switch (group.x.toInt()) {
                          case 0:
                            day = 'Mon';
                            break;
                          case 1:
                            day = 'Tue';
                            break;
                          case 2:
                            day = 'Wed';
                            break;
                          case 3:
                            day = 'Thu';
                            break;
                          case 4:
                            day = 'Fri';
                            break;
                          case 5:
                            day = 'Sat';
                            break;
                          case 6:
                            day = 'Sun';
                            break;
                          default:
                            day = '';
                            break;
                        }
                        return BarTooltipItem(
                          '$day\n',
                          const TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 14),
                          children: <TextSpan>[
                            TextSpan(
                              text: '\$${rod.toY.toStringAsFixed(2)}',
                              style: const TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.w500),
                            ),
                          ],
                        );
                      },
                    ),
                    touchCallback: (FlTouchEvent event, barTouchResponse) {
                      // This part handles touch events, e.g., for desktop or mobile
                      // if (TDeviceUtils.isDesktopScreen(context) && barTouchResponse != null) {
                      //   // Handle desktop specific touch response
                      // } else {
                      //   // Handle mobile or general touch response
                      // }
                      // The original code had:
                      // touchCallback: (TDeviceUtils.isDesktopScreen(context) ? (BarTouchEvent? event, BarTouchResponse? barResponse) {}: null),
                      // This implies a conditional touch handler. For simplicity, we can provide a default
                      // or keep it null if no special desktop handling is needed.
                      // For now, it's simplified to a placeholder or null if not implemented.
                    },
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

FlTitlesData buildFlTitlesData() {
  return FlTitlesData(
    show: true,
    bottomTitles: AxisTitles(
      sideTitles: SideTitles(
        showTitles: true,
        reservedSize: 25,
        getTitlesWidget: (value, meta) {
          // Map index to the desired day of the week
          final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];

          // Calculate the index and ensure it wraps around for the correct day
          final index = value.toInt() % days.length;

          // Get the day corresponding to the calculated index
          final day = days[index];

          return SideTitleWidget(meta: meta, child: Text(day));
        },
      ),
    ),
    leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: true, reservedSize: 35)),
    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
  );
}
