import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/authentication/screens/reset_password/responsive_screens/reset_password_screen_desktop_tablet.dart';
import 'package:alloy/features/authentication/screens/reset_password/responsive_screens/reset_password_screen_mobile.dart';
import 'package:flutter/material.dart';

class ResetPasswordScreen extends StatelessWidget {
  const ResetPasswordScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(
      useLayout: false,
      desktop: ResetPasswordScreenDesktopTablet(),
      mobile: ResetPasswordScreenMobile(),
    );
  }
}
