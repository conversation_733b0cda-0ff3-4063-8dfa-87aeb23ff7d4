// --- OrderModel Class ---
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../utils/constants/enums.dart';
import '../../../utils/helpers/helper_functions.dart';

class OldOrderModel {
  final String id;
  final String userId;
  final String docId;
  OrderStatus status;
  final double totalAmount;
  final double shippingCost;
  final double taxCost;
  final DateTime orderDate;
  final String paymentMethod;
  final DateTime? deliveryDate;
  final bool billingAddressSameAsShipping;

  OldOrderModel({
    required this.id,
    required this.userId,
    required this.docId,
    required this.status,
    required this.totalAmount,
    required this.shippingCost,
    required this.taxCost,
    required this.orderDate,
    this.paymentMethod = 'Cash on Delivery',
    this.deliveryDate,
    this.billingAddressSameAsShipping = true,
  });

  // --- Getters for formatted data ---
  String get formattedOrderDate => THelperFunctions.getFormattedDate(orderDate);

  String get formattedDeliveryDate => deliveryDate != null ? THelperFunctions.getFormattedDate(deliveryDate!) : '';

  // create empty ordermodel
  static OldOrderModel empty() => OldOrderModel(
    id: '',
    userId: '',
    docId: '',
    status: OrderStatus.pending,
    totalAmount: 0.0,
    shippingCost: 0.0,
    taxCost: 0.0,
    orderDate: DateTime.now(),
  );

  String get orderStatusText {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.shipped:
        return 'Shipment on the way';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
    }
  }

  // --- Static Factory Constructor to create an OrderModel from a Map ---
  factory OldOrderModel.fromMap(String id, Map<String, dynamic> map) {
    return OldOrderModel(
      id: id,
      userId: map['userId'] as String? ?? '',
      docId: map['docId'] as String? ?? '',
      status: OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == map['status'],
        orElse: () => OrderStatus.pending,
      ),
      totalAmount: (map['totalAmount'] as num? ?? 0.0).toDouble(),
      shippingCost: (map['shippingCost'] as num? ?? 0.0).toDouble(),
      taxCost: (map['taxCost'] as num? ?? 0.0).toDouble(),
      orderDate: (map['orderDate'] as Timestamp).toDate(),
      paymentMethod: map['paymentMethod'] as String? ?? 'Cash on Delivery',
      deliveryDate: map['deliveryDate'] != null ? (map['deliveryDate'] as Timestamp).toDate() : null,
      billingAddressSameAsShipping: map['billingAddressSameAsShipping'] as bool? ?? true,
    );
  }

  // --- Static Factory Constructor to create an OrderModel from a DocumentSnapshot ---
  factory OldOrderModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (document.data() == null) {
      throw StateError('Document data is null for ID: ${document.id}');
    }
    return OldOrderModel.fromMap(document.id, document.data()!);
  }

  // --- Method to convert an OrderModel instance to a Map (for Firestore) ---
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'docId': docId,
      'status': status.toString().split('.').last,
      'totalAmount': totalAmount,
      'shippingCost': shippingCost,
      'taxCost': taxCost,
      'orderDate': Timestamp.fromDate(orderDate),
      'paymentMethod': paymentMethod,
      'deliveryDate': deliveryDate != null ? Timestamp.fromDate(deliveryDate!) : null,
      'billingAddressSameAsShipping': billingAddressSameAsShipping,
    };
  }
}
