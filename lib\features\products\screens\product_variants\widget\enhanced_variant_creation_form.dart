import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/features/products/controller/variant_controller/product_variant_manager_controller.dart';
import 'package:alloy/features/products/models/product_model.dart';
import 'package:alloy/features/products/screens/product_variants/widget/visual_variant_builder.dart';

/// Visual variant creation form - streamlined interface
class EnhancedVariantCreationForm extends StatelessWidget {
  const EnhancedVariantCreationForm({super.key, required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context),
          const SizedBox(height: TSizes.spaceBtwSections),

          // Visual Builder Content
          Expanded(child: VisualVariantBuilder(product: product)),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        // Icon and Title
        Container(
          padding: const EdgeInsets.all(TSizes.sm),
          decoration: BoxDecoration(
            color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(TSizes.cardRadiusSm),
          ),
          child: Icon(Icons.tune, color: Theme.of(context).primaryColor, size: 24),
        ),
        const SizedBox(width: TSizes.spaceBtwItems),

        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Variant Creation Studio',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
              ),
              Text(
                'Create variants for ${product.name}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),

        // Quick Stats
        _buildQuickStats(context),
      ],
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    final controller = Get.find<ProductVariantManagerController>();

    return Obx(() {
      final selectedCount =
          controller.selectedMaterials.length +
          controller.selectedThicknesses.length +
          controller.selectedFinishes.length +
          controller.selectedLengths.length;

      final totalCombinations = _calculateTotalCombinations(controller);

      return Row(
        children: [
          _buildStatChip(
            context,
            icon: Icons.check_circle,
            label: 'Selected',
            value: selectedCount.toString(),
            color: Colors.green,
          ),
          const SizedBox(width: TSizes.spaceBtwItems / 2),
          _buildStatChip(
            context,
            icon: Icons.auto_awesome,
            label: 'Combinations',
            value: totalCombinations.toString(),
            color: Colors.blue,
          ),
        ],
      );
    });
  }

  Widget _buildStatChip(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(color: color, fontWeight: FontWeight.bold, fontSize: 14),
          ),
          const SizedBox(width: 2),
          Text(label, style: TextStyle(color: color, fontSize: 12)),
        ],
      ),
    );
  }

  int _calculateTotalCombinations(ProductVariantManagerController controller) {
    final materials = controller.selectedMaterials.length;
    final thicknesses = controller.selectedThicknesses.length;
    final finishes = controller.selectedFinishes.length;
    final lengths = controller.selectedLengths.length;

    if (materials == 0 || thicknesses == 0 || finishes == 0 || lengths == 0) {
      return 0;
    }

    return materials * thicknesses * finishes * lengths;
  }
}

/// Enhanced variant creation mode selector
class VariantCreationModeSelector extends StatelessWidget {
  const VariantCreationModeSelector({super.key, required this.selectedMode, required this.onModeChanged});

  final VariantCreationMode selectedMode;
  final ValueChanged<VariantCreationMode> onModeChanged;

  @override
  Widget build(BuildContext context) {
    return TRoundedContainer(
      backgroundColor: Colors.grey[50]!,
      child: Row(
        children: VariantCreationMode.values.map((mode) {
          final isSelected = selectedMode == mode;

          return Expanded(
            child: GestureDetector(
              onTap: () => onModeChanged(mode),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                margin: const EdgeInsets.all(4),
                padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                decoration: BoxDecoration(
                  color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
                  borderRadius: BorderRadius.circular(TSizes.cardRadiusSm),
                  boxShadow: isSelected
                      ? [
                          BoxShadow(
                            color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ]
                      : null,
                ),
                child: Column(
                  children: [
                    Icon(mode.icon, color: isSelected ? Colors.white : Colors.grey[600], size: 24),
                    const SizedBox(height: TSizes.spaceBtwItems / 2),
                    Text(
                      mode.title,
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.grey[600],
                        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        fontSize: 12,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// Variant creation modes
enum VariantCreationMode {
  visual('Visual Builder', Icons.auto_awesome),
  traditional('Traditional', Icons.list_alt),
  bulk('Bulk Creation', Icons.batch_prediction);

  const VariantCreationMode(this.title, this.icon);

  final String title;
  final IconData icon;
}

/// Variant creation tips widget
class VariantCreationTips extends StatelessWidget {
  const VariantCreationTips({super.key});

  @override
  Widget build(BuildContext context) {
    return TRoundedContainer(
      backgroundColor: Colors.blue[50]!,
      borderColor: Colors.blue[200]!,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.blue[600], size: 20),
              const SizedBox(width: TSizes.spaceBtwItems / 2),
              Text(
                'Pro Tips',
                style: TextStyle(color: Colors.blue[600], fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),

          ..._tips.map(
            (tip) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.only(top: 6),
                    width: 4,
                    height: 4,
                    decoration: BoxDecoration(color: Colors.blue[600], shape: BoxShape.circle),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(tip, style: TextStyle(color: Colors.blue[700], fontSize: 12)),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  static const List<String> _tips = [
    'Drag attributes between pools to quickly build combinations',
    'Use the preview grid to see all possible variants before generation',
    'Select specific combinations instead of generating all possibilities',
    'Set initial quantities to pre-populate inventory levels',
  ];
}
