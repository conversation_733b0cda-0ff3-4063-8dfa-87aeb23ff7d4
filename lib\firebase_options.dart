// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBBSI9oQuxC3e_ONoT8zfVy3k9mmcYuCo0',
    appId: '1:109986796455:web:744683ce3ccdf4ed451c82',
    messagingSenderId: '109986796455',
    projectId: 'bonn-alloy',
    authDomain: 'bonn-alloy.firebaseapp.com',
    storageBucket: 'bonn-alloy.firebasestorage.app',
    measurementId: 'G-JZPNBRGGHP',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCXK2PfHO0ygRbpQXk-q3zo0pb62nP_PTs',
    appId: '1:109986796455:android:a9695be45de7aa68451c82',
    messagingSenderId: '109986796455',
    projectId: 'bonn-alloy',
    storageBucket: 'bonn-alloy.firebasestorage.app',
  );

}