import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/forms/form_section.dart';
import 'package:alloy/common/widgets/forms/enhanced_text_field.dart';
import 'package:alloy/common/widgets/forms/enhanced_dropdown.dart';
import 'package:alloy/features/account/screens/create_account/widgets/enhanced_contact_multiselect.dart';
import 'package:alloy/features/account/screens/create_account/widgets/enhanced_parent_dropdown.dart';
import 'package:alloy/features/account/screens/create_account/widgets/enhanced_industry_selector.dart';
import 'package:alloy/features/account/screens/create_account/widgets/enhanced_handler_selector.dart';
import 'package:alloy/features/account/screens/create_account/widgets/enhanced_toggle_button.dart';
import 'package:alloy/features/account/controller/create_account_controller.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../address/screens/add_edit_address_popup.dart';

class CreateAccountForm extends StatelessWidget {
  const CreateAccountForm({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CreateAccountController());
    final userController = UserController.instance; // Assuming this is correctly initialized elsewhere

    // TODO: Replace with actual current user ID from authentication
    const String currentUserId = 'user_001';

    return TRoundedContainer(
      child: Form(
        key: controller.formKey,
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Section 1: Basic Information
              TFormSection(
                title: 'Basic Information',
                icon: Iconsax.user,
                isRequired: true,
                children: [
                  TEnhancedTextField(
                    controller: controller.name,
                    labelText: 'Account Name',
                    hintText: 'Enter the account name',
                    prefixIcon: Iconsax.building_3,
                    isRequired: true,
                    validator: (value) => TValidator.validateEmptyText('Account Name', value),
                  ),
                  TEnhancedTextField(
                    controller: controller.alsoKnownAs,
                    labelText: 'Also Known As',
                    hintText: 'Alternative name or abbreviation',
                    prefixIcon: Iconsax.user_tag,
                    helperText: 'Optional: Enter any alternative names or abbreviations',
                  ),
                  TEnhancedTextField(
                    controller: controller.email,
                    labelText: 'Email Address',
                    hintText: 'Enter primary email address',
                    prefixIcon: Iconsax.sms,
                    validator: (value) => TValidator.validateEmail(value),
                  ),
                  TEnhancedTextField(
                    controller: controller.phone,
                    labelText: 'Phone Number',
                    hintText: 'Enter primary phone number',
                    prefixIcon: Iconsax.call,
                    validator: (value) => TValidator.validatePhoneNumber(value),
                  ),
                  TEnhancedTextField(
                    controller: controller.paymentTerms,
                    labelText: 'Payment Terms',
                    hintText: 'e.g., Net 30, COD, etc.',
                    prefixIcon: Iconsax.money_send,
                    helperText: 'Specify payment terms and conditions',
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Section 2: Business Configuration
              TFormSection(
                title: 'Business Configuration',
                icon: Iconsax.building_3,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Obx(
                          () => TEnhancedDropdown<BusinessType>(
                            labelText: 'Business Type',
                            items: BusinessType.values,
                            value: controller.businessType.value,
                            onChanged: (value) => controller.businessType.value = value!,
                            prefixIcon: Iconsax.building_3,
                            displayStringForOption: (type) => type.name.capitalizeFirst!,
                          ),
                        ),
                      ),
                      const SizedBox(width: TSizes.spaceBtwInputFields),
                      Expanded(
                        child: Obx(
                          () => TEnhancedDropdown<PriorityLevel>(
                            labelText: 'Priority Level',
                            items: PriorityLevel.values,
                            value: controller.priority.value,
                            onChanged: (value) => controller.priority.value = value!,
                            prefixIcon: Iconsax.flash_1,
                            displayStringForOption: (level) => level.name.capitalizeFirst!,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Obx(
                    () => TEnhancedDropdown<AccountStatus>(
                      labelText: 'Account Status',
                      items: AccountStatus.values,
                      value: controller.status.value,
                      onChanged: (value) => controller.status.value = value!,
                      prefixIcon: Iconsax.activity,
                      displayStringForOption: (status) => status.name.capitalizeFirst!,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Section 3: Account Hierarchy
              TFormSection(
                title: 'Account Hierarchy',
                icon: Iconsax.hierarchy_square_2,
                children: [
                  TEnhancedToggleButton(
                    value: controller.isParent,
                    onChanged: (value) {
                      controller.isParent.value = value;
                      // If it's a parent, it cannot have a parent itself
                      if (controller.isParent.value) {
                        controller.selectedParentAccount.value = null;
                      }
                    },
                    labelText: 'Parent Account Status',
                    subtitle: 'Determine if this account can have child accounts',
                    trueLabel: 'Is Parent Account',
                    falseLabel: 'Regular Account',
                    trueIcon: Iconsax.hierarchy_square_2,
                    falseIcon: Iconsax.building_3,
                    helperText: 'Parent accounts can have other accounts assigned under them',
                    style: ToggleButtonStyle.card,
                  ),
                  // Parent Account Selection (conditionally visible)
                  Obx(
                    () => Visibility(
                      visible: !controller.isParent.value,
                      child: TEnhancedParentDropdown(
                        parentAccounts: controller.parentAccountOptions,
                        selectedParent: controller.selectedParentAccount,
                        onParentChanged: (parent) => controller.selectedParentAccount.value = parent,
                        labelText: 'Assign Parent Account',
                        hintText: 'Search and select a parent account',
                        helperText: 'Optional: Select a parent account if this is a sub-account',
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Section 4: Industry & Business Details
              TFormSection(
                title: 'Industry & Business Details',
                icon: Iconsax.building_4,
                children: [
                  TEnhancedIndustrySelector(
                    selectedIndustries: controller.selectedIndustries,
                    onIndustriesChanged: (industries) => controller.selectedIndustries.assignAll(industries),
                    labelText: 'Business Industries',
                    hintText: 'Select relevant industries for this account',
                    helperText: 'Choose industries that best describe this account\'s business',
                    maxSelections: 5,
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Section 5: Team & Contacts
              TFormSection(
                title: 'Team & Contacts',
                icon: Iconsax.people,
                children: [
                  TEnhancedHandlerSelector(
                    availableHandlers: userController.allItems,
                    selectedHandlers: controller.selectedHandlers,
                    onHandlersChanged: (handlers) => controller.selectedHandlers.assignAll(handlers),
                    labelText: 'Assign Account Handlers',
                    helperText: 'Select team members who will handle this account',
                    maxSelections: 3,
                  ),
                  TEnhancedContactMultiSelect(
                    selectedContacts: controller.selectedContacts,
                    onContactsChanged: (contacts) => controller.selectedContacts.assignAll(contacts),
                    labelText: 'Associate Contacts',
                    hintText: 'Search and select contacts for this account',
                    helperText: 'Add contacts who are associated with this account',
                  ),
                ],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Section 6: Address Management
              TFormSection(
                title: 'Address Management',
                icon: Iconsax.location,
                children: [
                  Row(
                    children: [
                      Expanded(child: Text('Account Addresses', style: Theme.of(context).textTheme.titleMedium)),
                      ElevatedButton.icon(
                        onPressed: () {
                          Get.dialog(
                            AddEditAddressPopup(
                              accountId: 'new_account_temp_id',
                              createdByUserId: currentUserId,
                              onSave: (address) => controller.addAddress(address),
                            ),
                          );
                        },
                        icon: const Icon(Iconsax.add),
                        label: const Text('Add Address'),
                      ),
                    ],
                  ),
                  const SizedBox(height: TSizes.sm),
                  Obx(() {
                    if (controller.accountAddresses.isEmpty) {
                      return Container(
                        padding: const EdgeInsets.all(TSizes.md),
                        decoration: BoxDecoration(
                          border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
                          borderRadius: BorderRadius.circular(TSizes.inputFieldRadius),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Iconsax.info_circle,
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
                              size: 20,
                            ),
                            const SizedBox(width: TSizes.sm),
                            Text(
                              'No addresses added yet. Add at least one address.',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                          ],
                        ),
                      );
                    }
                    return ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: controller.accountAddresses.length,
                      itemBuilder: (_, index) {
                        final address = controller.accountAddresses[index];
                        return Card(
                          key: ValueKey(address.id),
                          margin: const EdgeInsets.only(bottom: TSizes.spaceBtwItems),
                          child: Padding(
                            padding: const EdgeInsets.all(TSizes.sm),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        '${address.type} Address',
                                        style: Theme.of(context).textTheme.titleMedium,
                                      ),
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text('Default', style: Theme.of(context).textTheme.bodySmall),
                                            const SizedBox(width: 4),
                                            Switch(
                                              value: address.isDefault,
                                              onChanged: (value) => controller.toggleDefaultAddress(address),
                                              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                                            ),
                                          ],
                                        ),
                                        const SizedBox(width: 8),
                                        IconButton(
                                          icon: const Icon(Iconsax.edit_2),
                                          onPressed: () {
                                            Get.dialog(
                                              AddEditAddressPopup(
                                                initialAddress: address,
                                                accountId: 'new_account_temp_id',
                                                createdByUserId: currentUserId,
                                                onSave: (updatedAddress) => controller.updateAddress(updatedAddress),
                                              ),
                                            );
                                          },
                                        ),
                                        IconButton(
                                          icon: const Icon(Iconsax.trash, color: Colors.red),
                                          onPressed: () => controller.removeAddress(address),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                const SizedBox(height: TSizes.xs),
                                Text(address.toString(), style: Theme.of(context).textTheme.bodySmall),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  }),
                  // Denormalized location fields (read-only)
                  const SizedBox(height: TSizes.md),
                  Text(
                    'Location Summary (Auto-populated from default address)',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  const SizedBox(height: TSizes.sm),
                  Row(
                    children: [
                      Expanded(
                        child: Obx(
                          () => TEnhancedTextField(
                            controller: TextEditingController(text: controller.denormalizedCountry.value),
                            labelText: 'Country',
                            prefixIcon: Iconsax.global,
                            enabled: false,
                            helperText: 'Auto-populated from default billing address',
                          ),
                        ),
                      ),
                      const SizedBox(width: TSizes.spaceBtwInputFields),
                      Expanded(
                        child: Obx(
                          () => TEnhancedTextField(
                            controller: TextEditingController(text: controller.denormalizedEmirates.value),
                            labelText: 'Emirates/State',
                            prefixIcon: Iconsax.map_1,
                            enabled: false,
                            helperText: 'Auto-populated from default billing address',
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),

              const SizedBox(height: TSizes.spaceBtwSections),

              // Submit Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => controller.createAccount(),
                  style: ElevatedButton.styleFrom(padding: const EdgeInsets.all(TSizes.md)),
                  child: const Text('Create Account'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
