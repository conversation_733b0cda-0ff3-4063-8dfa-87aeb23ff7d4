import 'package:alloy/common/widgets/chips/rounded_choice_chips.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/features/account/screens/create_account/widgets/contact_selection_dialog.dart';
import 'package:alloy/features/account/controller/create_account_controller.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../address/screens/add_edit_address_popup.dart';
import '../../../models/account_model.dart';

class CreateAccountForm extends StatelessWidget {
  const CreateAccountForm({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CreateAccountController());
    final userController = UserController.instance; // Assuming this is correctly initialized elsewhere

    // TODO: Replace with actual current user ID from authentication
    const String currentUserId = 'user_001';

    return TRoundedContainer(
      width: 600,
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Account\'s Information', style: Theme.of(context).textTheme.headlineMedium),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Name & Also Known As
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controller.name,
                    validator: (value) => TValidator.validateEmptyText('Name', value),
                    decoration: const InputDecoration(
                      labelText: 'Account Name*',
                      prefixIcon: Icon(Iconsax.user_octagon),
                    ),
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwInputFields),
                Expanded(
                  child: TextFormField(
                    controller: controller.alsoKnownAs,
                    decoration: const InputDecoration(
                      labelText: 'Also Known As (Optional)',
                      prefixIcon: Icon(Iconsax.user_octagon),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),

            // Email
            TextFormField(
              controller: controller.email,
              decoration: const InputDecoration(labelText: 'Email', prefixIcon: Icon(Iconsax.sms)),
              validator: (value) => TValidator.validateEmail(value),
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),

            // Phone & Payment Terms
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controller.phone,
                    decoration: const InputDecoration(labelText: 'Phone (Optional)', prefixIcon: Icon(Iconsax.call)),
                    keyboardType: TextInputType.phone,
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwInputFields),
                Expanded(
                  child: TextFormField(
                    controller: controller.paymentTerms,
                    decoration: const InputDecoration(
                      labelText: 'Payment Terms (Optional)',
                      prefixIcon: Icon(Iconsax.timer_1),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),

            // Business Type, Priority, Status
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => DropdownButtonFormField<BusinessType>(
                      decoration: const InputDecoration(
                        labelText: 'Business Type',
                        prefixIcon: Icon(Iconsax.building_3),
                      ),
                      value: controller.businessType.value,
                      items: BusinessType.values
                          .map((type) => DropdownMenuItem(value: type, child: Text(type.name.capitalizeFirst!)))
                          .toList(),
                      onChanged: (value) => controller.businessType.value = value!,
                    ),
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwInputFields),
                Expanded(
                  child: Obx(
                    () => DropdownButtonFormField<PriorityLevel>(
                      decoration: const InputDecoration(labelText: 'Priority', prefixIcon: Icon(Iconsax.flash_1)),
                      value: controller.priority.value,
                      items: PriorityLevel.values
                          .map((level) => DropdownMenuItem(value: level, child: Text(level.name.capitalizeFirst!)))
                          .toList(),
                      onChanged: (value) => controller.priority.value = value!,
                    ),
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwInputFields),
                Expanded(
                  child: Obx(
                    () => DropdownButtonFormField<AccountStatus>(
                      decoration: const InputDecoration(labelText: 'Status', prefixIcon: Icon(Iconsax.activity)),
                      value: controller.status.value,
                      items: AccountStatus.values
                          .map((status) => DropdownMenuItem(value: status, child: Text(status.name.capitalizeFirst!)))
                          .toList(),
                      onChanged: (value) => controller.status.value = value!,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),

            // NEW: Is Parent Toggle
            Obx(
              () => CheckboxListTile(
                title: const Text('Is this a Parent Account?'),
                subtitle: const Text('Mark this if other accounts can be assigned under this one.'),
                value: controller.isParent.value,
                onChanged: (value) {
                  controller.isParent.value = value ?? false;
                  // If it's a parent, it cannot have a parent itself
                  if (controller.isParent.value) {
                    controller.selectedParentAccount.value = null;
                  }
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),

            // NEW: Assign Parent Account (conditionally visible)
            Obx(
              () => Visibility(
                visible: !controller.isParent.value, // Only show if not a parent itself
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Assign Parent Account (Optional)', style: Theme.of(context).textTheme.titleMedium),
                    const SizedBox(height: TSizes.spaceBtwItems),
                    DropdownButtonFormField<AccountModel>(
                      decoration: const InputDecoration(
                        labelText: 'Select Parent Account',
                        prefixIcon: Icon(Iconsax.building_3),
                      ),
                      value: controller.selectedParentAccount.value,
                      items: [
                        const DropdownMenuItem<AccountModel>(value: null, child: Text('No Parent')),
                        ...controller.parentAccountOptions.map(
                          (account) => DropdownMenuItem(value: account, child: Text(account.name)),
                        ),
                      ],
                      onChanged: (value) {
                        controller.selectedParentAccount.value = value;
                      },
                    ),
                    const SizedBox(height: TSizes.spaceBtwSections),
                  ],
                ),
              ),
            ),

            // Industries Multi-Select (placeholder as it's not fully implemented yet)
            Text('Industries (Optional)', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: TSizes.spaceBtwItems),
            Obx(
              () => Wrap(
                spacing: TSizes.sm,
                children: controller.selectedIndustries
                    .map(
                      (industry) =>
                          Chip(label: Text(industry), onDeleted: () => controller.selectedIndustries.remove(industry)),
                    )
                    .toList(),
              ),
            ),
            OutlinedButton(
              onPressed: () {
                // TODO: Implement industry selection dialog
                Get.snackbar('Industry Selection', 'Industry selection not yet implemented.');
              },
              child: const Text('Select Industries'),
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Handlers
            Text('Select Handlers', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: TSizes.spaceBtwInputFields / 2),
            Obx(() {
              if (userController.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              return Wrap(
                spacing: TSizes.sm,
                runSpacing: TSizes.sm,
                children: userController.allItems.map((user) {
                  return TChoiceChip(
                    text: user.fullName,
                    selected: controller.selectedHandlers.contains(user),
                    onSelected: (value) => controller.toggleHandlerSelection(user),
                    color: Colors.green,
                  );
                }).toList(),
              );
            }),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Contacts
            Text('Select Contacts', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: TSizes.spaceBtwInputFields / 2),
            Obx(
              () => Wrap(
                spacing: TSizes.sm,
                runSpacing: TSizes.sm,
                children: controller.selectedContacts.map((contact) {
                  return TChoiceChip(
                    text: contact.name,
                    color: Colors.green,
                    selected: true, // Contacts in this list are always selected
                    onSelected: (value) => controller.toggleContactSelection(contact),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => showContactSelectionDialog(context),
                icon: const Icon(Iconsax.add),
                label: const Text('Add Contacts'),
              ),
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // NEW: Addresses Section
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('Addresses', style: Theme.of(context).textTheme.headlineSmall),
                    ElevatedButton.icon(
                      onPressed: () {
                        // Show popup to add a new address
                        Get.dialog(
                          AddEditAddressPopup(
                            accountId: 'new_account_temp_id', // Temporary ID for new accounts
                            createdByUserId: currentUserId, // Pass current user ID
                            onSave: (address) {
                              controller.addAddress(address);
                            },
                          ),
                        );
                      },
                      icon: const Icon(Iconsax.add),
                      label: const Text('Add New Address'),
                    ),
                  ],
                ),
                const SizedBox(height: TSizes.spaceBtwSections),

                // Display Added Addresses
                Obx(() {
                  if (controller.accountAddresses.isEmpty) {
                    return Center(
                      child: Text(
                        'No addresses added yet.',
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: Colors.grey),
                      ),
                    );
                  }
                  return ListView.builder(
                    shrinkWrap: true, // Important for nested list views
                    physics: const NeverScrollableScrollPhysics(), // Disable scrolling
                    itemCount: controller.accountAddresses.length,
                    itemBuilder: (_, index) {
                      final address = controller.accountAddresses[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: TSizes.spaceBtwItems),
                        child: Padding(
                          padding: const EdgeInsets.all(TSizes.sm),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Text(
                                    '${address.type} Address ${address.isDefault ? '(Default)' : ''}',
                                    style: Theme.of(context).textTheme.titleMedium,
                                  ),
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      IconButton(
                                        icon: const Icon(Iconsax.edit_2),
                                        onPressed: () {
                                          Get.dialog(
                                            AddEditAddressPopup(
                                              initialAddress: address,
                                              accountId: 'new_account_temp_id', // Still temp ID
                                              createdByUserId: currentUserId,
                                              onSave: (updatedAddress) {
                                                controller.updateAddress(updatedAddress);
                                              },
                                            ),
                                          );
                                        },
                                      ),
                                      IconButton(
                                        icon: const Icon(Iconsax.trash, color: Colors.red),
                                        onPressed: () {
                                          controller.removeAddress(address);
                                        },
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: TSizes.xs),
                              Text(address.toString(), style: Theme.of(context).textTheme.bodySmall),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                }),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Country & Emirates (Read-Only) - Displaying denormalized values
            // These fields are now populated by the default billing address
            // and are read-only to the user directly on the account form.
            Text(
              'Denormalized Location (from Default Billing Address)',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => TextFormField(
                      readOnly: true, // Make read-only
                      controller: TextEditingController(text: controller.denormalizedCountry.value),
                      decoration: const InputDecoration(labelText: 'Country', prefixIcon: Icon(Iconsax.global)),
                    ),
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwInputFields),
                Expanded(
                  child: Obx(
                    () => TextFormField(
                      readOnly: true, // Make read-only
                      controller: TextEditingController(text: controller.denormalizedEmirates.value),
                      decoration: const InputDecoration(labelText: 'Emirates/State', prefixIcon: Icon(Iconsax.map_1)),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(onPressed: () => controller.createAccount(), child: const Text('Create Account')),
            ),
          ],
        ),
      ),
    );
  }
}

// Ensure this dialog function is defined within the same scope or accessible.
void showContactSelectionDialog(BuildContext context) {
  final controller = Get.find<CreateAccountController>(); // Get the controller in scope
  Get.dialog(
    ContactSelectionDialog(
      initialSelectedContacts: controller.selectedContacts.toList(), // Pass current selected contacts
      onContactsSelected: (selectedList) {
        controller.selectedContacts.assignAll(selectedList); // Update controller's list
      },
    ),
  );
}
