import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/products/models/product_model.dart';
import 'package:alloy/features/products/screens/product_variants/responsive_screen/product_variants_desktop.dart';
import 'package:flutter/material.dart';

class ProductVariantsScreen extends StatelessWidget {
  const ProductVariantsScreen({super.key, required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    return TSiteTemplate(
      desktop: ProductVariantsDesktop(product: product),
      tablet: ProductVariantsDesktop(product: product),
      mobile: ProductVariantsDesktop(product: product), // For now, use desktop layout for all
    );
  }
}
