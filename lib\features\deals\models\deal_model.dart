import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../utils/constants/enums.dart';
import 'deal_contact_person_model.dart';

class DealModel {
  String id;
  String dealNumber; // Renamed from quotationNumber
  String ownerId; // ID of the SalesUser who created this deal
  String? projectId; // Optional: Link to a project if you have a project module

  // Client Information (Denormalized for quick access)
  String clientId; // ID of the AccountModel
  String clientName;
  String clientAddress; // Billing address
  String? selectedShippingAddressId; // NEW: ID of the selected shipping address from AccountModel
  Map<String, dynamic>? selectedShippingAddressDetails; // NEW: Denormalized details of the selected shipping address
  List<DealContactPerson> contactPersons; // NEW: List of contact persons for this deal

  // Deal Details
  DateTime dealDate; // Renamed from quotationDate
  DateTime validityDate; // Renamed from quotationValidityDate
  String paymentTerms; // e.g., "100% Before Delivery", "30 Days Credit"
  String deliveryTime; // e.g., "7 Days", "TBD"
  String projectDetails; // e.g., "New Office Building - Phase 1"

  // Approval Workflow
  DealStatus status; // Renamed from QuotationStatus
  bool requiresApproval; // Set by user during creation (Point 6)
  String? approvedByUserId; // User ID of the manager/admin who approved it
  DateTime? approvalDate;
  bool isLocked; // True if approved and not unlocked (Point 3 & 5)

  // Deal Versioning (Point 4)
  int version; // Version number of the deal (e.g., 1, 2, 3)
  String? previousDealId; // ID of the previous version of this deal

  // Pricing & Discounts (Global)
  DiscountType discountType; // Percentage or Value
  double discountValue; // The actual percentage (e.g., 10.0 for 10%) or value (e.g., 500.0 AED)
  double totalAmountBeforeDiscount; // Calculated sum of item totals before global discount
  double totalTaxAmount; // Calculated VAT/Tax based on globalTaxRatePercentage from settings

  // Freight Charges (as per your existing quote)
  double freightCharges;

  // Two-Stage Deal Creation Workflow
  DealCreationStage creationStage; // basicIntent, detailedSpec, pricingReview, clientReview
  String? basicRequirements; // Stage 1: Rough requirements text
  String? salesPersonNotes; // Stage 1: Sales person's notes
  bool useClientDescriptions; // Global toggle for client LPO descriptions
  String? clientLPOReference; // Client's LPO reference number
  DateTime? lpoReceivedDate; // Date when client LPO was received

  // Comments System (Point 3 & 4)
  String? salesComments; // Sales comments/description for production team reference
  String? productionComments; // Production team's comments (added later)
  String? dealInstructions; // General deal instructions (nullable, can be updated after unlocking)

  // Progress Tracking (Point 2)
  double progressPercentage; // 0.0 to 100.0, calculated based on status or production stages

  // Deal Priority
  DealPriority priority; // Priority level for the deal

  // Additional Status Tracking Fields
  DateTime? quotationGeneratedAt; // When quotation PDF was generated
  String? quotationGeneratedByUserId; // User who generated the quotation
  DateTime? clientApprovedAt; // When client approved the deal
  DateTime? closedAt; // When deal was closed
  String? closedByUserId; // User who closed the deal

  // Primary Contact Person (from contactPersons list)
  String? contactPersonId; // ID of primary contact person
  String? contactPersonName; // Name of primary contact person
  String? contactPersonPhone; // Phone of primary contact person
  String? contactPersonEmail; // Email of primary contact person

  // Audit Fields
  DateTime? createdAt;
  DateTime? updatedAt;
  String createdByUserId; // ID of the user who created it
  String? updatedByUserId; // ID of the user who last updated it

  // Denormalized Sales Person Info (Removed email and phone as per request)
  String salesPersonId;
  String salesPersonName;

  DealModel({
    required this.id,
    required this.dealNumber,
    required this.ownerId,
    this.projectId,
    required this.clientId,
    required this.clientName,
    required this.clientAddress,
    this.selectedShippingAddressId, // NEW
    this.selectedShippingAddressDetails, // NEW
    this.contactPersons = const [], // NEW: Initialize as empty list
    required this.dealDate,
    required this.validityDate,
    this.paymentTerms = '100% Before Delivery', // Default as per your example
    this.deliveryTime = 'TBD', // Default as per your example
    this.projectDetails = '',
    this.status = DealStatus.draft, // Renamed enum
    this.requiresApproval = true, // Default to true as per discussion (Point 6)
    this.approvedByUserId,
    this.approvalDate,
    this.isLocked = false,
    this.version = 1, // NEW: Default to version 1
    this.previousDealId, // NEW
    this.discountType = DiscountType.none,
    this.discountValue = 0.0,
    this.totalAmountBeforeDiscount = 0.0,
    this.totalTaxAmount = 0.0,
    this.freightCharges = 0.0,
    // Two-stage workflow fields
    this.creationStage = DealCreationStage.basicIntent,
    this.basicRequirements,
    this.salesPersonNotes,
    this.useClientDescriptions = false,
    this.clientLPOReference,
    this.lpoReceivedDate,
    // Comments system fields
    this.salesComments,
    this.productionComments,
    this.dealInstructions,
    this.progressPercentage = 0.0, // NEW: Default to 0.0
    // Priority and additional tracking fields
    this.priority = DealPriority.medium, // Default priority
    this.quotationGeneratedAt,
    this.quotationGeneratedByUserId,
    this.clientApprovedAt,
    this.closedAt,
    this.closedByUserId,
    this.contactPersonId,
    this.contactPersonName,
    this.contactPersonPhone,
    this.contactPersonEmail,
    this.createdAt,
    this.updatedAt,
    required this.createdByUserId,
    this.updatedByUserId,
    required this.salesPersonId,
    required this.salesPersonName,
  });

  /// Getters for on-the-fly calculations (Point 2.c & removal of fields)
  // NEW: Calculate totalDiscountAmount based on discountType and discountValue
  double get totalDiscountAmount {
    if (discountType == DiscountType.percentage) {
      return totalAmountBeforeDiscount * (discountValue / 100.0);
    } else if (discountType == DiscountType.value) {
      return discountValue;
    }
    return 0.0;
  }

  double get totalAmountAfterDiscount {
    double amount = totalAmountBeforeDiscount - totalDiscountAmount;
    return amount < 0 ? 0 : amount; // Ensure it's not negative
  }

  double get grandTotalAmount {
    return totalAmountAfterDiscount + totalTaxAmount + freightCharges;
  }

  // Alias for grandTotalAmount (used by controllers)
  double get totalDealValue => grandTotalAmount;

  /// Empty Helper Function
  static DealModel empty() => DealModel(
    id: '',
    dealNumber: '',
    ownerId: '',
    clientId: '',
    clientName: '',
    clientAddress: '',
    dealDate: DateTime.now(),
    validityDate: DateTime.now().add(const Duration(days: 30)), // Default 30 days
    createdByUserId: '',
    salesPersonId: '',
    salesPersonName: '',
    contactPersons: const [], // Ensure empty list for contacts
  );

  /// Convert DealModel to JSON format for Firestore.
  Map<String, dynamic> toJson() {
    return {
      'dealNumber': dealNumber,
      'ownerId': ownerId,
      'projectId': projectId,
      'clientId': clientId,
      'clientName': clientName,
      'clientAddress': clientAddress,
      'selectedShippingAddressId': selectedShippingAddressId, // NEW
      'selectedShippingAddressDetails': selectedShippingAddressDetails, // NEW
      'contactPersons': contactPersons.map((cp) => cp.toJson()).toList(), // NEW: Convert list of objects to JSON
      'dealDate': Timestamp.fromDate(dealDate),
      'validityDate': Timestamp.fromDate(validityDate),
      'paymentTerms': paymentTerms,
      'deliveryTime': deliveryTime,
      'projectDetails': projectDetails,
      'status': status.name, // Convert enum to string
      'requiresApproval': requiresApproval,
      'approvedByUserId': approvedByUserId,
      'approvalDate': approvalDate != null ? Timestamp.fromDate(approvalDate!) : null,
      'isLocked': isLocked,
      'version': version, // NEW
      'previousDealId': previousDealId, // NEW
      'discountType': discountType.name, // Convert enum to string
      'discountValue': discountValue,
      'totalAmountBeforeDiscount': totalAmountBeforeDiscount,
      'totalTaxAmount': totalTaxAmount,
      'freightCharges': freightCharges,
      // Two-stage workflow fields
      'creationStage': creationStage.name,
      'basicRequirements': basicRequirements,
      'salesPersonNotes': salesPersonNotes,
      'useClientDescriptions': useClientDescriptions,
      'clientLPOReference': clientLPOReference,
      'lpoReceivedDate': lpoReceivedDate != null ? Timestamp.fromDate(lpoReceivedDate!) : null,
      // Comments system fields
      'salesComments': salesComments,
      'productionComments': productionComments,
      'dealInstructions': dealInstructions,
      'progressPercentage': progressPercentage, // NEW
      'createdAt': createdAt ?? FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
      'createdByUserId': createdByUserId,
      'updatedByUserId': updatedByUserId,
      'salesPersonId': salesPersonId,
      'salesPersonName': salesPersonName,
    };
  }

  /// Factory method to create a DealModel from a Firestore DocumentSnapshot.
  factory DealModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (!document.exists || document.data() == null) {
      return DealModel.empty();
    }

    final data = document.data()!;
    return DealModel(
      id: document.id,
      dealNumber: data['dealNumber'] ?? '',
      ownerId: data['ownerId'] ?? '',
      projectId: data['projectId'],
      clientId: data['clientId'] ?? '',
      clientName: data['clientName'] ?? '',
      clientAddress: data['clientAddress'] ?? '',
      selectedShippingAddressId: data['selectedShippingAddressId'], // NEW
      selectedShippingAddressDetails: data['selectedShippingAddressDetails'] != null
          ? Map<String, dynamic>.from(data['selectedShippingAddressDetails'])
          : null, // NEW
      contactPersons:
          (data['contactPersons'] as List<dynamic>?)
              ?.map((cpData) => DealContactPerson.fromMap(Map<String, dynamic>.from(cpData)))
              .toList() ??
          const [], // NEW: Parse list of contacts
      dealDate: (data['dealDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      validityDate: (data['validityDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      paymentTerms: data['paymentTerms'] ?? '100% Before Delivery',
      deliveryTime: data['deliveryTime'] ?? 'TBD',
      projectDetails: data['projectDetails'] ?? '',
      status: DealStatus.values.firstWhere(
        (e) => e.name == (data['status'] as String?),
        orElse: () => DealStatus.draft,
      ),
      requiresApproval: data['requiresApproval'] ?? true,
      approvedByUserId: data['approvedByUserId'],
      approvalDate: (data['approvalDate'] as Timestamp?)?.toDate(),
      isLocked: data['isLocked'] ?? false,
      version: (data['version'] as int?) ?? 1, // NEW
      previousDealId: data['previousDealId'], // NEW
      discountType: DiscountType.values.firstWhere(
        (e) => e.name == (data['discountType'] as String?),
        orElse: () => DiscountType.none,
      ),
      discountValue: (data['discountValue'] as num?)?.toDouble() ?? 0.0,
      totalAmountBeforeDiscount: (data['totalAmountBeforeDiscount'] as num?)?.toDouble() ?? 0.0,
      totalTaxAmount: (data['totalTaxAmount'] as num?)?.toDouble() ?? 0.0,
      freightCharges: (data['freightCharges'] as num?)?.toDouble() ?? 0.0,
      // Two-stage workflow fields
      creationStage: DealCreationStage.values.firstWhere(
        (e) => e.name == data['creationStage'],
        orElse: () => DealCreationStage.basicIntent,
      ),
      basicRequirements: data['basicRequirements'],
      salesPersonNotes: data['salesPersonNotes'],
      useClientDescriptions: data['useClientDescriptions'] ?? false,
      clientLPOReference: data['clientLPOReference'],
      lpoReceivedDate: (data['lpoReceivedDate'] as Timestamp?)?.toDate(),
      // Comments system fields
      salesComments: data['salesComments'],
      productionComments: data['productionComments'],
      dealInstructions: data['dealInstructions'],
      progressPercentage: (data['progressPercentage'] as num?)?.toDouble() ?? 0.0, // NEW
      createdAt: (data['createdAt'] as Timestamp?)?.toDate(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
      createdByUserId: data['createdByUserId'] ?? '',
      updatedByUserId: data['updatedByUserId'],
      salesPersonId: data['salesPersonId'] ?? '',
      salesPersonName: data['salesPersonName'] ?? '',
    );
  }

  // CopyWith method for immutability and easy updates
  DealModel copyWith({
    String? id,
    String? dealNumber,
    String? ownerId,
    String? projectId,
    String? clientId,
    String? clientName,
    String? clientAddress,
    String? selectedShippingAddressId,
    Map<String, dynamic>? selectedShippingAddressDetails,
    List<DealContactPerson>? contactPersons,
    DateTime? dealDate,
    DateTime? validityDate,
    String? paymentTerms,
    String? deliveryTime,
    String? projectDetails,
    DealStatus? status,
    bool? requiresApproval,
    String? approvedByUserId,
    DateTime? approvalDate,
    bool? isLocked,
    int? version,
    String? previousDealId,
    DiscountType? discountType,
    double? discountValue,
    double? totalAmountBeforeDiscount,
    double? totalTaxAmount,
    double? freightCharges,
    // Two-stage workflow fields
    DealCreationStage? creationStage,
    String? basicRequirements,
    String? salesPersonNotes,
    bool? useClientDescriptions,
    String? clientLPOReference,
    DateTime? lpoReceivedDate,
    // Comments system fields
    String? salesComments,
    String? productionComments,
    String? dealInstructions,
    double? progressPercentage,
    // Priority and additional tracking fields
    DealPriority? priority,
    DateTime? quotationGeneratedAt,
    String? quotationGeneratedByUserId,
    DateTime? clientApprovedAt,
    DateTime? closedAt,
    String? closedByUserId,
    String? contactPersonId,
    String? contactPersonName,
    String? contactPersonPhone,
    String? contactPersonEmail,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdByUserId,
    String? updatedByUserId,
    String? salesPersonId,
    String? salesPersonName,
  }) {
    return DealModel(
      id: id ?? this.id,
      dealNumber: dealNumber ?? this.dealNumber,
      ownerId: ownerId ?? this.ownerId,
      projectId: projectId ?? this.projectId,
      clientId: clientId ?? this.clientId,
      clientName: clientName ?? this.clientName,
      clientAddress: clientAddress ?? this.clientAddress,
      selectedShippingAddressId: selectedShippingAddressId ?? this.selectedShippingAddressId,
      selectedShippingAddressDetails: selectedShippingAddressDetails ?? this.selectedShippingAddressDetails,
      contactPersons: contactPersons ?? this.contactPersons,
      dealDate: dealDate ?? this.dealDate,
      validityDate: validityDate ?? this.validityDate,
      paymentTerms: paymentTerms ?? this.paymentTerms,
      deliveryTime: deliveryTime ?? this.deliveryTime,
      projectDetails: projectDetails ?? this.projectDetails,
      status: status ?? this.status,
      requiresApproval: requiresApproval ?? this.requiresApproval,
      approvedByUserId: approvedByUserId ?? this.approvedByUserId,
      approvalDate: approvalDate ?? this.approvalDate,
      isLocked: isLocked ?? this.isLocked,
      version: version ?? this.version,
      previousDealId: previousDealId ?? this.previousDealId,
      discountType: discountType ?? this.discountType,
      discountValue: discountValue ?? this.discountValue,
      totalAmountBeforeDiscount: totalAmountBeforeDiscount ?? this.totalAmountBeforeDiscount,
      totalTaxAmount: totalTaxAmount ?? this.totalTaxAmount,
      freightCharges: freightCharges ?? this.freightCharges,
      // Two-stage workflow fields
      creationStage: creationStage ?? this.creationStage,
      basicRequirements: basicRequirements ?? this.basicRequirements,
      salesPersonNotes: salesPersonNotes ?? this.salesPersonNotes,
      useClientDescriptions: useClientDescriptions ?? this.useClientDescriptions,
      clientLPOReference: clientLPOReference ?? this.clientLPOReference,
      lpoReceivedDate: lpoReceivedDate ?? this.lpoReceivedDate,
      // Comments system fields
      salesComments: salesComments ?? this.salesComments,
      productionComments: productionComments ?? this.productionComments,
      dealInstructions: dealInstructions ?? this.dealInstructions,
      progressPercentage: progressPercentage ?? this.progressPercentage,
      // Priority and additional tracking fields
      priority: priority ?? this.priority,
      quotationGeneratedAt: quotationGeneratedAt ?? this.quotationGeneratedAt,
      quotationGeneratedByUserId: quotationGeneratedByUserId ?? this.quotationGeneratedByUserId,
      clientApprovedAt: clientApprovedAt ?? this.clientApprovedAt,
      closedAt: closedAt ?? this.closedAt,
      closedByUserId: closedByUserId ?? this.closedByUserId,
      contactPersonId: contactPersonId ?? this.contactPersonId,
      contactPersonName: contactPersonName ?? this.contactPersonName,
      contactPersonPhone: contactPersonPhone ?? this.contactPersonPhone,
      contactPersonEmail: contactPersonEmail ?? this.contactPersonEmail,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdByUserId: createdByUserId ?? this.createdByUserId,
      updatedByUserId: updatedByUserId ?? this.updatedByUserId,
      salesPersonId: salesPersonId ?? this.salesPersonId,
      salesPersonName: salesPersonName ?? this.salesPersonName,
    );
  }
}
