import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../../../../common/widgets/appbar/appbar.dart';
import '../../../../common/widgets/containers/rounded_container.dart';
import '../../../../common/widgets/forms/enhanced_dropdown.dart';
import '../../../../common/widgets/forms/enhanced_text_field.dart';
import '../../../../common/widgets/forms/form_section.dart';
import '../../../../utils/constants/colors.dart';
import '../../../../utils/constants/sizes.dart';
import '../../../../utils/helpers/helper_functions.dart';
import '../../controllers/deal_controller.dart';

/// Stage 1: Basic Intent Capture Form
/// Mobile-optimized form for sales on-the-go
/// Captures rough requirements and basic client information
class BasicIntentCaptureForm extends StatelessWidget {
  const BasicIntentCaptureForm({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<DealController>();
    final dark = THelperFunctions.isDarkMode(context);

    return Scaffold(
      appBar: TAppBar(
        title: Text('New Deal - Basic Intent', style: Theme.of(context).textTheme.headlineSmall),
        showBackArrow: true,
        actions: [
          TextButton(
            onPressed: () => _saveDraft(controller),
            child: Text('Save Draft', style: TextStyle(color: TColors.primary)),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(TSizes.defaultSpace),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Progress Indicator
            _buildProgressIndicator(context),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Client Information Section
            _buildClientSection(context, controller, dark),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Basic Requirements Section
            _buildRequirementsSection(context, controller, dark),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Project Details Section
            _buildProjectSection(context, controller, dark),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Sales Notes Section
            _buildNotesSection(context, controller, dark),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Action Buttons
            _buildActionButtons(context, controller),
          ],
        ),
      ),
    );
  }

  /// Progress indicator showing current stage
  Widget _buildProgressIndicator(BuildContext context) {
    return TRoundedContainer(
      padding: const EdgeInsets.all(TSizes.md),
      backgroundColor: TColors.primary.withOpacity(0.1),
      child: Row(
        children: [
          Icon(Iconsax.document_text, color: TColors.primary),
          const SizedBox(width: TSizes.sm),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Stage 1: Basic Intent Capture',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(color: TColors.primary),
                ),
                Text('Quick capture for sales on-the-go', style: Theme.of(context).textTheme.bodySmall),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: TSizes.sm, vertical: TSizes.xs),
            decoration: BoxDecoration(color: TColors.primary, borderRadius: BorderRadius.circular(TSizes.sm)),
            child: Text('1/4', style: TextStyle(color: TColors.white, fontSize: 12)),
          ),
        ],
      ),
    );
  }

  /// Client information section
  Widget _buildClientSection(BuildContext context, DealController controller, bool dark) {
    return TFormSection(
      title: 'Client Information',
      icon: Iconsax.user,
      children: [
        // Client Selection
        Obx(
          () => TEnhancedDropdown<String>(
            labelText: 'Select Client *',
            value: controller.selectedClientId.value.isEmpty ? null : controller.selectedClientId.value,
            items: controller.clients
                .map((client) => DropdownMenuItem(value: client.id, child: Text(client.name)))
                .toList(),
            onChanged: (value) => controller.selectClient(value!),
            validator: (value) => value == null ? 'Please select a client' : null,
            prefixIcon: Iconsax.building,
          ),
        ),
        const SizedBox(height: TSizes.spaceBtwInputFields),

        // Contact Person Selection
        Obx(
          () => controller.selectedClientId.value.isNotEmpty
              ? TEnhancedDropdown<String>(
                  labelText: 'Primary Contact',
                  value: controller.selectedContactId.value.isEmpty ? null : controller.selectedContactId.value,
                  items: controller
                      .getClientContacts()
                      .map(
                        (contact) => DropdownMenuItem(
                          value: contact.id,
                          child: Text('${contact.name} - ${contact.designation}'),
                        ),
                      )
                      .toList(),
                  onChanged: (value) => controller.selectContact(value!),
                  prefixIcon: Iconsax.user_tag,
                )
              : const SizedBox.shrink(),
        ),
      ],
    );
  }

  /// Basic requirements section
  Widget _buildRequirementsSection(BuildContext context, DealController controller, bool dark) {
    return TFormSection(
      title: 'Basic Requirements',
      icon: Iconsax.clipboard_text,
      children: [
        // Rough Requirements Text
        TEnhancedTextField(
          labelText: 'Rough Requirements *',
          hintText: 'e.g., Cable tray 200mm width, Trunking 100x50mm, Ladder type cable tray...',
          controller: controller.basicRequirementsController,
          maxLines: 4,
          validator: (value) => value?.isEmpty ?? true ? 'Please enter basic requirements' : null,
          prefixIcon: Iconsax.note_text,
        ),
        const SizedBox(height: TSizes.spaceBtwInputFields),

        // Estimated Quantity (Optional)
        TEnhancedTextField(
          labelText: 'Estimated Quantity',
          hintText: 'e.g., 500 meters, 100 pieces, etc.',
          controller: controller.estimatedQuantityController,
          prefixIcon: Iconsax.calculator,
        ),
        const SizedBox(height: TSizes.spaceBtwInputFields),

        // Urgency Level
        Obx(
          () => TEnhancedDropdown<String>(
            labelText: 'Urgency Level',
            value: controller.urgencyLevel.value.isEmpty ? null : controller.urgencyLevel.value,
            items: const [
              DropdownMenuItem(value: 'Low', child: Text('Low - Standard timeline')),
              DropdownMenuItem(value: 'Medium', child: Text('Medium - Moderate urgency')),
              DropdownMenuItem(value: 'High', child: Text('High - Rush order')),
              DropdownMenuItem(value: 'Critical', child: Text('Critical - Emergency')),
            ],
            onChanged: (value) => controller.urgencyLevel.value = value!,
            prefixIcon: Iconsax.timer,
          ),
        ),
      ],
    );
  }

  /// Project details section
  Widget _buildProjectSection(BuildContext context, DealController controller, bool dark) {
    return TFormSection(
      title: 'Project Information',
      icon: Iconsax.building_3,
      children: [
        // Project Name
        TEnhancedTextField(
          labelText: 'Project Name',
          hintText: 'e.g., New Office Building - Phase 1',
          controller: controller.projectDetailsController,
          prefixIcon: Iconsax.building_3,
        ),
        const SizedBox(height: TSizes.spaceBtwInputFields),

        // Expected Timeline
        TEnhancedTextField(
          labelText: 'Expected Timeline',
          hintText: 'e.g., 2 weeks, 1 month, ASAP',
          controller: controller.expectedTimelineController,
          prefixIcon: Iconsax.calendar,
        ),
        const SizedBox(height: TSizes.spaceBtwInputFields),

        // Budget Range (Optional)
        TEnhancedTextField(
          labelText: 'Budget Range (Optional)',
          hintText: 'e.g., 10,000 - 15,000 AED',
          controller: controller.budgetRangeController,
          prefixIcon: Iconsax.money,
        ),
      ],
    );
  }

  /// Sales notes section
  Widget _buildNotesSection(BuildContext context, DealController controller, bool dark) {
    return TFormSection(
      title: 'Sales Notes',
      icon: Iconsax.note_1,
      children: [
        // Sales Person Notes
        TEnhancedTextField(
          labelText: 'Sales Notes',
          hintText: 'Internal notes, client preferences, special requirements...',
          controller: controller.salesNotesController,
          maxLines: 3,
          prefixIcon: Iconsax.note_1,
        ),
        const SizedBox(height: TSizes.spaceBtwInputFields),

        // Photo Attachment (Future Enhancement)
        Container(
          padding: const EdgeInsets.all(TSizes.md),
          decoration: BoxDecoration(
            border: Border.all(color: TColors.grey),
            borderRadius: BorderRadius.circular(TSizes.cardRadiusMd),
          ),
          child: Row(
            children: [
              Icon(Iconsax.camera, color: TColors.grey),
              const SizedBox(width: TSizes.sm),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Attach Photos (Coming Soon)', style: Theme.of(context).textTheme.bodyMedium),
                    Text(
                      'Client sketches, site photos, etc.',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(color: TColors.grey),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Action buttons
  Widget _buildActionButtons(BuildContext context, DealController controller) {
    return Column(
      children: [
        // Primary Action - Proceed to Detailed Specification
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () => _proceedToDetailedSpec(controller),
            style: ElevatedButton.styleFrom(
              backgroundColor: TColors.primary,
              padding: const EdgeInsets.symmetric(vertical: TSizes.md),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Proceed to Detailed Specification', style: TextStyle(color: TColors.white)),
                const SizedBox(width: TSizes.sm),
                Icon(Iconsax.arrow_right_3, color: TColors.white, size: 16),
              ],
            ),
          ),
        ),
        const SizedBox(height: TSizes.sm),

        // Secondary Action - Save and Continue Later
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () => _saveAndContinueLater(controller),
            style: OutlinedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: TSizes.md)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Iconsax.save_2, size: 16),
                const SizedBox(width: TSizes.sm),
                Text('Save & Continue Later'),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Save draft functionality
  void _saveDraft(DealController controller) {
    // Implement save draft logic
    controller.saveDraftDeal();
    Get.snackbar('Success', 'Draft saved successfully');
  }

  /// Proceed to detailed specification
  void _proceedToDetailedSpec(DealController controller) {
    if (controller.validateBasicIntent()) {
      controller.proceedToDetailedSpecification();
      // Navigate to detailed specification form
      Get.toNamed('/deal/detailed-specification');
    }
  }

  /// Save and continue later
  void _saveAndContinueLater(DealController controller) {
    controller.saveDraftDeal();
    Get.back();
    Get.snackbar('Success', 'Deal saved as draft. You can continue later from the deals list.');
  }
}
