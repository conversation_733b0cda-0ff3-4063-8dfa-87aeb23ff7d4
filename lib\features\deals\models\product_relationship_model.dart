import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../utils/constants/enums.dart';

/// Model for managing parent-child product relationships
/// Handles business logic for accessories, covers, hardware, and couplers
class ProductRelationshipModel {
  final String id;
  final String parentProductId;
  final String childProductId;
  final RelationshipType type;
  final bool includeInProduction;    // Separate production planning
  final bool showInInvoice;          // Show separately in invoice
  final bool isMandatory;            // Must be included with parent
  final double defaultQuantityRatio; // Auto-calculate child quantity
  final bool isFOC;                  // Free of charge
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  ProductRelationshipModel({
    required this.id,
    required this.parentProductId,
    required this.childProductId,
    required this.type,
    this.includeInProduction = true,
    this.showInInvoice = true,
    this.isMandatory = false,
    this.defaultQuantityRatio = 1.0,
    this.isFOC = false,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Convert to JSON for Firestore
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'parentProductId': parentProductId,
      'childProductId': childProductId,
      'type': type.name,
      'includeInProduction': includeInProduction,
      'showInInvoice': showInInvoice,
      'isMandatory': isMandatory,
      'defaultQuantityRatio': defaultQuantityRatio,
      'isFOC': isFOC,
      'notes': notes,
      'createdAt': createdAt,
      'updatedAt': updatedAt,
    };
  }

  /// Create from Firestore document
  factory ProductRelationshipModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    final data = document.data()!;
    return ProductRelationshipModel(
      id: document.id,
      parentProductId: data['parentProductId'] ?? '',
      childProductId: data['childProductId'] ?? '',
      type: RelationshipType.values.firstWhere(
        (e) => e.name == data['type'],
        orElse: () => RelationshipType.accessory,
      ),
      includeInProduction: data['includeInProduction'] ?? true,
      showInInvoice: data['showInInvoice'] ?? true,
      isMandatory: data['isMandatory'] ?? false,
      defaultQuantityRatio: (data['defaultQuantityRatio'] ?? 1.0).toDouble(),
      isFOC: data['isFOC'] ?? false,
      notes: data['notes'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  /// Create from JSON
  factory ProductRelationshipModel.fromJson(Map<String, dynamic> json) {
    return ProductRelationshipModel(
      id: json['id'] ?? '',
      parentProductId: json['parentProductId'] ?? '',
      childProductId: json['childProductId'] ?? '',
      type: RelationshipType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RelationshipType.accessory,
      ),
      includeInProduction: json['includeInProduction'] ?? true,
      showInInvoice: json['showInInvoice'] ?? true,
      isMandatory: json['isMandatory'] ?? false,
      defaultQuantityRatio: (json['defaultQuantityRatio'] ?? 1.0).toDouble(),
      isFOC: json['isFOC'] ?? false,
      notes: json['notes'],
      createdAt: json['createdAt'] is Timestamp 
        ? (json['createdAt'] as Timestamp).toDate()
        : DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] is Timestamp 
        ? (json['updatedAt'] as Timestamp).toDate()
        : DateTime.parse(json['updatedAt']),
    );
  }

  /// Create copy with updated fields
  ProductRelationshipModel copyWith({
    String? id,
    String? parentProductId,
    String? childProductId,
    RelationshipType? type,
    bool? includeInProduction,
    bool? showInInvoice,
    bool? isMandatory,
    double? defaultQuantityRatio,
    bool? isFOC,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ProductRelationshipModel(
      id: id ?? this.id,
      parentProductId: parentProductId ?? this.parentProductId,
      childProductId: childProductId ?? this.childProductId,
      type: type ?? this.type,
      includeInProduction: includeInProduction ?? this.includeInProduction,
      showInInvoice: showInInvoice ?? this.showInInvoice,
      isMandatory: isMandatory ?? this.isMandatory,
      defaultQuantityRatio: defaultQuantityRatio ?? this.defaultQuantityRatio,
      isFOC: isFOC ?? this.isFOC,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Calculate child quantity based on parent quantity
  double calculateChildQuantity(double parentQuantity) {
    return parentQuantity * defaultQuantityRatio;
  }

  /// Get display name for relationship type
  String get relationshipDisplayName {
    switch (type) {
      case RelationshipType.cover:
        return 'Cover';
      case RelationshipType.accessory:
        return 'Accessory';
      case RelationshipType.hardware:
        return 'Hardware';
      case RelationshipType.coupler:
        return 'Coupler';
      case RelationshipType.consumable:
        return 'Consumable';
    }
  }

  /// Check if this relationship should be included in production planning
  bool shouldIncludeInProduction() {
    return includeInProduction;
  }

  /// Check if this relationship should show separately in invoice
  bool shouldShowInInvoice() {
    return showInInvoice;
  }

  /// Check if this is a mandatory relationship
  bool isMandatoryRelationship() {
    return isMandatory;
  }

  /// Check if this is a free of charge item
  bool isFreeOfCharge() {
    return isFOC;
  }

  @override
  String toString() {
    return 'ProductRelationshipModel(id: $id, parentProductId: $parentProductId, childProductId: $childProductId, type: $type, ratio: $defaultQuantityRatio)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ProductRelationshipModel &&
        other.id == id &&
        other.parentProductId == parentProductId &&
        other.childProductId == childProductId &&
        other.type == type;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        parentProductId.hashCode ^
        childProductId.hashCode ^
        type.hashCode;
  }

  /// Static method to create a new relationship
  static ProductRelationshipModel create({
    required String parentProductId,
    required String childProductId,
    required RelationshipType type,
    bool includeInProduction = true,
    bool showInInvoice = true,
    bool isMandatory = false,
    double defaultQuantityRatio = 1.0,
    bool isFOC = false,
    String? notes,
  }) {
    final now = DateTime.now();
    return ProductRelationshipModel(
      id: '', // Will be set by Firestore
      parentProductId: parentProductId,
      childProductId: childProductId,
      type: type,
      includeInProduction: includeInProduction,
      showInInvoice: showInInvoice,
      isMandatory: isMandatory,
      defaultQuantityRatio: defaultQuantityRatio,
      isFOC: isFOC,
      notes: notes,
      createdAt: now,
      updatedAt: now,
    );
  }
}
