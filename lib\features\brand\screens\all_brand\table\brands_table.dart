import 'package:alloy/common/widgets/data_table/paginated_data_table.dart';
import 'package:alloy/features/brand/controller/brand_controller.dart';
import 'package:alloy/features/brand/screens/all_brand/table/table_source.dart';
import 'package:alloy/utils/device/device_utility.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BrandsTable extends StatelessWidget {
  const BrandsTable({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(BrandController());
    final isMobile = TDeviceUtils.isMobileScreen(context);

    return Obx(() {
      // This is just to trigger the Obx. Is not going to show the Text widget as its not being retuned
      Text(controller.filteredItems.length.toString());
      Text(controller.selectedRows.length.toString());

      // Check if any of the brands have more than 2 categories. If so, we need to show the table in lg mode
      // final lgTable = controller.filteredItems.any(
      //   (item) => item.brandCategories != null && item.brandCategories!.length > 2,
      // );

      return TPaginatedDataTable(
        minWidth: 700,
        // tableHeight: lgTable ? 96 * 11.5 : 760,
        // dataRowHeight: lgTable ? 94 : 64,
        sortAscending: controller.sortAscending.value,
        sortColumnIndex: controller.sortColumnIndex.value,
        columns: [
          DataColumn2(label: Text('Brand'), fixedWidth: isMobile ? null : 200),
          DataColumn2(label: Text('Categories')),
          DataColumn2(label: Text('Featured'), fixedWidth: isMobile ? null : 100),
          DataColumn2(label: Text('Date'), fixedWidth: isMobile ? null : 200),
          DataColumn2(label: Text('Action'), fixedWidth: isMobile ? null : 100),
        ],
        source: BrandsRows(),
      );
    });
  }
}
