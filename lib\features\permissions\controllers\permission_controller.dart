import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/features/permissions/models/module_permission_model.dart';
import 'package:alloy/features/permissions/repository/permission_repository.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:get/get.dart';

/// Controller for managing user permissions and access control
class PermissionController extends GetxController {
  static PermissionController get instance => Get.find();

  final _permissionRepository = PermissionRepository.instance;
  final _userController = UserController.instance;

  /// Current user's permissions
  final Rx<UserPermissions?> currentUserPermissions = Rx<UserPermissions?>(null);

  /// All user permissions (for admin management)
  final RxList<UserPermissions> allUserPermissions = <UserPermissions>[].obs;

  /// Loading states
  final RxBool isLoading = false.obs;
  final RxBool isUpdating = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializePermissionStreaming();
  }

  /// Initialize permission streaming for current user
  void _initializePermissionStreaming() {
    // Listen to user changes and update permissions accordingly
    _userController.user.listen((user) {
      if (user.id != null && user.id!.isNotEmpty) {
        _streamCurrentUserPermissions(user.id!);
      } else {
        currentUserPermissions.value = null;
      }
    });
  }

  /// Stream current user's permissions
  void _streamCurrentUserPermissions(String userId) {
    try {
      currentUserPermissions.bindStream(_permissionRepository.streamUserPermissions(userId));
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Permission Error', message: 'Could not load user permissions: $e');
    }
  }

  /// Check if current user has specific permission
  bool hasPermission(AppModule module, PermissionType permission) {
    final permissions = currentUserPermissions.value;

    // If permissions are not set up yet, fall back to role-based access
    if (permissions == null) {
      return _hasRoleBasedAccess(module, permission);
    }

    return permissions.hasPermission(module, permission);
  }

  /// Check if current user has any permission for a module
  bool hasModuleAccess(AppModule module) {
    final permissions = currentUserPermissions.value;

    // If permissions are not set up yet, fall back to role-based access
    if (permissions == null) {
      return _hasRoleBasedModuleAccess(module);
    }

    return permissions.hasAnyPermissionForModule(module);
  }

  /// Check if current user has access to a department
  bool hasDepartmentAccess(String department) {
    final permissions = currentUserPermissions.value;
    if (permissions == null) return false;

    return permissions.hasAccessToDepartment(department);
  }

  /// Get current user's permissions for a specific module
  List<PermissionType> getModulePermissions(AppModule module) {
    final permissions = currentUserPermissions.value;
    if (permissions == null) return [];

    return permissions.getModulePermissions(module);
  }

  /// Check if current user is admin (has admin role or admin permissions)
  bool get isAdmin {
    final user = _userController.user.value;
    return user.roles.contains(UserRole.Admin) || hasPermission(AppModule.users, PermissionType.manage);
  }

  /// Check if current user is manager or above
  bool get isManagerOrAbove {
    final user = _userController.user.value;
    return user.roles.contains(UserRole.Admin) ||
        user.roles.contains(UserRole.Manager) ||
        hasPermission(AppModule.deals, PermissionType.approve);
  }

  /// Create default permissions for a new user
  Future<void> createUserPermissions(String userId, List<UserRole> roles, {List<String> departments = const []}) async {
    try {
      isUpdating.value = true;
      await _permissionRepository.createDefaultPermissions(userId, roles, departments: departments);
      TLoaders.successSnackBar(title: 'Success', message: 'User permissions created successfully');
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Could not create user permissions: $e');
    } finally {
      isUpdating.value = false;
    }
  }

  /// Update specific permission for a user
  Future<void> updateUserPermission(String userId, AppModule module, PermissionType permission, bool granted) async {
    try {
      isUpdating.value = true;
      await _permissionRepository.updateUserPermission(userId, module, permission, granted);
      TLoaders.successSnackBar(title: 'Success', message: 'Permission updated successfully');
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Could not update permission: $e');
    } finally {
      isUpdating.value = false;
    }
  }

  /// Update user's department access
  Future<void> updateUserDepartments(String userId, List<String> departments) async {
    try {
      isUpdating.value = true;
      await _permissionRepository.updateUserDepartments(userId, departments);
      TLoaders.successSnackBar(title: 'Success', message: 'Department access updated successfully');
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Could not update department access: $e');
    } finally {
      isUpdating.value = false;
    }
  }

  /// Apply role template to user
  Future<void> applyRoleTemplate(String userId, UserRole role, {List<String>? departments}) async {
    try {
      isUpdating.value = true;
      await _permissionRepository.applyRoleTemplate(userId, role, departments: departments);
      TLoaders.successSnackBar(title: 'Success', message: 'Role template applied successfully');
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Could not apply role template: $e');
    } finally {
      isUpdating.value = false;
    }
  }

  /// Load all user permissions for admin management
  Future<void> loadAllUserPermissions() async {
    try {
      isLoading.value = true;
      final permissions = await _permissionRepository.getAllUserPermissions();
      allUserPermissions.assignAll(permissions);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Could not load user permissions: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// Stream all user permissions for admin management
  void streamAllUserPermissions() {
    try {
      allUserPermissions.bindStream(_permissionRepository.streamAllUserPermissions());
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Could not stream user permissions: $e');
    }
  }

  /// Delete user permissions
  Future<void> deleteUserPermissions(String userId) async {
    try {
      isUpdating.value = true;
      await _permissionRepository.deleteUserPermissions(userId);
      TLoaders.successSnackBar(title: 'Success', message: 'User permissions deleted successfully');
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Error', message: 'Could not delete user permissions: $e');
    } finally {
      isUpdating.value = false;
    }
  }

  /// Refresh current user permissions
  void refreshPermissions() {
    final userId = _userController.user.value.id;
    if (userId != null && userId.isNotEmpty) {
      _streamCurrentUserPermissions(userId);
    }
  }

  /// Check route access based on module mapping
  bool hasRouteAccess(String route) {
    final module = _getModuleForRoute(route);
    if (module == null) return true; // Allow access to unmapped routes

    return hasModuleAccess(module);
  }

  /// Map routes to modules for access control
  AppModule? _getModuleForRoute(String route) {
    // Define route to module mapping
    const routeModuleMap = {
      '/dashboard': AppModule.dashboard,
      '/accounts': AppModule.accounts,
      '/create-account': AppModule.accounts,
      '/import-accounts': AppModule.accounts,
      '/contacts': AppModule.contacts,
      '/create-contact': AppModule.contacts,
      '/import-contacts': AppModule.contacts,
      '/deals': AppModule.deals,
      '/createDeal': AppModule.deals,
      '/products': AppModule.products,
      '/create-product': AppModule.products,
      '/categories': AppModule.categories,
      '/orders': AppModule.orders,
      '/inventory': AppModule.inventory,
      '/reports': AppModule.reports,
      '/settings': AppModule.settings,
      '/users': AppModule.users,
      '/human-resources': AppModule.humanResources,
      '/workers': AppModule.workers,
      '/media': AppModule.media,
      '/banners': AppModule.banners,
      '/brands': AppModule.brands,
      '/profile': AppModule.profile,
    };

    return routeModuleMap[route];
  }

  /// Get permission summary for a user (for display purposes)
  Map<AppModule, List<PermissionType>> getUserPermissionSummary(String userId) {
    final userPermissions = allUserPermissions.firstWhereOrNull((p) => p.userId == userId);
    if (userPermissions == null) return {};

    final summary = <AppModule, List<PermissionType>>{};

    for (final module in AppModule.values) {
      final modulePermissions = userPermissions.getModulePermissions(module);
      if (modulePermissions.isNotEmpty) {
        summary[module] = modulePermissions;
      }
    }

    return summary;
  }

  /// Check if user can perform bulk operations
  bool canPerformBulkOperations(AppModule module) {
    return hasPermission(module, PermissionType.manage) ||
        (hasPermission(module, PermissionType.edit) && hasPermission(module, PermissionType.delete));
  }

  /// Check if user can export data from module
  bool canExportData(AppModule module) {
    return hasPermission(module, PermissionType.export) || hasPermission(module, PermissionType.manage);
  }

  /// Check if user can import data to module
  bool canImportData(AppModule module) {
    return hasPermission(module, PermissionType.import) || hasPermission(module, PermissionType.manage);
  }

  /// Get available departments for current user
  List<String> get availableDepartments {
    final permissions = currentUserPermissions.value;
    return permissions?.departments ?? [];
  }

  /// Check if current user has super admin privileges
  bool get isSuperAdmin {
    return hasPermission(AppModule.settings, PermissionType.manage) &&
        hasPermission(AppModule.users, PermissionType.manage);
  }

  /// Fallback role-based access when permissions are not set up
  bool _hasRoleBasedAccess(AppModule module, PermissionType permission) {
    final user = _userController.user.value;

    // Admin has access to everything
    if (user.roles.contains(UserRole.Admin)) {
      return true;
    }

    // Manager has access to most things except user management
    if (user.roles.contains(UserRole.Manager)) {
      if (module == AppModule.users && permission == PermissionType.manage) {
        return false;
      }
      return true;
    }

    // SalesUser has access to basic modules
    if (user.roles.contains(UserRole.SalesUser)) {
      switch (module) {
        case AppModule.dashboard:
        case AppModule.accounts:
        case AppModule.contacts:
        case AppModule.deals:
        case AppModule.products:
        case AppModule.categories:
          return permission == PermissionType.view ||
              permission == PermissionType.create ||
              permission == PermissionType.edit;
        default:
          return false;
      }
    }

    return false;
  }

  /// Fallback role-based module access when permissions are not set up
  bool _hasRoleBasedModuleAccess(AppModule module) {
    final user = _userController.user.value;

    // Admin has access to everything
    if (user.roles.contains(UserRole.Admin)) return true;

    // Manager has access to most modules
    if (user.roles.contains(UserRole.Manager)) {
      return module != AppModule.settings; // Restrict settings for managers
    }

    // SalesUser has access to basic modules
    if (user.roles.contains(UserRole.SalesUser)) {
      switch (module) {
        case AppModule.dashboard:
        case AppModule.accounts:
        case AppModule.contacts:
        case AppModule.deals:
        case AppModule.products:
        case AppModule.categories:
          return true;
        default:
          return false;
      }
    }

    return false;
  }
}
