import 'package:alloy/features/products/models/product_variant_model.dart';
import 'package:alloy/utils/constants/text_strings.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../../../utils/exceptions/firebase_exceptions.dart';
import '../../../utils/exceptions/platform_exceptions.dart';

class ProductVariantRepository extends GetxController {
  static ProductVariantRepository get instance => Get.find();

  final _db = FirebaseFirestore.instance;

  // Collection reference for product variants
  final CollectionReference<Map<String, dynamic>> _productVariantsCollection = FirebaseFirestore.instance.collection(TTexts.productVariants);

  /// Stream all variants for a specific product
  Stream<List<ProductVariantModel>> streamVariantsForProduct(String productId) {
    // Ensure 'productId' field name in Firestore is camelCase
    return _productVariantsCollection.where('productId', isEqualTo: productId).snapshots().map((snapshot) {
      return snapshot.docs.map((doc) => ProductVariantModel.fromSnapshot(doc)).toList();
    });
  }

  /// Create multiple product variants in a single batch operation
  Future<void> createVariantsInBatch(List<ProductVariantModel> variants) async {
    try {
      final batch = _db.batch();
      for (final variant in variants) {
        final docRef = _productVariantsCollection.doc();
        // variant.toJson() should now use camelCase keys
        batch.set(docRef, variant.toJson());
      }
      await batch.commit();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'An unexpected error occurred while batch creating variants: $e';
    }
  }

  /// Update a single product variant.
  Future<void> updateVariant(ProductVariantModel variant) async {
    try {
      await _productVariantsCollection.doc(variant.id).update(variant.toJson());
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }

  /// Delete a single product variant.
  Future<void> deleteVariant(String variantId) async {
    try {
      await _productVariantsCollection.doc(variantId).delete();
    } on FirebaseException catch (e) {
      throw TFirebaseException(e.code).message;
    } on PlatformException catch (e) {
      throw TPlatformException(e.code).message;
    } catch (e) {
      throw 'Something went wrong. Please try again';
    }
  }
}
