import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/forms/enhanced_text_field.dart';
import 'package:alloy/common/widgets/forms/enhanced_dropdown.dart';
import 'package:alloy/common/widgets/forms/form_section.dart';
import 'package:alloy/features/contact/controller/create_contact_controller.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/common/widgets/chips/rounded_choice_chips.dart';
import 'package:alloy/features/contact/screens/create_contact/widgets/account_selection_dialog.dart';
import 'package:alloy/features/contact/screens/create_contact/widgets/handler_selection_dialog.dart';
import 'package:alloy/utils/constants/lists.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class CreateContactForm extends StatelessWidget {
  const CreateContactForm({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CreateContactController());

    return TRoundedContainer(
      width: 600,
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Contact\'s Information', style: Theme.of(context).textTheme.headlineMedium),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Name & Title
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controller.name,
                    validator: (value) => TValidator.validateEmptyText('Name', value),
                    decoration: const InputDecoration(labelText: 'Name*', prefixIcon: Icon(Iconsax.user)),
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwInputFields),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: controller.title.text.isNotEmpty ? controller.title.text : null,
                    items: TLists.salutations
                        .map((salutation) => DropdownMenuItem(value: salutation, child: Text(salutation)))
                        .toList(),
                    onChanged: (value) => controller.title.text = value ?? '',
                    decoration: const InputDecoration(labelText: 'Title', prefixIcon: Icon(Iconsax.user_octagon)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),

            // Email & Phone
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controller.email,
                    validator: (value) => TValidator.validateEmail(value),
                    decoration: const InputDecoration(labelText: 'Email', prefixIcon: Icon(Iconsax.direct)),
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwInputFields),
                Expanded(
                  child: TextFormField(
                    controller: controller.phone,
                    validator: (value) => TValidator.validateEmptyText('Phone', value),
                    decoration: const InputDecoration(labelText: 'Phone*', prefixIcon: Icon(Iconsax.call)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),

            // Landline & Designation
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: controller.landline,
                    decoration: const InputDecoration(labelText: 'Landline', prefixIcon: Icon(Iconsax.call)),
                  ),
                ),
                const SizedBox(width: TSizes.spaceBtwInputFields),
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: controller.designation.text.isNotEmpty ? controller.designation.text : null,
                    items: TLists.designations
                        .map((designation) => DropdownMenuItem(value: designation, child: Text(designation)))
                        .toList(),
                    onChanged: (value) => controller.designation.text = value ?? '',
                    decoration: const InputDecoration(labelText: 'Designation', prefixIcon: Icon(Iconsax.briefcase)),
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),

            // Active
            Obx(
              () => SwitchListTile(
                title: const Text('Active'),
                value: controller.active.value,
                onChanged: (value) => controller.active.value = value,
              ),
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Accounts
            Text('Select Accounts', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: TSizes.spaceBtwInputFields / 2),
            Obx(
              () => Wrap(
                spacing: TSizes.sm,
                runSpacing: TSizes.sm,
                children: controller.selectedAccounts.map((account) {
                  return TChoiceChip(
                    color: Colors.green,
                    text: account.name,
                    selected: true,
                    onSelected: (value) => controller.toggleAccountSelection(account),
                  );
                }).toList(),
              ),
            ),
            const SizedBox(height: TSizes.spaceBtwInputFields),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: () => _showAccountSelectionDialog(context, controller),
                icon: const Icon(Iconsax.add),
                label: const Text('Add Accounts'),
              ),
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Handlers
            Text('Select Handlers', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: TSizes.spaceBtwInputFields / 2),
            Obx(() {
              final userController = Get.find<UserController>();
              if (userController.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              return Wrap(
                spacing: TSizes.sm,
                runSpacing: TSizes.sm,
                children: userController.allItems.map((user) {
                  return TChoiceChip(
                    color: Colors.blue,
                    text: user.fullName,
                    selected: controller.selectedHandlers.contains(user),
                    onSelected: (value) => controller.toggleHandlerSelection(user),
                  );
                }).toList(),
              );
            }),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(onPressed: () => controller.createContact(), child: const Text('Create Contact')),
            ),
          ],
        ),
      ),
    );
  }
}

// Updated dialog call functions
void _showAccountSelectionDialog(BuildContext context, CreateContactController controller) {
  Get.dialog(
    AccountSelectionDialog(
      initialSelectedAccounts: controller.selectedAccounts.toList(),
      onAccountsSelected: (selectedList) {
        controller.selectedAccounts.assignAll(selectedList);
      },
    ),
  );
}

void _showHandlerSelectionDialog(BuildContext context, CreateContactController controller) {
  Get.dialog(
    HandlerSelectionDialog(
      initialSelectedHandlers: controller.selectedHandlers.toList(),
      onHandlersSelected: (selectedList) {
        controller.selectedHandlers.assignAll(selectedList);
      },
    ),
  );
}
