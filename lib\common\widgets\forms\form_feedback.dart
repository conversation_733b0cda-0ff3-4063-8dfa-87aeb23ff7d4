import 'package:flutter/material.dart';
import '../../../utils/constants/sizes.dart';
import '../containers/rounded_container.dart';

/// Enhanced form feedback component for success, error, and loading states
class TFormFeedback extends StatelessWidget {
  const TFormFeedback({
    super.key,
    required this.type,
    required this.message,
    this.title,
    this.actions,
    this.onDismiss,
    this.showIcon = true,
    this.isCollapsible = false,
    this.duration,
  });

  final FormFeedbackType type;
  final String message;
  final String? title;
  final List<Widget>? actions;
  final VoidCallback? onDismiss;
  final bool showIcon;
  final bool isCollapsible;
  final Duration? duration;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final config = _getTypeConfig(theme);

    Widget content = TRoundedContainer(
      backgroundColor: config.backgroundColor,
      borderColor: config.borderColor,
      padding: const EdgeInsets.all(TSizes.md),
      child: Row(
        children: [
          if (showIcon) ...[
            Icon(
              config.icon,
              color: config.iconColor,
              size: 24,
            ),
            const SizedBox(width: TSizes.spaceBtwItems),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (title != null) ...[
                  Text(
                    title!,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: config.textColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: TSizes.xs),
                ],
                Text(
                  message,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: config.textColor,
                  ),
                ),
                if (actions != null && actions!.isNotEmpty) ...[
                  const SizedBox(height: TSizes.spaceBtwItems),
                  Row(
                    children: actions!,
                  ),
                ],
              ],
            ),
          ),
          if (onDismiss != null) ...[
            const SizedBox(width: TSizes.spaceBtwItems),
            IconButton(
              onPressed: onDismiss,
              icon: Icon(
                Icons.close,
                color: config.iconColor,
                size: 20,
              ),
              padding: EdgeInsets.zero,
              constraints: const BoxConstraints(),
            ),
          ],
        ],
      ),
    );

    if (duration != null) {
      return TAutoHideFeedback(
        duration: duration!,
        onDismiss: onDismiss,
        child: content,
      );
    }

    return content;
  }

  _FeedbackConfig _getTypeConfig(ThemeData theme) {
    switch (type) {
      case FormFeedbackType.success:
        return _FeedbackConfig(
          backgroundColor: Colors.green.withOpacity(0.1),
          borderColor: Colors.green.withOpacity(0.3),
          iconColor: Colors.green,
          textColor: Colors.green.shade800,
          icon: Icons.check_circle_outline,
        );
      case FormFeedbackType.error:
        return _FeedbackConfig(
          backgroundColor: theme.colorScheme.error.withOpacity(0.1),
          borderColor: theme.colorScheme.error.withOpacity(0.3),
          iconColor: theme.colorScheme.error,
          textColor: theme.colorScheme.error,
          icon: Icons.error_outline,
        );
      case FormFeedbackType.warning:
        return _FeedbackConfig(
          backgroundColor: Colors.orange.withOpacity(0.1),
          borderColor: Colors.orange.withOpacity(0.3),
          iconColor: Colors.orange,
          textColor: Colors.orange.shade800,
          icon: Icons.warning_outlined,
        );
      case FormFeedbackType.info:
        return _FeedbackConfig(
          backgroundColor: theme.primaryColor.withOpacity(0.1),
          borderColor: theme.primaryColor.withOpacity(0.3),
          iconColor: theme.primaryColor,
          textColor: theme.primaryColor,
          icon: Icons.info_outline,
        );
      case FormFeedbackType.loading:
        return _FeedbackConfig(
          backgroundColor: theme.cardColor,
          borderColor: theme.dividerColor,
          iconColor: theme.primaryColor,
          textColor: theme.textTheme.bodyMedium?.color ?? Colors.black,
          icon: Icons.hourglass_empty,
        );
    }
  }
}

/// Auto-hiding feedback component
class TAutoHideFeedback extends StatefulWidget {
  const TAutoHideFeedback({
    super.key,
    required this.duration,
    required this.child,
    this.onDismiss,
  });

  final Duration duration;
  final Widget child;
  final VoidCallback? onDismiss;

  @override
  State<TAutoHideFeedback> createState() => _TAutoHideFeedbackState();
}

class _TAutoHideFeedbackState extends State<TAutoHideFeedback>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(_controller);

    _controller.forward();

    Future.delayed(widget.duration, () {
      if (mounted) {
        _controller.reverse().then((_) {
          widget.onDismiss?.call();
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: widget.child,
    );
  }
}

/// Loading overlay for forms
class TFormLoadingOverlay extends StatelessWidget {
  const TFormLoadingOverlay({
    super.key,
    required this.isLoading,
    required this.child,
    this.loadingMessage = 'Processing...',
    this.backgroundColor,
  });

  final bool isLoading;
  final Widget child;
  final String loadingMessage;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isLoading)
          Container(
            color: backgroundColor ?? Colors.black.withOpacity(0.5),
            child: Center(
              child: TRoundedContainer(
                backgroundColor: Theme.of(context).cardColor,
                padding: const EdgeInsets.all(TSizes.lg),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: TSizes.spaceBtwItems),
                    Text(
                      loadingMessage,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// Form validation summary component
class TFormValidationSummary extends StatelessWidget {
  const TFormValidationSummary({
    super.key,
    required this.errors,
    this.title = 'Please fix the following errors:',
    this.onErrorTap,
  });

  final List<String> errors;
  final String title;
  final void Function(String error)? onErrorTap;

  @override
  Widget build(BuildContext context) {
    if (errors.isEmpty) return const SizedBox.shrink();

    final theme = Theme.of(context);

    return TRoundedContainer(
      backgroundColor: theme.colorScheme.error.withOpacity(0.1),
      borderColor: theme.colorScheme.error.withOpacity(0.3),
      padding: const EdgeInsets.all(TSizes.md),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: theme.colorScheme.error,
                size: 20,
              ),
              const SizedBox(width: TSizes.spaceBtwItems),
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  color: theme.colorScheme.error,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: TSizes.spaceBtwItems),
          ...errors.map((error) => Padding(
            padding: const EdgeInsets.only(bottom: TSizes.xs),
            child: GestureDetector(
              onTap: onErrorTap != null ? () => onErrorTap!(error) : null,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '• ',
                    style: TextStyle(color: theme.colorScheme.error),
                  ),
                  Expanded(
                    child: Text(
                      error,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.error,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          )),
        ],
      ),
    );
  }
}

enum FormFeedbackType {
  success,
  error,
  warning,
  info,
  loading,
}

class _FeedbackConfig {
  final Color backgroundColor;
  final Color borderColor;
  final Color iconColor;
  final Color textColor;
  final IconData icon;

  _FeedbackConfig({
    required this.backgroundColor,
    required this.borderColor,
    required this.iconColor,
    required this.textColor,
    required this.icon,
  });
}
