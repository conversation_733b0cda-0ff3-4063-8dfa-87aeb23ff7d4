import 'package:alloy/data/abstract/base_data_table_controller.dart';
import 'package:alloy/features/brand/models/brand_model.dart';
import 'package:alloy/features/brand/repository/brand_repository.dart';
import 'package:alloy/features/category/models/category_model.dart';
import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:get/get.dart';

class BrandController extends TBaseController<BrandModel> {
  static BrandController get instance => Get.find();

  final _brandRepository = Get.put(BrandRepository());
  final _categoryController = Get.put(CategoryController());

  @override
  Future<List<BrandModel>> fetchItems() async {
    // Fetch all brands
    final fetchedBrands = await _brandRepository.getAllBrands();

    // Fetch all categories if data is not already fetched
    if (_categoryController.allItems.isEmpty) {
      await _categoryController.fetchItems();
    }

    // Fetch all brand categories
    final fetchedBrandCategories = await _brandRepository.getAllBrandCategories();

    // Map brand categories to brands
    for (final brand in fetchedBrands) {
      // Extract categoryIds from the documents
      List<String> categoryIds = fetchedBrandCategories
          .where((brandCategory) => brandCategory.brandId == brand.id)
          .map((brandCategory) => brandCategory.categoryId)
          .toList();

      // Fetch categories using categoryIds. And store them in the brand model's List<CategoryModel>? brandCategories
      brand.brandCategories = _categoryController.allItems.where((category) => categoryIds.contains(category.id)).toList();
    }
    // fetchedBrands will now have the brandCategories list populated including the category details
    return fetchedBrands;
  }

  @override
  bool containsSearchQuery(BrandModel item, String searchText) {
    return item.name.toLowerCase().contains(searchText.toLowerCase());
  }

  @override
  Future<void> deleteItem(BrandModel item) {
    return _brandRepository.deleteBrand(item);
  }

  /// Sorting related code
  void sortByName(int sortColumnIndex, bool isAscending) {
    sortByProperty(sortColumnIndex, isAscending, (item) => item.name.toLowerCase());
  }

  void sortByFeatured(int sortColumnIndex, bool isAscending) {
    sortByProperty(
      sortColumnIndex,
      isAscending,
      (item) => (item.isFeatured) ? 1 : 0, // Convert bool? to int (1 for true, 0 for false/null)
    );
  }

  void sortByCreatedAt(int sortColumnIndex, bool isAscending) {
    sortByProperty(sortColumnIndex, isAscending, (item) => item.createdAt!);
  }

  void sortByUpdatedAt(int sortColumnIndex, bool isAscending) {
    sortByProperty(sortColumnIndex, isAscending, (item) => item.updatedAt!);
  }

  @override
  Stream<List<BrandModel>> streamItems() {
    // This implementation assumes that your BrandRepository has a streamAllBrands() method.
    // It uses an async* generator to fetch related data and then yield lists of brands with their categories.
    return _streamBrandsWithCategories();
  }

  Stream<List<BrandModel>> _streamBrandsWithCategories() async* {
    // Ensure categories are loaded first.
    if (_categoryController.allItems.isEmpty) {
      await _categoryController.fetchItems();
    }

    // Fetch the mapping table. For full reactivity, this could also be a stream combined with rxdart.
    final brandCategories = await _brandRepository.getAllBrandCategories();
    final categoryMap = {for (var category in _categoryController.allItems) category.id: category};

    // Listen to the stream of brands from the repository
    await for (final brands in _brandRepository.streamAllBrands()) {
      // For each batch of brands, populate the category details
      for (final brand in brands) {
        final categoryIds = brandCategories.where((bc) => bc.brandId == brand.id).map((bc) => bc.categoryId).toList();
        brand.brandCategories = categoryIds.map((id) => categoryMap[id]).where((c) => c != null).cast<CategoryModel>().toList();
      }
      yield brands;
    }
  }

  @override
  Comparable getComparableProperty(BrandModel item, int columnIndex) {
    // Implement this method to return a Comparable value for sorting based on column index
    // Ensure you always return a String, int, double, or DateTime.
    // Handle null values by providing a default comparable value (e.g., empty string, 0).
    switch (columnIndex) {
      case 0:
        return item.name.toLowerCase(); // Brand Name
      case 1:
        // Categories: Sort by the name of the first category, or empty string if no categories
        return item.brandCategories?.firstOrNull?.name.toLowerCase() ?? '';
      case 2: // Featured (bool is Comparable)
        return item.isFeatured ? 1 : 0; // Convert bool to int for comparability
      case 3:
        return item.createdAt ?? DateTime(0); // Date (assuming createdAt is DateTime, default to epoch if null)
      case 4:
        return ''; // Action column is not sortable
      default:
        return ''; // Fallback for undefined columns
    }
  }
}
