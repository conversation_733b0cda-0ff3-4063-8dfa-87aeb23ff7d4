import 'dart:async';

import 'package:alloy/features/authentication/repository/user_repository.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart';

class UserController extends GetxController {
  static UserController get instance => Get.find();

  final _userRepository = UserRepository.instance;
  final isLoading = false.obs;

  final user = UserModel.empty().obs;
  final allItems = <UserModel>[].obs; // To store all users for selection
  final filteredItems = <UserModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    // Listen to auth changes to start/stop user data stream
    FirebaseAuth.instance.authStateChanges().listen(_authStatusChanged);
    // Fetch all users when the controller is initialized.
    fetchItems();
    // Initialize filteredItems with all items
    allItems.listen((users) {
      filteredItems.assignAll(users);
    });
  }

  /// Called when auth state changes.
  /// Binds the user data stream to the 'user' Rx variable.
  void _authStatusChanged(User? firebaseUser) {
    if (firebaseUser != null) {
      // If the user is logged in, bind the user stream to the 'user' variable.
      // GetX will automatically handle the subscription lifecycle (subscribing and disposing).
      user.bindStream(_userRepository.streamUserRecord(firebaseUser.uid));
    } else {
      // If the user is logged out, reset the user to an empty model.
      user.value = UserModel.empty();
    }
  }

  /// Get User Data (one-time fetch, useful for initial load or as a fallback).
  /// This remains important for synchronous-like checks during login/redirect.
  Future<UserModel> getUserData() async {
    try {
      // This is a one-time fetch and does not update the controller's state.
      // The stream bound in _authStatusChanged is responsible for state updates.
      final userData = await _userRepository.fetchUserData();
      return userData;
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
      return UserModel.empty();
    }
  }

  /// Create User (delegates to UserRepository)
  void createUser(UserModel newUser) async {
    try {
      await _userRepository.createUser(newUser);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  /// Fetch all user data (for selection in other modules)
  Future<void> fetchItems() async {
    try {
      isLoading.value = true;
      final users = await _userRepository.getAllUsers();
      allItems.assignAll(users);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: 'Could not fetch all users: $e');
    } finally {
      isLoading.value = false;
    }
  }

  void searchQuery(String query) {
    if (query.isEmpty) {
      filteredItems.assignAll(allItems);
    } else {
      filteredItems.assignAll(
        allItems.where((user) => user.fullName.toLowerCase().contains(query.toLowerCase())).toList(),
      );
    }
  }
}
