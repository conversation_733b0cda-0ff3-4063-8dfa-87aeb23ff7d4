import 'dart:io';

import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import '../../../controller/product_controller/edit_product_controller.dart';
import '../../../models/product_model.dart';

class EditProductDesktop extends StatelessWidget {
  // Changed to StatelessWidget for simplicity with GetX
  const EditProductDesktop({super.key, required this.product});

  final ProductModel product;

  @override
  Widget build(BuildContext context) {
    // Initialize the EditProductController with the product to be edited
    final controller = Get.put(EditProductController(initialProduct: product));
    final categoryController =
        Get.find<CategoryController>(); // CategoryController is still needed for dropdown options

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              TBreadcrumbsWithHeading(
                heading: 'Edit Product: ${product.name}',
                breadcrumbItems: [
                  TBreadcrumbItem(text: 'Products', route: TRoutes.products),
                  TBreadcrumbItem(text: 'Edit Product'),
                ],
                showBackButton: true,
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              TRoundedContainer(
                padding: const EdgeInsets.all(TSizes.defaultSpace),
                child: Form(
                  key: controller.formKey, // Use the formKey from EditProductController
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Product Details', style: Theme.of(context).textTheme.headlineSmall),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Product Name
                      TextFormField(
                        controller: controller.name, // Bind to controller's TextEditingController
                        decoration: const InputDecoration(labelText: 'Product Name', prefixIcon: Icon(Iconsax.box)),
                        validator: (value) => TValidator.validateEmptyText('Product Name', value),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Product Description
                      TextFormField(
                        controller: controller.description, // Bind to controller's TextEditingController
                        decoration: const InputDecoration(labelText: 'Description', prefixIcon: Icon(Iconsax.note)),
                        maxLines: 3,
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Category Dropdown
                      Obx(
                        () => DropdownButtonFormField<String>(
                          value: controller.selectedCategoryId.value.isEmpty
                              ? null
                              : controller.selectedCategoryId.value,
                          decoration: const InputDecoration(labelText: 'Category', prefixIcon: Icon(Iconsax.category)),
                          items: categoryController.allItems.map((category) {
                            return DropdownMenuItem(value: category.id, child: Text(category.name));
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedCategoryId.value = value;
                            }
                          },
                          validator: (value) => TValidator.validateEmptyText('Category', value),
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Segment Dropdown
                      Obx(
                        () => DropdownButtonFormField<ProductSegment>(
                          value: controller.selectedSegment.value,
                          decoration: const InputDecoration(labelText: 'Segment', prefixIcon: Icon(Iconsax.tag)),
                          items: ProductSegment.values.map((segment) {
                            return DropdownMenuItem(value: segment, child: Text(segment.name));
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedSegment.value = value;
                            }
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'Please select a segment.';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Product Status Dropdown
                      Obx(
                        () => DropdownButtonFormField<ProductStatus>(
                          value: controller.status.value,
                          decoration: const InputDecoration(labelText: 'Status', prefixIcon: Icon(Iconsax.activity)),
                          items: ProductStatus.values.map((status) {
                            return DropdownMenuItem(value: status, child: Text(status.name));
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              controller.status.value = value;
                            }
                          },
                          validator: (value) {
                            if (value == null) {
                              return 'Please select a status.';
                            }
                            return null;
                          },
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Dimensions (Width & Height) - Conditionally disabled
                      Obx(() {
                        // Disable if status is active (or any other status you define as "locked")
                        final bool dimensionsDisabled = controller.status.value == ProductStatus.active;
                        return Row(
                          children: [
                            Expanded(
                              child: TextFormField(
                                controller: controller.width, // Bind to controller's TextEditingController
                                decoration: const InputDecoration(
                                  labelText: 'Width (mm)',
                                  prefixIcon: Icon(Iconsax.ruler),
                                ),
                                keyboardType: TextInputType.number,
                                validator: (value) => TValidator.validateEmptyText(value, 'Width'),
                                enabled: !dimensionsDisabled, // Disable based on status
                              ),
                            ),
                            const SizedBox(width: TSizes.spaceBtwInputFields),
                            Expanded(
                              child: TextFormField(
                                controller: controller.height, // Bind to controller's TextEditingController
                                decoration: const InputDecoration(
                                  labelText: 'Height (mm)',
                                  prefixIcon: Icon(Iconsax.ruler),
                                ),
                                keyboardType: TextInputType.number,
                                validator: (value) => TValidator.validateEmptyText(value, 'Height'),
                                enabled: !dimensionsDisabled, // Disable based on status
                              ),
                            ),
                          ],
                        );
                      }),
                      const SizedBox(height: TSizes.spaceBtwInputFields),

                      // Thumbnail Upload Section
                      Text('Product Thumbnail (Optional)', style: Theme.of(context).textTheme.titleMedium),
                      const SizedBox(height: TSizes.spaceBtwItems),
                      Obx(
                        () => Row(
                          children: [
                            // Display selected image or placeholder
                            TRoundedContainer(
                              width: 100,
                              height: 100,
                              //color: Colors.grey.shade200,
                              child: controller.thumbnailPath.value.isNotEmpty
                                  ? (controller.thumbnailPath.value.startsWith('http') // Check if it's a URL
                                        ? Image.network(
                                            controller.thumbnailPath.value,
                                            fit: BoxFit.cover,
                                            errorBuilder: (context, error, stackTrace) =>
                                                const Icon(Icons.broken_image, size: 50, color: Colors.grey),
                                          )
                                        : (kIsWeb // If not a URL, check if on web (it's a local blob/data URL)
                                              ? Image.network(
                                                  controller
                                                      .thumbnailPath
                                                      .value, // Use Image.network for local web paths
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error, stackTrace) =>
                                                      const Icon(Icons.broken_image, size: 50, color: Colors.grey),
                                                )
                                              : Image.file(
                                                  File(
                                                    controller.thumbnailPath.value,
                                                  ), // Use Image.file for other platforms
                                                  fit: BoxFit.cover,
                                                  errorBuilder: (context, error, stackTrace) =>
                                                      const Icon(Icons.broken_image, size: 50, color: Colors.grey),
                                                )))
                                  : const Icon(Iconsax.image, size: 50, color: Colors.grey),
                            ),
                            const SizedBox(width: TSizes.spaceBtwItems),
                            // Upload Button
                            Expanded(
                              child: OutlinedButton(
                                onPressed: () => controller.pickAndUploadThumbnail(),
                                child: const Text('Upload Thumbnail'),
                              ),
                            ),
                            // Clear Button (optional, if you want to allow removing thumbnail)
                            if (controller.thumbnailPath.value.isNotEmpty) ...[
                              const SizedBox(width: TSizes.spaceBtwItems),
                              IconButton(
                                icon: const Icon(Iconsax.close_circle, color: Colors.red),
                                onPressed: () => controller.thumbnailPath.value = '', // Clear the path
                                tooltip: 'Clear Thumbnail',
                              ),
                            ],
                          ],
                        ),
                      ),
                      const SizedBox(height: TSizes.spaceBtwSections),

                      // Save Changes Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => controller.updateProduct(), // Call the update method on the new controller
                          child: const Text('Save Changes'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
