import 'dart:io';

import 'package:alloy/features/category/controller/category_controller.dart';
import 'package:alloy/features/products/models/product_model.dart';
import 'package:alloy/features/products/repository/product_repository.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../data/repositories/storage_repository.dart';
import '../../screens/product_variants/product_variants_screen.dart';

class CreateProductController extends GetxController {
  static CreateProductController get instance => Get.find();

  // --- Repositories & Other Controllers
  final _productRepository = Get.put(ProductRepository());
  final categoryController = Get.put(CategoryController()); // Ensure CategoryController is initialized
  final _storageRepository = Get.put(StorageRepository()); // StorageRepository

  // --- Form State
  final formKey = GlobalKey<FormState>();
  final isLoading = false.obs;
  final currentStep = 0.obs;
  final formErrors = <String>[].obs;
  final isFormValid = false.obs;

  // --- Product Template Fields
  final name = TextEditingController();
  final description = TextEditingController();
  final width = TextEditingController();
  final height = TextEditingController();
  final selectedCategoryId = ''.obs; // Holds the ID of the selected category
  final selectedSegment = ProductSegment.lengths.obs; // Holds the selected product segment

  // Thumbnail (for image upload)
  final RxString thumbnailPath = ''.obs; // Stores local path of selected image or uploaded URL
  final Rxn<XFile> _selectedXFile = Rxn<XFile>(); // NEW: Store the XFile for later upload

  @override
  void onInit() {
    super.onInit();
    // Fetch categories when the controller initializes, if not already loaded
    if (categoryController.allItems.isEmpty) {
      categoryController.fetchItems();
    }
  }

  @override
  void onClose() {
    name.dispose();
    description.dispose();
    width.dispose();
    height.dispose();
    super.onClose();
  }

  /// --- Create Product Template
  Future<void> createProduct() async {
    try {
      // Start Loading
      TFullScreenLoader.popUpCircular();

      // Check Internet Connectivity
      final isConnected = await NetworkManager.instance.isConnected();
      if (!isConnected) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Form Validation
      if (!formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        return;
      }

      // Basic validation for dropdowns
      if (selectedCategoryId.value.isEmpty) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'Category Required', message: 'Please select a category for the product.');
        return;
      }

      // Corrected: Implement Thumbnail Image Upload to Firebase Storage
      String? uploadedThumbnailUrl;
      if (_selectedXFile.value != null) {
        // Use _selectedXFile for upload
        if (kIsWeb) {
          final bytes = await _selectedXFile.value!.readAsBytes();
          uploadedThumbnailUrl = await _storageRepository.uploadFile(
            'Products/Thumbnails/${DateTime.now().millisecondsSinceEpoch}_${_selectedXFile.value!.name}',
            bytes, // Pass bytes for web
          );
        } else {
          final file = File(_selectedXFile.value!.path);
          if (await file.exists()) {
            uploadedThumbnailUrl = await _storageRepository.uploadFile(
              'Products/Thumbnails/${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}',
              file, // Pass File for non-web
            );
          } else {
            TLoaders.warningSnackBar(title: 'Invalid Image', message: 'Selected image file not found.');
            TFullScreenLoader.stopLoading();
            return;
          }
        }
      }

      // Create the Product Model
      final product = ProductModel(
        id: '', // Firestore will generate this
        name: name.text.trim(),
        description: description.text.trim(),
        categoryId: selectedCategoryId.value,
        segment: selectedSegment.value,
        width: double.tryParse(width.text) ?? 0.0,
        height: double.tryParse(height.text) ?? 0.0,
        thumbnail: uploadedThumbnailUrl, // Assign the uploaded URL
      );

      // Create the product in Firestore
      final productId = await _productRepository.createProduct(product);

      // Stop Loading
      TFullScreenLoader.stopLoading();

      // Show Success Message
      TLoaders.successSnackBar(title: 'Success', message: 'Product template "${product.name}" created successfully.');

      // Reset form fields
      resetForm();

      // Navigate to the Product Variants screen for the newly created product
      final createdProductWithId = product.copyWith(id: productId);
      Get.off(() => ProductVariantsScreen(product: createdProductWithId));
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  /// Resets all form fields and selections.
  void resetForm() {
    formKey.currentState?.reset(); // Resets the form state
    name.clear();
    description.clear();
    width.clear();
    height.clear();
    selectedCategoryId.value = '';
    selectedSegment.value = ProductSegment.lengths;
    thumbnailPath.value = '';
    _selectedXFile.value = null;
    currentStep.value = 0;
    formErrors.clear();
    isFormValid.value = false;
  }

  /// Validates basic information step
  bool validateBasicInfo() {
    formErrors.clear();

    if (name.text.trim().isEmpty) {
      formErrors.add('Product name is required');
    }

    if (selectedCategoryId.value.isEmpty) {
      formErrors.add('Category selection is required');
    }

    if (selectedSegment.value == ProductSegment.lengths) {
      formErrors.add('Product segment selection is required');
    }

    return formErrors.isEmpty;
  }

  /// Validates specifications step
  bool validateSpecifications() {
    formErrors.clear();

    if (width.text.trim().isEmpty) {
      formErrors.add('Width is required');
    } else {
      final widthValue = double.tryParse(width.text);
      if (widthValue == null || widthValue <= 0) {
        formErrors.add('Width must be a positive number');
      }
    }

    if (height.text.trim().isEmpty) {
      formErrors.add('Height is required');
    } else {
      final heightValue = double.tryParse(height.text);
      if (heightValue == null || heightValue <= 0) {
        formErrors.add('Height must be a positive number');
      }
    }

    return formErrors.isEmpty;
  }

  /// Validates media step (optional)
  bool validateMedia() {
    // Media step is optional, so always return true
    formErrors.clear();
    return true;
  }

  /// Final validation before creating product
  bool validateFinalStep() {
    return validateBasicInfo() && validateSpecifications() && validateMedia();
  }

  /// Picks an image from gallery or camera and updates thumbnailPath.
  Future<void> pickAndUploadThumbnail() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? pickedFile = await picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 70,
      ); // Reduced quality for faster upload

      if (pickedFile != null) {
        thumbnailPath.value = pickedFile.path; // Store local path temporarily
        TLoaders.successSnackBar(title: 'Image Selected', message: 'Thumbnail image selected successfully.');
      } else {
        TLoaders.warningSnackBar(title: 'No Image Selected', message: 'No image was selected for the thumbnail.');
      }
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: 'Failed to pick image: $e');
    }
  }
}
