import 'package:flutter/material.dart';

// Import your responsive settings screens
import '../../../common/layouts/templates/site_template.dart';
import 'responsive_screens/settings_desktop.dart';
import 'responsive_screens/settings_mobile.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    // Use your TSiteTemplate to manage responsiveness
    return const TSiteTemplate(
      desktop: SettingsDesktop(),
      tablet: SettingsDesktop(), // Reusing desktop layout for tablet for now
      mobile: SettingsMobile(),
    );
  }
}
