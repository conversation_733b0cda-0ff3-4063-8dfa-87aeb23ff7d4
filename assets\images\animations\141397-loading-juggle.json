{"v": "5.9.6", "fr": 25, "ip": 0, "op": 67, "w": 320, "h": 240, "nm": "Juggling", "ddd": 0, "assets": [], "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Ball13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.958], "y": [0.961]}, "o": {"x": [0.042], "y": [0.295]}, "t": 11, "s": [197.875]}, {"i": {"x": [0.762], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [160]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 34, "s": [122.375]}, {"i": {"x": [0.958], "y": [0.888]}, "o": {"x": [0.042], "y": [0.221]}, "t": 44, "s": [122.375]}, {"i": {"x": [0.958], "y": [0.694]}, "o": {"x": [0.167], "y": [0.167]}, "t": 56, "s": [160]}, {"t": 67, "s": [197.875]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.303], "y": [1]}, "o": {"x": [0.042], "y": [0.08]}, "t": 11, "s": [136]}, {"i": {"x": [0.904], "y": [0.826]}, "o": {"x": [0.55], "y": [0]}, "t": 17, "s": [59.5]}, {"i": {"x": [0.293], "y": [1]}, "o": {"x": [0.052], "y": [0.404]}, "t": 23, "s": [136]}, {"i": {"x": [0.917], "y": [0.589]}, "o": {"x": [0.601], "y": [0]}, "t": 29, "s": [153.898]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 34, "s": [136]}, {"i": {"x": [0.504], "y": [1]}, "o": {"x": [0.042], "y": [0.119]}, "t": 44, "s": [136]}, {"i": {"x": [0.958], "y": [0.791]}, "o": {"x": [0.671], "y": [0]}, "t": 50, "s": [59.5]}, {"i": {"x": [0.252], "y": [1]}, "o": {"x": [0.042], "y": [0.386]}, "t": 56, "s": [136]}, {"i": {"x": [0.95], "y": [0.773]}, "o": {"x": [0.707], "y": [0]}, "t": 62, "s": [153.898]}, {"t": 67, "s": [136]}], "ix": 4}}, "a": {"a": 0, "k": [-43, 48.25, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [0.809, 0.809, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.099, -0.099, 0]}, "t": 0, "s": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.574, -0.574, 0]}, "t": 3, "s": [105, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 6, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 11, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 14, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 17, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 21, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 23, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.852, 0.852, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [110, 90, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.809, 0.809, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.099, -0.099, 0]}, "t": 34, "s": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.574, -0.574, 0]}, "t": 37, "s": [105, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 40, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 44, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 47, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 50, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 54, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 56, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.852, 0.852, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 61, "s": [110, 90, 100]}, {"t": 67, "s": [90, 110, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [32, 32], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.380392156863, 0.505882352941, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-43, 32.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 100, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Ball12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.762], "y": [1]}, "o": {"x": [0.055], "y": [0.444]}, "t": 0, "s": [160]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [122.375]}, {"i": {"x": [0.958], "y": [0.888]}, "o": {"x": [0.042], "y": [0.221]}, "t": 22, "s": [122.375]}, {"i": {"x": [0.958], "y": [0.694]}, "o": {"x": [0.167], "y": [0.167]}, "t": 34, "s": [160]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [197.875]}, {"i": {"x": [0.958], "y": [0.961]}, "o": {"x": [0.042], "y": [0.295]}, "t": 55, "s": [197.875]}, {"t": 67, "s": [160]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.293], "y": [1]}, "o": {"x": [0.05], "y": [0.479]}, "t": 0, "s": [136]}, {"i": {"x": [0.917], "y": [0.507]}, "o": {"x": [0.601], "y": [0]}, "t": 6, "s": [153.898]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [136]}, {"i": {"x": [0.282], "y": [1]}, "o": {"x": [0.042], "y": [0.097]}, "t": 22, "s": [136]}, {"i": {"x": [0.96], "y": [0.894]}, "o": {"x": [0.561], "y": [0]}, "t": 28, "s": [59.5]}, {"i": {"x": [0.252], "y": [1]}, "o": {"x": [0.042], "y": [0.534]}, "t": 34, "s": [136]}, {"i": {"x": [0.946], "y": [0.692]}, "o": {"x": [0.707], "y": [0]}, "t": 40, "s": [153.898]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 45, "s": [136]}, {"i": {"x": [0.357], "y": [1]}, "o": {"x": [0.054], "y": [0.123]}, "t": 55, "s": [136]}, {"i": {"x": [0.958], "y": [0.891]}, "o": {"x": [0.625], "y": [0]}, "t": 61, "s": [59.5]}, {"t": 67, "s": [136]}], "ix": 4}}, "a": {"a": 0, "k": [-43, 48.25, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 5, "s": [110, 90, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.809, 0.809, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.099, -0.099, 0]}, "t": 12, "s": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.574, -0.574, 0]}, "t": 15, "s": [105, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 18, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 22, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 25, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 28, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 32, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 34, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.852, 0.852, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 39, "s": [110, 90, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.809, 0.809, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.099, -0.099, 0]}, "t": 45, "s": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.574, -0.574, 0]}, "t": 48, "s": [105, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 51, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 55, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 58, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 61, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 65, "s": [80, 120, 100]}, {"t": 67, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [32, 32], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.380392156863, 0.505882352941, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-43, 32.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 100, "st": 0, "ct": 1, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Ball11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"s": true, "x": {"a": 1, "k": [{"i": {"x": [0.958], "y": [0.888]}, "o": {"x": [0.042], "y": [0.221]}, "t": 0, "s": [122.375]}, {"i": {"x": [0.958], "y": [0.694]}, "o": {"x": [0.167], "y": [0.167]}, "t": 12, "s": [160]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 23, "s": [197.875]}, {"i": {"x": [0.958], "y": [0.961]}, "o": {"x": [0.042], "y": [0.295]}, "t": 33, "s": [197.875]}, {"i": {"x": [0.762], "y": [1]}, "o": {"x": [0.167], "y": [0.167]}, "t": 45, "s": [160]}, {"t": 56, "s": [122.375]}], "ix": 3}, "y": {"a": 1, "k": [{"i": {"x": [0.463], "y": [1]}, "o": {"x": [0.042], "y": [0.084]}, "t": 0, "s": [136]}, {"i": {"x": [0.979], "y": [0.939]}, "o": {"x": [0.509], "y": [0]}, "t": 6, "s": [59.5]}, {"i": {"x": [0.422], "y": [1]}, "o": {"x": [0.05], "y": [0.512]}, "t": 12, "s": [136]}, {"i": {"x": [0.95], "y": [0.483]}, "o": {"x": [0.479], "y": [0]}, "t": 17, "s": [154]}, {"i": {"x": [0.833], "y": [1]}, "o": {"x": [0.167], "y": [0]}, "t": 23, "s": [136]}, {"i": {"x": [0.421], "y": [1]}, "o": {"x": [0.042], "y": [0.059]}, "t": 33, "s": [136]}, {"i": {"x": [0.949], "y": [0.9]}, "o": {"x": [0.707], "y": [0]}, "t": 39, "s": [59.5]}, {"i": {"x": [0.293], "y": [1]}, "o": {"x": [0.042], "y": [0.349]}, "t": 45, "s": [136]}, {"i": {"x": [0.917], "y": [0.589]}, "o": {"x": [0.601], "y": [0]}, "t": 51, "s": [153.898]}, {"t": 56, "s": [136]}], "ix": 4}}, "a": {"a": 0, "k": [-43, 48.25, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 3, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 6, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 10, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 12, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.852, 0.852, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 17, "s": [110, 90, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.809, 0.809, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.099, -0.099, 0]}, "t": 23, "s": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.574, -0.574, 0]}, "t": 26, "s": [105, 95, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0, 0, 0]}, "t": 29, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 33, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 36, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 39, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 43, "s": [80, 120, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 45, "s": [100, 100, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.852, 0.852, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 50, "s": [110, 90, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [0.809, 0.809, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.099, -0.099, 0]}, "t": 56, "s": [90, 110, 100]}, {"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [-0.574, -0.574, 0]}, "t": 59, "s": [105, 95, 100]}, {"t": 62, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [32, 32], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "Ellipse Path 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [1, 1, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 0, "ix": 5}, "lc": 1, "lj": 1, "ml": 4, "bm": 0, "nm": "Stroke 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.380392156863, 0.505882352941, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "Fill 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [-43, 32.25], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "Transform"}], "nm": "Ellipse 1", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 100, "st": 0, "ct": 1, "bm": 0}], "markers": []}