import 'package:alloy/features/products/controller/variant_controller/product_variant_controller.dart';
import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../common/widgets/data_table/paginated_data_table.dart';
import '../../../models/product_model.dart';
import '../../../models/product_variant_model.dart';
import 'table_source.dart';

class ProductVariantsTable extends StatelessWidget {
  const ProductVariantsTable({super.key, required this.parentProduct});

  final ProductModel parentProduct; // The parent product whose variants are being displayed

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<ProductVariantController>();

    return Obx(() {
      // OPTIMIZED: Direct reactive listening without unnecessary widgets
      // Display a loading indicator if data is loading and no items are present yet
      if (controller.isLoading.value && controller.filteredItems.isEmpty) {
        return const Center(child: CircularProgressIndicator());
      }

      // Display a message if no variants are found after loading
      if (controller.filteredItems.isEmpty && !controller.isLoading.value) {
        return const Center(child: Text('No variants found for this product or search query.'));
      }

      return TPaginatedDataTable(
        // Removed minWidth: 1200 to allow for more flexible width adjustment
        sortAscending: controller.sortAscending.value,
        sortColumnIndex: controller.sortColumnIndex.value,
        columns: [
          DataColumn2(
            label: const Text('Material'),
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductVariantModel variant) => variant.attributes['Material']?.toLowerCase() ?? '',
            ),
            size: ColumnSize.M, // Set size to Medium
          ),
          DataColumn2(
            label: const Text('Thickness'),
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductVariantModel variant) =>
                  double.tryParse(variant.attributes['Thickness']?.replaceAll('mm', '') ?? '0') ?? 0.0,
            ),
            size: ColumnSize.M, // Set size to Medium
          ),
          DataColumn2(
            label: const Text('Finish'),
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductVariantModel variant) => variant.attributes['Finish']?.toLowerCase() ?? '',
            ),
            size: ColumnSize.M, // Set size to Medium
          ),
          DataColumn2(
            label: const Text('Length'),
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductVariantModel variant) =>
                  double.tryParse(variant.attributes['Length']?.replaceAll('m', '') ?? '0') ?? 0.0,
            ),
            size: ColumnSize.M, // Set size to Medium
          ),
          DataColumn2(
            label: const Text('Weight (KG)'),
            numeric: true,
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductVariantModel variant) => variant.weight ?? 0.0,
            ),
            size: ColumnSize.S, // Set size to Small
          ),
          DataColumn2(
            label: const Text('SQM'),
            numeric: true,
            onSort: (columnIndex, ascending) =>
                controller.sortByProperty(columnIndex, ascending, (ProductVariantModel variant) => variant.sqm ?? 0.0),
            size: ColumnSize.S, // Set size to Small
          ),
          DataColumn2(
            label: const Text('On Hand'),
            numeric: true,
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductVariantModel variant) => variant.quantityOnHand,
            ),
            size: ColumnSize.S, // Set size to Small
          ),
          DataColumn2(
            label: const Text('On Order'),
            numeric: true,
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductVariantModel variant) => variant.quantityOnOrder,
            ),
            size: ColumnSize.S, // Set size to Small
          ),
          DataColumn2(
            label: const Text('In Production'),
            numeric: true,
            onSort: (columnIndex, ascending) => controller.sortByProperty(
              columnIndex,
              ascending,
              (ProductVariantModel variant) => variant.quantityInProduction,
            ),
            size: ColumnSize.S, // Set size to Small
          ),
          DataColumn2(
            label: const Text('SKU'),

            size: ColumnSize.L, // Set size to Large for SKU
          ),
          const DataColumn2(label: Text('Actions'), fixedWidth: 100), // Actions column typically not sortable
        ],
        source: ProductVariantsRows(parentProduct: parentProduct), // Directly use the DataTableSource
      );
    });
  }
}
