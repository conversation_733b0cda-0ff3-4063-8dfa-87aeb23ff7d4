import 'package:clipboard/clipboard.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/images/t_rounded_image.dart';
import 'package:alloy/common/media/controller/media_controller.dart';
import 'package:alloy/common/media/models/image_model.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:alloy/utils/device/device_utility.dart';

// --- The ImagePopup Widget ---
class ImagePopup extends StatelessWidget {
  final ImageModel image; // The image model to display detailed information about.

  const ImagePopup({super.key, required this.image}); // Constructor for the ImagePopup class.

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      // The image model to display detailed information about.
      child: Dialog(
        // Define the shape of the dialog.
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(TSizes.cardRadiusLg)), // Define the shape of the dialog.
        child: TRoundedContainer(
          // Set the width of the rounded container based on the screen size.
          width: TDeviceUtils.isDesktopScreen(context) ? MediaQuery.of(context).size.width * 0.4 : double.infinity,
          padding: const EdgeInsets.all(TSizes.spaceBtwItems),
          child: Column(
            mainAxisSize: MainAxisSize.min, // Make column content wrap its children
            crossAxisAlignment: CrossAxisAlignment.start, // Align content to the start
            children: [
              // Display the image with an option to close the dialog.
              SizedBox(
                child: Stack(
                  // Display the image with rounded container.
                  children: [
                    TRoundedContainer(
                      // Display the image with rounded container.
                      backgroundColor: TColors.primaryBackground,
                      child: TRoundedImage(
                        image: image.url,
                        applyImageRadius: true,
                        height: MediaQuery.of(context).size.height * 0.4,
                        width: TDeviceUtils.isDesktopScreen(context) ? MediaQuery.of(context).size.width * 0.4 : double.infinity,
                        imageType: ImageType.network, // Assuming network image from URL
                      ), // TRoundedImage
                    ), // TRoundedContainer
                    Positioned(
                      // Close icon button positioned at the top-right corner.
                      top: 0,
                      right: 0,
                      child: IconButton(onPressed: () => Get.back(), icon: const Icon(Iconsax.close_circle)),
                    ),
                  ],
                ), // Stack
              ), // SizedBox
              const Divider(),
              const SizedBox(height: TSizes.spaceBtwItems),

              // Display various metadata about the image.
              // Includes image name, path, type, size, creation and modification dates, and URL.
              // Also provides an option to copy the image URL.
              Row(
                children: [
                  Expanded(child: Text('Image Name:', style: Theme.of(context).textTheme.bodyLarge)),
                  Expanded(flex: 3, child: Text(image.filename, style: Theme.of(context).textTheme.titleLarge)),
                ],
              ), // Row

              const SizedBox(height: TSizes.sm),

              // Display the image URL with an option to copy it.
              Row(
                children: [
                  Expanded(child: Text('Image URL:', style: Theme.of(context).textTheme.bodyLarge)),
                  Expanded(
                    flex: 2,
                    child: Text(image.url, style: Theme.of(context).textTheme.titleLarge, maxLines: 1, overflow: TextOverflow.ellipsis),
                  ),
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () {
                        FlutterClipboard.copy(image.url).then((value) => TLoaders.customToast(message: 'URL copied!'));
                      },
                      child: const Text('Copy URL'),
                    ),
                  ),
                ],
              ), // Row

              const SizedBox(height: TSizes.sm),

              // Add other metadata rows from the screenshot's comments:
              // - path (folder)
              // - type (contentType)
              // - size (sizeBytes)
              // - creation date (createdAtFormatted)
              // - modification date (updatedAtFormatted)
              _buildMetadataRow(context, 'Folder:', image.folder),
              // _buildMetadataRow(context, 'Type:', image.contentType ?? 'N/A'),
              _buildMetadataRow(context, 'Size:', '${((image.sizeBytes ?? 0) / 1024 / 1024).toStringAsFixed(3)} MB'),
              _buildMetadataRow(context, 'Created At:', image.createdAtFormatted),
              _buildMetadataRow(context, 'Last Modified:', image.updatedAtFormatted),

              const Divider(),
              const SizedBox(height: TSizes.spaceBtwItems),

              // Delete Image button
              Center(
                child: SizedBox(
                  width: 250,
                  child: TextButton(
                    onPressed: () => MediaController.instance.removeCloudImageConfirmation(image),
                    child: const Text('Delete Image', style: TextStyle(color: Colors.red)),
                  ),
                ),
              ),
            ],
          ), // Column
        ), // TRoundedContainer
      ), // Dialog
    ); // SingleChildScrollView
  }

  // Helper method to build consistent metadata rows
  Widget _buildMetadataRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: TSizes.sm / 2),
      child: Row(
        children: [
          Expanded(child: Text(label, style: Theme.of(context).textTheme.bodyLarge)),
          Expanded(flex: 3, child: Text(value, style: Theme.of(context).textTheme.bodyMedium)),
        ],
      ),
    );
  }
}
