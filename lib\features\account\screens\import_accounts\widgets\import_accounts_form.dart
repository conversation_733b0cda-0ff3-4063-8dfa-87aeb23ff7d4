import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/features/account/controller/import_accounts_controller.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class ImportAccountsForm extends StatelessWidget {
  const ImportAccountsForm({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ImportAccountsController());
    return TRoundedContainer(
      width: 600,
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      child: Column(
        children: [
          Text('Import from Excel', style: Theme.of(context).textTheme.headlineMedium),
          const SizedBox(height: TSizes.spaceBtwSections),
          Text(
            'Upload an .xlsx file with account data. Ensure the columns are in the following order: Name, BusinessType, Country, Emirates, Phone.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: TSizes.spaceBtwSections),
          Obx(
            () => controller.isLoading.value
                ? const CircularProgressIndicator()
                : SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () => controller.pickAndImportFile(),
                      icon: const Icon(Iconsax.document_upload),
                      label: const Text('Select and Import File'),
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}
