import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/common/widgets/popups/popup_menu_button.dart';
import 'package:alloy/features/contact/controller/contact_controller.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/reporting/controllers/report_controller.dart';
import '../../../../../common/widgets/containers/rounded_container.dart';
import '../../../../../common/widgets/data_table/table_header.dart';
import '../data_table/contacts_table.dart';

class ContactsDesktop extends StatelessWidget {
  const ContactsDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    final reportController = Get.put(ReportController()); // Initialize if not already put
    final controller = Get.find<ContactController>();
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Contacts Management',
                breadcrumbItems: [TBreadcrumbItem(text: 'Contacts')],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Table Body
              TRoundedContainer(
                child: Column(
                  children: [
                    // --- Header Row for All Actions and Search ---
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start, // Align items to the start
                      crossAxisAlignment: CrossAxisAlignment.center, // Vertically center all items
                      children: [
                        // Printing Menu (Popup Button)
                        popUpMenuButton(reportController),
                        const SizedBox(width: TSizes.spaceBtwItems), // Space after popup menu
                        // Create New Contact Button
                        ElevatedButton(
                          onPressed: () => Get.toNamed(TRoutes.createContact),
                          child: const Text('   Create New Contact   '),
                        ),
                        const SizedBox(width: TSizes.spaceBtwItems), // Space after create button
                        // Import Button
                        ElevatedButton.icon(
                          onPressed: () => Get.toNamed(TRoutes.importContacts),
                          icon: const Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8.0),
                            child: Icon(Iconsax.document_upload),
                          ),
                          label: const Text('Import   '),
                        ),

                        // Search Bar
                        Expanded(
                          flex: 3, // Adjust flex as needed for search bar width
                          child: TTableHeader(
                            hintText: 'Search Contacs',
                            searchOnChanged: (query) => controller.searchQuery(query),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: TSizes.spaceBtwItems),

                    // Table
                    Obx(() {
                      if (controller.isLoading.value) return const Center(child: CircularProgressIndicator());
                      return const ContactsTable();
                    }),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
