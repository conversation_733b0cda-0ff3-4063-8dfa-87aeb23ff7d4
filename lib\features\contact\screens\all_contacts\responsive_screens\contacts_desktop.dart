import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/common/widgets/popups/popup_menu_button.dart';
import 'package:alloy/features/contact/controller/contact_controller.dart';
import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

import '../../../../../common/reporting/controllers/report_controller.dart';

class ContactsDesktop extends StatelessWidget {
  const ContactsDesktop({super.key});

  @override
  Widget build(BuildContext context) {
    final reportController = Get.put(ReportController());
    final controller = Get.find<ContactController>();

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Contacts Management',
                breadcrumbItems: [TBreadcrumbItem(text: 'Contacts')],
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Header Section with Actions and Search
              Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: TColors.white,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: TColors.darkGrey.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Title and Stats Row
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: TColors.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(Iconsax.people, color: TColors.primary, size: 24),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Contacts Directory',
                                style: Theme.of(
                                  context,
                                ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold, color: TColors.dark),
                              ),
                              Obx(
                                () => Text(
                                  '${controller.filteredItems.length} contacts found',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: TColors.darkGrey),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),

                    // Actions and Search Row
                    Row(
                      children: [
                        // Action Buttons
                        ElevatedButton.icon(
                          onPressed: () => Get.toNamed(TRoutes.createContact),
                          icon: const Icon(Iconsax.user_add, size: 18),
                          label: const Text('Create Contact'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: TColors.primary,
                            foregroundColor: TColors.white,
                            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          ),
                        ),
                        const SizedBox(width: 12),
                        OutlinedButton.icon(
                          onPressed: () => Get.toNamed(TRoutes.importContacts),
                          icon: const Icon(Iconsax.document_upload, size: 18),
                          label: const Text('Import'),
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                          ),
                        ),
                        const SizedBox(width: 12),
                        popUpMenuButton(reportController),

                        const Spacer(),

                        // Search Bar
                        SizedBox(
                          width: 300,
                          child: TextField(
                            onChanged: (query) => controller.searchQuery(query),
                            decoration: InputDecoration(
                              hintText: 'Search contacts...',
                              prefixIcon: const Icon(Iconsax.search_normal, size: 20),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: TColors.borderPrimary),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: BorderSide(color: TColors.borderPrimary),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(12),
                                borderSide: const BorderSide(color: TColors.primary),
                              ),
                              filled: true,
                              fillColor: TColors.lightGrey.withValues(alpha: 0.3),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: TSizes.spaceBtwSections),

              // Contacts Grid
              Obx(() {
                if (controller.isLoading.value) {
                  return const Center(
                    child: Padding(padding: EdgeInsets.all(50), child: CircularProgressIndicator()),
                  );
                }

                if (controller.filteredItems.isEmpty) {
                  return Container(
                    padding: const EdgeInsets.all(50),
                    decoration: BoxDecoration(
                      color: TColors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: TColors.darkGrey.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Column(
                        children: [
                          Icon(Iconsax.people, size: 64, color: TColors.darkGrey.withValues(alpha: 0.5)),
                          const SizedBox(height: 16),
                          Text(
                            'No contacts found',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(color: TColors.darkGrey),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Create your first contact or adjust your search criteria',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: TColors.darkGrey),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                return GridView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 3,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1.3,
                  ),
                  itemCount: controller.filteredItems.length,
                  itemBuilder: (context, index) {
                    final contact = controller.filteredItems[index];
                    return _buildContactCard(context, contact);
                  },
                );
              }),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContactCard(BuildContext context, ContactModel contact) {
    return Container(
      decoration: BoxDecoration(
        color: TColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(color: TColors.darkGrey.withValues(alpha: 0.1), blurRadius: 8, offset: const Offset(0, 2)),
        ],
        border: Border.all(color: TColors.borderPrimary.withValues(alpha: 0.1), width: 1),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => Get.toNamed(TRoutes.editContact, arguments: contact),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with Avatar and Status
                Row(
                  children: [
                    Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: TColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Center(
                        child: Text(
                          contact.name.isNotEmpty ? contact.name[0].toUpperCase() : 'C',
                          style: Theme.of(
                            context,
                          ).textTheme.titleLarge?.copyWith(color: TColors.primary, fontWeight: FontWeight.bold),
                        ),
                      ),
                    ),
                    const Spacer(),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: contact.active
                            ? TColors.success.withValues(alpha: 0.1)
                            : TColors.error.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        contact.active ? 'Active' : 'Inactive',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: contact.active ? TColors.success : TColors.error,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // Contact Name and Title
                Text(
                  contact.name,
                  style: Theme.of(
                    context,
                  ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: TColors.dark),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                if (contact.title?.isNotEmpty == true) ...[
                  const SizedBox(height: 4),
                  Text(contact.title!, style: Theme.of(context).textTheme.bodySmall?.copyWith(color: TColors.darkGrey)),
                ],
                const SizedBox(height: 8),

                // Designation
                if (contact.designation?.isNotEmpty == true) ...[
                  Row(
                    children: [
                      Icon(Iconsax.briefcase, size: 14, color: TColors.darkGrey),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          contact.designation!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: TColors.darkGrey),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                ],

                // Contact Info
                Row(
                  children: [
                    Icon(Iconsax.call, size: 14, color: TColors.darkGrey),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        contact.phone,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(color: TColors.darkGrey),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
                if (contact.email?.isNotEmpty == true) ...[
                  const SizedBox(height: 6),
                  Row(
                    children: [
                      Icon(Iconsax.sms, size: 14, color: TColors.darkGrey),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          contact.email!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(color: TColors.darkGrey),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ],

                const Spacer(),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => Get.toNamed(TRoutes.editContact, arguments: contact),
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          side: BorderSide(color: TColors.primary),
                        ),
                        child: Text('Edit', style: TextStyle(color: TColors.primary, fontSize: 12)),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Container(
                      decoration: BoxDecoration(
                        color: TColors.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: IconButton(
                        onPressed: () => _showContactActions(context, contact),
                        icon: const Icon(Iconsax.more, size: 16),
                        color: TColors.primary,
                        constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _showContactActions(BuildContext context, ContactModel contact) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Iconsax.edit),
              title: const Text('Edit Contact'),
              onTap: () {
                Get.back();
                Get.toNamed(TRoutes.editContact, arguments: contact);
              },
            ),
            ListTile(
              leading: const Icon(Iconsax.trash),
              title: const Text('Delete Contact'),
              onTap: () {
                Get.back();
                _confirmDelete(context, contact);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDelete(BuildContext context, ContactModel contact) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Contact'),
        content: Text('Are you sure you want to delete ${contact.name}?'),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('Cancel')),
          ElevatedButton(
            onPressed: () {
              Get.back();
              final controller = Get.find<ContactController>();
              controller.deleteItem(contact);
            },
            style: ElevatedButton.styleFrom(backgroundColor: TColors.error),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
