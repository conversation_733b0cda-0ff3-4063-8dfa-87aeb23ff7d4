import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/brand/screens/all_brand/responsive_screens/brands_desktop.dart';
import 'package:alloy/features/brand/screens/all_brand/responsive_screens/brands_mobile.dart';
import 'package:alloy/features/brand/screens/all_brand/responsive_screens/brands_tablet.dart';
import 'package:flutter/material.dart';

class BrandsScreen extends StatelessWidget {
  const BrandsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(
      useLayout: true,
      desktop: BrandsDesktop(),
      tablet: BrandsTablet(),
      mobile: BrandsMobile(),
    );
  }
}
