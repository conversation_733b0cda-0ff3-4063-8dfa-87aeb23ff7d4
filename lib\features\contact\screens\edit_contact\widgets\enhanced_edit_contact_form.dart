import 'package:alloy/common/widgets/forms/enhanced_text_field.dart';
import 'package:alloy/common/widgets/forms/enhanced_dropdown.dart';
import 'package:alloy/common/widgets/forms/form_section.dart';
import 'package:alloy/features/contact/controller/edit_contact_controller.dart';
import 'package:alloy/features/authentication/controllers/user_controller.dart';
import 'package:alloy/common/widgets/chips/rounded_choice_chips.dart';
import 'package:alloy/features/contact/screens/create_contact/widgets/account_selection_dialog.dart';
import 'package:alloy/features/contact/screens/create_contact/widgets/handler_selection_dialog.dart';
import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/utils/constants/lists.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/validators/validation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class EnhancedEditContactForm extends StatelessWidget {
  const EnhancedEditContactForm({super.key, required this.contact});

  final ContactModel contact;

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EditContactController());
    controller.initializeForm(contact);

    return Container(
      constraints: const BoxConstraints(maxWidth: 800),
      padding: const EdgeInsets.all(TSizes.defaultSpace),
      decoration: BoxDecoration(
        color: TColors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: TColors.darkGrey.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Form(
        key: controller.formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: TColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Iconsax.user_edit, color: TColors.primary, size: 24),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Edit Contact',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: TColors.dark,
                        ),
                      ),
                      Text(
                        'Update contact information and settings',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: TColors.darkGrey,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Section 1: Basic Information
            TFormSection(
              title: 'Basic Information',
              icon: Iconsax.user,
              isRequired: true,
              children: [
                Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: TEnhancedTextField(
                        controller: controller.name,
                        labelText: 'Full Name',
                        hintText: 'Enter contact\'s full name',
                        prefixIcon: Iconsax.user,
                        isRequired: true,
                        validator: (value) => TValidator.validateEmptyText('Name', value),
                      ),
                    ),
                    const SizedBox(width: TSizes.spaceBtwInputFields),
                    Expanded(
                      child: TEnhancedDropdown<String>(
                        value: controller.title.text.isNotEmpty ? controller.title.text : null,
                        items: TLists.salutations,
                        onChanged: (value) => controller.title.text = value ?? '',
                        labelText: 'Title',
                        hintText: 'Select title',
                        prefixIcon: Iconsax.user_octagon,
                        helperText: 'Optional: Mr., Mrs., Dr., etc.',
                      ),
                    ),
                  ],
                ),
                TEnhancedDropdown<String>(
                  value: controller.designation.text.isNotEmpty ? controller.designation.text : null,
                  items: TLists.designations,
                  onChanged: (value) => controller.designation.text = value ?? '',
                  labelText: 'Designation',
                  hintText: 'Select job designation',
                  prefixIcon: Iconsax.briefcase,
                  helperText: 'Contact\'s job title or position',
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Section 2: Contact Information
            TFormSection(
              title: 'Contact Information',
              icon: Iconsax.call,
              isRequired: true,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: TEnhancedTextField(
                        controller: controller.email,
                        labelText: 'Email Address',
                        hintText: 'Enter email address',
                        prefixIcon: Iconsax.sms,
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) => TValidator.validateEmail(value),
                        helperText: 'Primary email for communication',
                      ),
                    ),
                    const SizedBox(width: TSizes.spaceBtwInputFields),
                    Expanded(
                      child: TEnhancedTextField(
                        controller: controller.phone,
                        labelText: 'Mobile Phone',
                        hintText: 'Enter mobile number',
                        prefixIcon: Iconsax.call,
                        isRequired: true,
                        keyboardType: TextInputType.phone,
                        validator: (value) => TValidator.validateEmptyText('Phone', value),
                      ),
                    ),
                  ],
                ),
                TEnhancedTextField(
                  controller: controller.landline,
                  labelText: 'Landline Number',
                  hintText: 'Enter landline number (optional)',
                  prefixIcon: Iconsax.call_calling,
                  keyboardType: TextInputType.phone,
                  helperText: 'Optional: Office or home landline number',
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Section 3: Account & Handler Assignment
            TFormSection(
              title: 'Account & Handler Assignment',
              icon: Iconsax.people,
              children: [
                // Account Selection
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Iconsax.building, size: 16, color: TColors.primary),
                        const SizedBox(width: 8),
                        Text(
                          'Associated Accounts',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: TColors.dark,
                          ),
                        ),
                        const Spacer(),
                        TextButton.icon(
                          onPressed: () => _showAccountSelectionDialog(context, controller),
                          icon: const Icon(Iconsax.edit, size: 16),
                          label: const Text('Edit Accounts'),
                          style: TextButton.styleFrom(
                            foregroundColor: TColors.primary,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: TColors.lightGrey.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: TColors.borderPrimary.withValues(alpha: 0.1)),
                      ),
                      child: Obx(() => controller.selectedAccounts.isEmpty
                          ? Text(
                              'No accounts selected',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: TColors.darkGrey,
                                fontStyle: FontStyle.italic,
                              ),
                            )
                          : Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: controller.selectedAccounts
                                  .map((account) => TRoundedChoiceChip(
                                        text: account.name,
                                        selected: true,
                                        onSelected: (_) => controller.selectedAccounts.remove(account),
                                      ))
                                  .toList(),
                            )),
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                
                // Handler Selection
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Iconsax.user_tag, size: 16, color: TColors.primary),
                        const SizedBox(width: 8),
                        Text(
                          'Assigned Handlers',
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: TColors.dark,
                          ),
                        ),
                        const Spacer(),
                        TextButton.icon(
                          onPressed: () => _showHandlerSelectionDialog(context, controller),
                          icon: const Icon(Iconsax.edit, size: 16),
                          label: const Text('Edit Handlers'),
                          style: TextButton.styleFrom(
                            foregroundColor: TColors.primary,
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: TColors.lightGrey.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: TColors.borderPrimary.withValues(alpha: 0.1)),
                      ),
                      child: Obx(() => controller.selectedHandlers.isEmpty
                          ? Text(
                              'No handlers assigned',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: TColors.darkGrey,
                                fontStyle: FontStyle.italic,
                              ),
                            )
                          : Wrap(
                              spacing: 8,
                              runSpacing: 8,
                              children: controller.selectedHandlers
                                  .map((handler) => TRoundedChoiceChip(
                                        text: handler.fullName,
                                        selected: true,
                                        onSelected: (_) => controller.selectedHandlers.remove(handler),
                                      ))
                                  .toList(),
                            )),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Section 4: Status Settings
            TFormSection(
              title: 'Status Settings',
              icon: Iconsax.setting_2,
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: TColors.lightGrey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: TColors.borderPrimary.withValues(alpha: 0.1)),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: TColors.primary.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(Iconsax.status, color: TColors.primary, size: 18),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Active Status',
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.w600,
                                color: TColors.dark,
                              ),
                            ),
                            Text(
                              'Enable this contact for use in the system',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: TColors.darkGrey,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Obx(() => Switch(
                        value: controller.active.value,
                        onChanged: (value) => controller.active.value = value,
                        activeColor: TColors.primary,
                      )),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: TSizes.spaceBtwSections),

            // Action Buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Get.back(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(color: TColors.darkGrey),
                    ),
                    child: const Text('Cancel'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: () => controller.updateContact(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: TColors.primary,
                      foregroundColor: TColors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: const Text('Update Contact'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showAccountSelectionDialog(BuildContext context, EditContactController controller) {
    Get.dialog(
      AccountSelectionDialog(
        initialSelectedAccounts: controller.selectedAccounts.toList(),
        onAccountsSelected: (selectedAccounts) {
          controller.selectedAccounts.assignAll(selectedAccounts);
        },
      ),
    );
  }

  void _showHandlerSelectionDialog(BuildContext context, EditContactController controller) {
    Get.dialog(
      HandlerSelectionDialog(
        initialSelectedHandlers: controller.selectedHandlers.toList(),
        onHandlersSelected: (selectedHandlers) {
          controller.selectedHandlers.assignAll(selectedHandlers);
        },
      ),
    );
  }
}
