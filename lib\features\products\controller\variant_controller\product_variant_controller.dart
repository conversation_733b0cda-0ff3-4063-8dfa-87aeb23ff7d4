import 'package:alloy/data/abstract/base_data_table_controller.dart';
import 'package:alloy/features/products/models/product_variant_model.dart';
import 'package:alloy/features/products/repository/product_variant_repository.dart';
import 'package:get/get.dart';

import '../../../../utils/constants/enums.dart';
import '../../../../utils/popups/loaders.dart';

class ProductVariantController extends TBaseController<ProductVariantModel> {
  static ProductVariantController get instance => Get.find();

  final _variantRepository = ProductVariantRepository.instance; // Get the instance

  final currentProductId = ''.obs; // The ID of the product whose variants are currently loaded

  // Reactive variable to control visibility of discontinued variants
  final RxBool showDiscontinuedVariants = false.obs;

  ProductVariantController(); // Constructor

  /// Sets the product ID and triggers fetching of variants by re-listening to the stream.
  void loadVariantsForProduct(String productId) {
    if (currentProductId.value != productId) {
      // Only update if product ID changes
      currentProductId.value = productId;
      listenToStream(); // Re-bind the stream for the new product ID
    }
  }

  @override
  Future<List<ProductVariantModel>> fetchItems() async {
    // This method is primarily for one-time fetches if needed, not used with streams.
    // If you implement a getAllVariantsForProduct in repository, you'd call it here.
    return [];
  }

  @override
  Stream<List<ProductVariantModel>> streamItems() {
    // Simplified: This stream now directly depends on currentProductId.value.
    // The reactivity is managed by loadVariantsForProduct calling listenToStream()
    // whenever currentProductId changes, which re-subscribes to this stream.
    if (currentProductId.value.isEmpty) {
      return Stream.value([]); // Return empty list if no product ID is set
    }
    return _variantRepository.streamVariantsForProduct(currentProductId.value).distinct().map((variants) {
      List<ProductVariantModel> filteredVariants = [];
      for (final variant in variants) {
        // Filter based on showDiscontinuedVariants toggle
        if (showDiscontinuedVariants.value || variant.status != ProductStatus.discontinued) {
          filteredVariants.add(variant);
        }
      }
      return filteredVariants;
    });
  }

  @override
  bool containsSearchQuery(ProductVariantModel item, String searchText) {
    final searchLower = searchText.toLowerCase();
    if (item.sku.toLowerCase().contains(searchLower)) return true;
    // Search through all attribute values
    for (final value in item.attributes.values) {
      if (value.toLowerCase().contains(searchLower)) return true;
    }
    // Search by status
    if (item.status.name.toLowerCase().contains(searchLower)) return true;

    return false;
  }

  @override
  Future<void> deleteItem(ProductVariantModel item) async {
    try {
      await _variantRepository.deleteVariant(item.id);
    } catch (e) {
      print('Error deleting variant: $e');
      rethrow;
    }
  }

  /// --- Update Product Variant ---
  /// This method is called by EditProductVariantController to save changes.
  Future<void> updateVariant(ProductVariantModel variant) async {
    try {
      await _variantRepository.updateVariant(variant);
    } catch (e) {
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: 'Failed to update variant: $e');
      rethrow; // Re-throw to allow caller (EditProductVariantController) to catch
    }
  }

  @override
  Comparable getComparableProperty(ProductVariantModel item, int columnIndex) {
    // Implement this method to return a Comparable value for sorting based on column index
    // Ensure you always return a String, int, double, or DateTime.
    // Handle null values by providing a default comparable value (e.g., empty string, 0).
    switch (columnIndex) {
      case 0:
        return item.sku.toLowerCase(); // SKU
      case 1:
        return item.attributes['Material']?.toLowerCase() ?? ''; // Material
      case 2:
        // Thickness: Convert to double for numeric sorting
        return double.tryParse(item.attributes['Thickness']?.replaceAll('mm', '') ?? '0') ?? 0.0;
      case 3:
        return item.attributes['Finish']?.toLowerCase() ?? ''; // Finish
      case 4:
        // Length: Convert to double for numeric sorting
        return double.tryParse(item.attributes['Length']?.replaceAll('m', '') ?? '0') ?? 0.0;
      case 5:
        return item.weight ?? 0.0; // Weight (KG)
      case 6:
        return item.sqm ?? 0.0; // SQM
      case 7:
        return item.quantityOnHand; // On Hand
      case 8:
        return item.quantityOnOrder; // On Order
      case 9:
        return item.quantityInProduction; // In Production
      case 10:
        return item.status.name.toLowerCase(); // Status (assuming it's the last data column before actions)
      case 11:
        return ''; // Actions column is not sortable
      default:
        return ''; // Fallback for undefined columns
    }
  }
}
