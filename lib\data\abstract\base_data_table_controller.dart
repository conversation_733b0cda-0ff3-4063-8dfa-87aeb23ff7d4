import 'dart:async';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../utils/popups/full_screen_loader.dart';

/// A base controller for managing data in tables, providing common functionalities
/// like loading, filtering, sorting, and stream listening.
///
/// [T] is the type of the data model (e.g., ProductModel, AccountModel, ProductVariantModel).
abstract class TBaseController<T> extends GetxController {
  final RxBool isLoading = true.obs; // Tracks if data is currently loading
  final RxList<T> allItems = <T>[].obs; // Stores all fetched items
  final RxList<T> filteredItems = <T>[].obs; // Stores items after search/filter applied

  // Add selectedRows property
  final RxList<bool> selectedRows = <bool>[].obs; // Tracks selection state of filtered items

  // --- Sorting State ---
  final RxInt sortColumnIndex = 0.obs; // Index of the column currently sorted
  final RxBool sortAscending = true.obs; // Sorting direction (true for ascending)

  // --- Search State ---
  final RxString _currentSearchQuery = ''.obs; // Renamed to avoid conflict with method

  // --- Pagination State (if using TPaginatedDataTable) ---
  // You might need these if TPaginatedDataTable manages internal pagination logic
  // final RxInt rowsPerPage = PaginatedDataTable.defaultRowsPerPage.obs;
  // final RxInt pageIndex = 0.obs;

  // --- Stream Subscription ---
  // Using a Rx variable to hold the stream subscription, allowing it to be easily managed
  // and disposed when the controller is closed or the stream needs to be changed.
  final Rx<Stream<List<T>>?> _dataStream = Rx<Stream<List<T>>?>(null);
  final Rxn<StreamSubscription<List<T>>> _streamSubscription = Rxn<StreamSubscription<List<T>>>();

  @override
  void onInit() {
    super.onInit();
    // Start listening to the data stream when the controller initializes
    listenToStream();

    // Debounce the search query to avoid excessive filtering on every keystroke
    debounce(_currentSearchQuery, (_) => _applyFilter(), time: const Duration(milliseconds: 300));
  }

  @override
  void onClose() {
    _streamSubscription.value?.cancel(); // Cancel any existing subscription
    super.onClose();
  }

  /// Abstract method to fetch items (for one-time fetches, less common with streams).
  /// Implement this if you need a non-stream based data retrieval.
  Future<List<T>> fetchItems();

  /// Abstract method to provide the stream of items.
  /// This is where you connect to your repository's stream.
  Stream<List<T>> streamItems();

  /// Starts or re-starts listening to the data stream.
  void listenToStream() {
    isLoading.value = true;
    _streamSubscription.value?.cancel(); // Cancel any existing subscription

    _dataStream.value = streamItems(); // Get the new stream
    _streamSubscription.value = _dataStream.value?.listen(
      (items) {
        allItems.assignAll(items); // Update allItems
        _applyFilter(); // Apply current filter/search to new data
        isLoading.value = false;
      },
      onError: (error) {
        isLoading.value = false;
        // Handle error, e.g., show a snackbar
        Get.snackbar('Error', 'Failed to load data: $error', snackPosition: SnackPosition.BOTTOM);
        print('Error in stream: $error');
      },
    );
  }

  /// Sets the search query and triggers filtering.
  void searchQuery(String searchText) {
    _currentSearchQuery.value = searchText; // Set the value of the RxString
  }

  /// Applies the current search query to filter items.
  void _applyFilter() {
    if (_currentSearchQuery.value.isEmpty) {
      filteredItems.assignAll(allItems);
    } else {
      filteredItems.assignAll(allItems.where((item) => containsSearchQuery(item, _currentSearchQuery.value)).toList());
    }
    // Reset selection when items are filtered
    selectedRows.assignAll(List<bool>.filled(filteredItems.length, false));
    // Re-apply sorting after filtering
    _applySort();
  }

  /// Abstract method to define how an item matches a search query.
  /// Implement this in concrete controllers (e.g., ProductController, AccountController).
  bool containsSearchQuery(T item, String searchText);

  /// Sorts the filtered items based on the selected column and direction.
  ///
  /// [columnIndex]: The index of the column to sort by.
  /// [ascending]: True if sorting in ascending order, false for descending.
  /// [getProperty]: A function that extracts the comparable property from an item.
  void sortByProperty<P extends Comparable>(int columnIndex, bool ascending, P Function(T item) getProperty) {
    sortColumnIndex.value = columnIndex;
    sortAscending.value = ascending;

    _applySort(getProperty: getProperty);
  }

  /// Internal helper to apply sorting.
  /// It can optionally take a `getProperty` function if called directly after a sort event,
  /// or it will use the last applied sorting logic.
  void _applySort({Comparable Function(T item)? getProperty}) {
    // Changed P Function to Comparable Function
    if (sortColumnIndex.value >= 0 && filteredItems.isNotEmpty) {
      filteredItems.sort((a, b) {
        // Ensure aValue and bValue are non-null and Comparable
        final Comparable aValue = getProperty != null ? getProperty(a) : getComparableProperty(a, sortColumnIndex.value);
        final Comparable bValue = getProperty != null ? getProperty(b) : getComparableProperty(b, sortColumnIndex.value);

        final comparison = aValue.compareTo(bValue);
        return sortAscending.value ? comparison : -comparison;
      });
    }
  }

  /// Internal helper to get a comparable property based on column index.
  /// This method MUST be overridden in concrete controllers to map column index to actual data properties.
  /// It must always return a Comparable value (e.g., String, int, double, DateTime).
  /// If a property can be null, return a default Comparable value (e.g., '', 0.0) instead of null.
  ///
  /// Example override:
  /// @override
  /// Comparable _getComparableProperty(ProductModel item, int columnIndex) {
  ///   switch (columnIndex) {
  ///     case 0: return item.name;
  ///     case 1: return item.category?.name ?? ''; // Handles null category name
  ///     case 2: return item.status.name; // Convert enum to its name (String)
  ///     // ... other cases
  ///     default: return ''; // Fallback for undefined columns
  ///   }
  /// }
  Comparable getComparableProperty(T item, int columnIndex); // Changed to public method
  /// Abstract method to delete a specific item.
  /// Implement this in concrete controllers to interact with the repository.
  Future<void> deleteItem(T item);

  /// Update an item in the RxLists locally for optimistic UI updates.
  void updateItemInRxList(T updatedItem) {
    // This assumes T has an 'id' property.
    // Ensure your T models (like ProductModel, AccountModel) have an 'id' getter.
    // If your models are immutable, ensure you are creating new instances for updates.
    final dynamic updatedItemId = (updatedItem as dynamic).id; // Assuming 'id' field
    final indexInAll = allItems.indexWhere((item) => (item as dynamic).id == updatedItemId);
    if (indexInAll != -1) {
      allItems[indexInAll] = updatedItem;
      // Re-apply filter and sort to ensure the updated item appears correctly
      // in the filtered and sorted list. This will also reset selectedRows.
      _applyFilter();
    } else {
      // If the item wasn't found in allItems, it might be a new item not yet
      // reflected by the stream, or an item that was previously filtered out.
      // For new items, the stream will eventually add it. For updates,
      // this means the item wasn't in the initial fetched set.
      print('Warning: Item with ID $updatedItemId not found in allItems for optimistic update.');
    }
  }

  /// Confirms deletion of an item with a dialog.
  Future<void> confirmDeleteItem(T item) async {
    Get.defaultDialog(
      title: 'Delete Confirmation',
      middleText: 'Are you sure you want to delete this item?',
      textConfirm: 'Delete',
      textCancel: 'Cancel',
      confirmTextColor: Colors.white,
      cancelTextColor: Colors.black,
      buttonColor: Colors.red,
      onConfirm: () async {
        Get.back(); // Close the dialog
        TFullScreenLoader.popUpCircular(); // Show loading
        try {
          await deleteItem(item); // Call the concrete deleteItem
          TFullScreenLoader.stopLoading();
          TLoaders.successSnackBar(title: 'Success', message: 'Item deleted successfully!');
        } catch (e) {
          TFullScreenLoader.stopLoading();
          TLoaders.errorSnackBar(title: 'Error', message: 'Failed to delete item: $e');
        }
      },
    );
  }
}
