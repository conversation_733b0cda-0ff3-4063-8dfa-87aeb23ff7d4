import 'package:alloy/routes/routes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:alloy/common/widgets/breadcrumbs/breadcrumbs_with_heading.dart';
import 'package:alloy/common/widgets/containers/rounded_container.dart';
import 'package:alloy/common/widgets/data_table/table_header.dart';
import 'package:alloy/features/brand/controller/brand_controller.dart';
import 'package:alloy/features/brand/screens/all_brand/table/brands_table.dart';
import 'package:alloy/utils/constants/sizes.dart';

class BrandsTablet extends StatelessWidget {
  const BrandsTablet({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(BrandController());

    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.all(TSizes.defaultSpace),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Breadcrumbs
              const TBreadcrumbsWithHeading(
                heading: 'Brands',
                breadcrumbItems: [TBreadcrumbItem(text: 'Brands')],
              ),
              SizedBox(height: TSizes.spaceBtwSections),

              // Table Body
              TRoundedContainer(
                child: Column(
                  children: [
                    // Table Header
                    TTableHeader(
                      hintText: 'Search Brands',
                      searchOnChanged: (query) => controller.searchQuery(query),
                      actions: [ElevatedButton(onPressed: () => Get.toNamed(TRoutes.createContact), child: const Text('Create New Brand'))],
                    ),
                    SizedBox(height: TSizes.spaceBtwItems),

                    // Table
                    Obx(() {
                      // Show Loader
                      if (controller.isLoading.value) {
                        return const Center(child: CircularProgressIndicator());
                      }
                      // Show Table
                      return const BrandsTable();
                    }),
                  ],
                ), // Column
              ), // TRoundedContainer
            ],
          ), // Column
        ), // Padding
      ), // SingleChildScrollView
    ); // Scaffold
  }
}
