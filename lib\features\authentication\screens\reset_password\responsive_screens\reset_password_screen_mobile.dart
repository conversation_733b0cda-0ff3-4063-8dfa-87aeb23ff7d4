import 'package:alloy/features/authentication/screens/reset_password/widgets/reset_password_widget.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';

class ResetPasswordScreenMobile extends StatelessWidget {
  const ResetPasswordScreenMobile({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: Padding(padding: const EdgeInsets.all(TSizes.defaultSpace), child: ResetPasswordWidget()),
      ),
    );
  }
}
