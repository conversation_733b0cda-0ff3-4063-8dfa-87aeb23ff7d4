import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/formatters/formatter.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Model class representing user data.
class UserModel {
  final String? id; // Made nullable as per original model
  String firstName;
  String lastName;
  String userName; // Original field name
  String email;
  String phoneNumber;
  String profilePicture;
  String department; // Added department

  // NEW: Replaced AppRole with List<UserRole>
  List<UserRole> roles;
  // NEW: Added reporting manager IDs
  List<String> reportingManagerIds;

  DateTime? createdAt;
  DateTime? updatedAt;

  /// Constructor for UserModel.
  UserModel({
    this.id,
    required this.email,
    this.firstName = '',
    this.lastName = '',
    this.userName = '', // Original field
    this.phoneNumber = '',
    this.profilePicture = '',
    required this.department, // Made department required in constructor
    this.roles = const [UserRole.SalesUser], // Default to SalesUser role
    this.reportingManagerIds = const [], // Default to empty list
    this.createdAt,
    this.updatedAt,
  });

  /// Helper methods
  String get fullName => '$firstName $lastName';
  // Assuming TFormatter is available and correctly implemented
  String get formattedDate => TFormatter.formatDate(createdAt);
  String get formattedUpdatedAtDate => TFormatter.formatDate(updatedAt);
  String get formattedPhoneNo => TFormatter.formatPhoneNumber(phoneNumber);

  /// Get initials from full name
  String get initials {
    final nameParts = fullName.trim().split(' ');
    if (nameParts.length > 1 && nameParts[1].isNotEmpty) {
      return '${nameParts[0][0]}${nameParts[1][0]}'.toUpperCase();
    } else if (nameParts.isNotEmpty && nameParts[0].isNotEmpty) {
      return nameParts[0][0].toUpperCase();
    }
    return '?';
  }

  /// Static function to create an empty user model.
  static UserModel empty() => UserModel(
    email: '',
    department: '',
    roles: const [UserRole.SalesUser], // Ensure default roles are set for empty
    reportingManagerIds: const [],
  );

  // Convert model to JSON
  Map<String, dynamic> toJson() {
    return {
      'firstName': firstName,
      'lastName': lastName,
      'userName': userName, // Original field
      'email': email,
      'phoneNumber': phoneNumber,
      'profilePicture': profilePicture,
      'department': department,
      'roles': roles.map((role) => role.toString().split('.').last).toList(), // Convert enum list to string list
      'reportingManagerIds': reportingManagerIds,
      'createdAt': createdAt, // Repository will handle conversion/server timestamp
      'updatedAt': updatedAt, // Repository will handle conversion/server timestamp
    };
  }

  /// Convert a subset of model data to JSON (e.g., for subcollections or simplified embeds)
  Map<String, dynamic> toSubJson() {
    return {
      'id': id,
      'firstName': firstName,
      'lastName': lastName,
      'userName': userName, // Include userName in subJson if needed
      'email': email, // Include email in subJson if needed
      'roles': roles.map((role) => role.toString().split('.').last).toList(),
      'department': department,
    };
  }

  /// Factory method to create UserModel from a Firebase document snapshot.
  factory UserModel.fromSnapshot(DocumentSnapshot<Map<String, dynamic>> document) {
    if (document.data() != null) {
      return UserModel.fromMap(document.data()!, id: document.id);
    } else {
      return empty();
    }
  }

  /// Factory method to create UserModel from a map.
  factory UserModel.fromMap(Map<String, dynamic> data, {String? id}) {
    // Parse the roles correctly from a list of strings
    List<UserRole> userRoles =
        (data['roles'] as List<dynamic>?)?.map((roleString) {
          return UserRole.values.firstWhere(
            (e) => e.toString().split('.').last == roleString,
            orElse: () => UserRole.SalesUser, // Default if role string not found
          );
        }).toList() ??
        const [UserRole.SalesUser]; // Default if 'roles' field is null or empty

    return UserModel(
      id: id,
      firstName: data.containsKey('firstName') ? data['firstName'] as String? ?? '' : '',
      lastName: data.containsKey('lastName') ? data['lastName'] as String? ?? '' : '',
      userName: data.containsKey('userName') ? data['userName'] as String? ?? '' : '', // Original field
      email: data.containsKey('email') ? data['email'] as String? ?? '' : '',
      phoneNumber: data.containsKey('phoneNumber') ? data['phoneNumber'] as String? ?? '' : '',
      profilePicture: data.containsKey('profilePicture') ? data['profilePicture'] as String? ?? '' : '',
      department: data.containsKey('department') ? data['department'] as String? ?? '' : '',
      roles: userRoles, // Assign parsed roles
      reportingManagerIds: List<String>.from(data['reportingManagerIds'] ?? []), // Parse reportingManagerIds
      createdAt: data.containsKey('createdAt') && data['createdAt'] is Timestamp ? (data['createdAt'] as Timestamp).toDate() : null,
      updatedAt: data.containsKey('updatedAt') && data['updatedAt'] is Timestamp ? (data['updatedAt'] as Timestamp).toDate() : null,
    );
  }

  @override
  bool operator ==(Object other) => identical(this, other) || other is UserModel && runtimeType == other.runtimeType && id == other.id; // Compare by ID

  @override
  int get hashCode => id.hashCode; // Use ID for hash code
}
