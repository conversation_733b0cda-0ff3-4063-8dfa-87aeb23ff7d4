import 'package:alloy/features/account/models/account_model.dart';
import 'package:alloy/features/account/repository/account_repository.dart';
import 'package:alloy/features/authentication/models/user_model.dart';
import 'package:alloy/features/contact/models/contact_model.dart';
import 'package:alloy/routes/routes.dart';
import 'package:alloy/utils/constants/enums.dart';
import 'package:alloy/utils/helpers/network_manager.dart';
import 'package:alloy/utils/popups/full_screen_loader.dart';
import 'package:alloy/utils/popups/loaders.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../address/models/address_model.dart';
import '../../address/repository/address_repository.dart';
import 'account_controller.dart';

class CreateAccountController extends GetxController {
  static CreateAccountController get instance => Get.find();

  /// Variables
  final formKey = GlobalKey<FormState>();
  final name = TextEditingController();
  final alsoKnownAs = TextEditingController();
  final phone = TextEditingController();
  final paymentTerms = TextEditingController();
  final email = TextEditingController(); // NEW: Email field

  final businessType = BusinessType.customer.obs;
  final priority = PriorityLevel.medium.obs;
  final status = AccountStatus.active.obs;
  final isParent = false.obs; // NEW: To allow making this account a parent
  final selectedParentAccount = Rx<AccountModel?>(null); // NEW: To hold the selected parent account

  final RxList<String> selectedIndustries = <String>[].obs;
  final RxList<UserModel> selectedHandlers = <UserModel>[].obs;
  final RxList<ContactModel> selectedContacts = <ContactModel>[].obs;

  // NEW: Reactive list to hold addresses associated with this new account
  final RxList<AddressModel> accountAddresses = <AddressModel>[].obs;

  // NEW: Reactive variables for denormalized location fields
  final RxString denormalizedCountry = ''.obs;
  final RxString denormalizedEmirates = ''.obs;

  // Repositories
  final AccountRepository _accountRepository = Get.find<AccountRepository>();
  final AddressRepository _addressRepository = Get.find<AddressRepository>();
  final AccountController _accountController = Get.find<AccountController>(); // For parent accounts

  // Reactive list for parent account dropdown options
  final RxList<AccountModel> parentAccountOptions = <AccountModel>[].obs;

  @override
  void onInit() {
    super.onInit();
    // TODO: Fix Firestore type casting issue for parent accounts
    // Temporarily disabled to allow app to run
    // _accountController.streamParentAccounts().listen((accounts) {
    //   parentAccountOptions.assignAll(accounts);
    // });

    // Listen to changes in accountAddresses to update denormalized fields
    ever(accountAddresses, (_) => _updateDenormalizedLocation());
  }

  @override
  void onClose() {
    name.dispose();
    alsoKnownAs.dispose();
    phone.dispose();
    paymentTerms.dispose();
    email.dispose();
    super.onClose();
  }

  /// Toggle Handler Selection
  void toggleHandlerSelection(UserModel handler) {
    if (selectedHandlers.contains(handler)) {
      selectedHandlers.remove(handler);
    } else {
      selectedHandlers.add(handler);
    }
  }

  /// Toggle Contact Selection
  void toggleContactSelection(ContactModel contact) {
    if (selectedContacts.contains(contact)) {
      selectedContacts.remove(contact);
    } else {
      selectedContacts.add(contact);
    }
  }

  /// Add a new address to the temporary list for the account
  void addAddress(AddressModel address) {
    // Determine if this should be the default address
    bool shouldBeDefault = accountAddresses.isEmpty; // If it's the first address, make it default

    // If it's a billing address and set as default (or should be default),
    // ensure only one billing address is default.
    if (address.type == 'Billing' && (address.isDefault || shouldBeDefault)) {
      for (var i = 0; i < accountAddresses.length; i++) {
        if (accountAddresses[i].type == 'Billing' && accountAddresses[i].isDefault) {
          accountAddresses[i] = accountAddresses[i].copyWith(isDefault: false);
        }
      }
      address = address.copyWith(isDefault: true); // Ensure the new one is default
    } else if (shouldBeDefault) {
      address = address.copyWith(isDefault: true); // If it's the first address overall, make it default
    }

    // Assign a temporary ID if not already present, for list management before saving
    final addressWithTempId = address.id.isEmpty
        ? address.copyWith(id: 'temp_${DateTime.now().millisecondsSinceEpoch}')
        : address;

    // Add the address to a temporary list, then assign all to trigger reactivity properly
    final List<AddressModel> tempAddresses = List.from(accountAddresses);
    tempAddresses.add(addressWithTempId);
    accountAddresses.assignAll(tempAddresses); // Use assignAll to trigger a single rebuild
  }

  /// Update an existing address in the temporary list
  void updateAddress(AddressModel updatedAddress) {
    final List<AddressModel> tempAddresses = List.from(accountAddresses);
    final index = tempAddresses.indexWhere((addr) => addr.id == updatedAddress.id);
    if (index != -1) {
      // If it's a billing address and set as default, ensure only one is default
      if (updatedAddress.type == 'Billing' && updatedAddress.isDefault) {
        for (var i = 0; i < tempAddresses.length; i++) {
          if (tempAddresses[i].type == 'Billing' &&
              tempAddresses[i].id != updatedAddress.id &&
              tempAddresses[i].isDefault) {
            tempAddresses[i] = tempAddresses[i].copyWith(isDefault: false);
          }
        }
      }
      tempAddresses[index] = updatedAddress;
      accountAddresses.assignAll(tempAddresses); // Use assignAll to trigger a single rebuild
    }
  }

  /// Remove an address from the temporary list
  void removeAddress(AddressModel address) {
    final List<AddressModel> tempAddresses = List.from(accountAddresses);
    tempAddresses.removeWhere((addr) => addr.id == address.id);

    // If the removed address was the default billing, find a new default or clear
    if (address.isDefault && address.type == 'Billing') {
      final newDefaultBilling = tempAddresses.firstWhereOrNull((addr) => addr.type == 'Billing');
      if (newDefaultBilling != null) {
        final index = tempAddresses.indexOf(newDefaultBilling);
        tempAddresses[index] = newDefaultBilling.copyWith(isDefault: true);
      }
    }
    accountAddresses.assignAll(tempAddresses); // Use assignAll to trigger a single rebuild
  }

  /// Toggle default status for an address
  void toggleDefaultAddress(AddressModel address) {
    // OPTIMIZED: Direct modification approach to minimize reactive triggers
    final index = accountAddresses.indexWhere((addr) => addr.id == address.id);

    if (index != -1) {
      final currentAddress = accountAddresses[index];
      final newDefaultStatus = !currentAddress.isDefault;

      // Batch all updates in a single operation
      final List<AddressModel> updatedAddresses = accountAddresses.map((addr) {
        if (addr.id == address.id) {
          // Update the target address
          return addr.copyWith(isDefault: newDefaultStatus);
        } else if (newDefaultStatus && addr.isDefault) {
          // If setting target as default, unset all other defaults
          return addr.copyWith(isDefault: false);
        } else {
          // Keep other addresses unchanged
          return addr;
        }
      }).toList();

      // Single reactive update
      accountAddresses.assignAll(updatedAddresses);
      _updateDenormalizedLocation();
    }
  }

  /// Internal method to update denormalized country and emirates based on any default address.
  /// Prioritizes billing address, then falls back to any default address.
  void _updateDenormalizedLocation() {
    // Find any default address (prioritize billing, then any other default)
    final defaultAddress =
        accountAddresses.firstWhereOrNull((addr) => addr.isDefault && addr.type == 'Billing') ??
        accountAddresses.firstWhereOrNull((addr) => addr.isDefault);

    if (defaultAddress != null) {
      denormalizedCountry.value = defaultAddress.country;
      denormalizedEmirates.value = defaultAddress.state ?? '';
    } else {
      denormalizedCountry.value = '';
      denormalizedEmirates.value = '';
    }
  }

  /// Create Account
  Future<void> createAccount() async {
    try {
      TFullScreenLoader.popUpCircular();

      // TODO: Replace with actual current user ID from authentication
      const String currentUserId = 'user_001';

      if (!await NetworkManager.instance.isConnected()) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'No Internet Connection', message: 'Please check your internet connection.');
        return;
      }

      if (!formKey.currentState!.validate()) {
        TFullScreenLoader.stopLoading();
        TLoaders.warningSnackBar(title: 'Invalid Form', message: 'Please correct the errors in the form.');
        return;
      }

      // Validation: Ensure at least one default address exists
      final hasDefaultAddress = accountAddresses.any((addr) => addr.isDefault);
      if (accountAddresses.isNotEmpty && !hasDefaultAddress) {
        TFullScreenLoader.stopLoading();
        TLoaders.errorSnackBar(
          title: 'Validation Error',
          message: 'Please select at least one default address before saving.',
        );
        return;
      }

      // 1. Create the AccountModel
      final newAccount = AccountModel(
        id: '', // Firestore will generate this
        name: name.text.trim(),
        alsoKnownAs: alsoKnownAs.text.trim(),
        phone: phone.text.trim(),
        paymentTerms: paymentTerms.text.trim(),
        email: email.text.trim(),
        businessType: businessType.value,
        priority: priority.value,
        status: status.value,
        isParent: isParent.value,
        parentId: selectedParentAccount.value?.id,
        industry: selectedIndustries.toList(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdByUserId: currentUserId,
        updatedByUserId: currentUserId,
        addressIds: [], // Will be populated after addresses are saved
        country: denormalizedCountry.value, // Denormalized from default billing address
        emirates: denormalizedEmirates.value, // Denormalized from default billing address
      );
      newAccount.handlerDetails = selectedHandlers.toList();
      newAccount.contactDetails = selectedContacts.toList();

      // 2. Create the Account document in Firestore to get its ID
      final accountId = await _accountRepository.createAccount(newAccount);
      final createdAccountWithId = newAccount.copyWith(id: accountId);

      // 3. Process Addresses (Create and link to the new account ID)
      final List<String> finalAddressIds = [];
      String? defaultBillingAddressId;

      for (var addr in accountAddresses) {
        final addressToCreate = addr.copyWith(
          accountId: accountId, // Assign the newly created account ID
          createdByUserId: currentUserId,
          updatedByUserId: currentUserId,
          id: '', // Ensure ID is empty for Firestore to generate a new one
        );
        final newAddressId = await _addressRepository.createAddress(addressToCreate);
        finalAddressIds.add(newAddressId);
        if (addressToCreate.isDefault && addressToCreate.type == 'Billing') {
          defaultBillingAddressId = newAddressId;
        }
      }

      // 4. Update the AccountModel with the final list of address IDs
      final accountToSave = createdAccountWithId.copyWith(addressIds: finalAddressIds);
      await _accountRepository.updateAccount(
        accountToSave,
        createdAccountWithId,
      ); // Pass itself as original for no diff

      // 5. Explicitly set default billing address to trigger denormalization in AccountModel
      if (defaultBillingAddressId != null) {
        await _addressRepository.setDefaultAddress(accountId, defaultBillingAddressId, 'Billing');
      } else {
        // If no default billing address is set, clear the country/emirates on the account
        await _accountRepository.updateAccountFields(accountId, {'country': null, 'emirates': null});
      }

      TFullScreenLoader.stopLoading();
      TLoaders.successSnackBar(title: 'Account Created', message: 'Account ${newAccount.name} created successfully!');

      // Reset form fields
      resetForm();

      Get.offAllNamed(TRoutes.accounts);
    } catch (e) {
      TFullScreenLoader.stopLoading();
      TLoaders.errorSnackBar(title: 'Oh Snap!', message: e.toString());
    }
  }

  /// Resets all form fields and selections.
  void resetForm() {
    formKey.currentState?.reset();
    name.clear();
    alsoKnownAs.clear();
    phone.clear();
    paymentTerms.clear();
    email.clear();
    businessType.value = BusinessType.customer;
    priority.value = PriorityLevel.medium;
    status.value = AccountStatus.active;
    isParent.value = false;
    selectedParentAccount.value = null;
    selectedIndustries.clear();
    selectedHandlers.clear();
    selectedContacts.clear();
    accountAddresses.clear(); // Clear addresses
    denormalizedCountry.value = ''; // Clear denormalized fields
    denormalizedEmirates.value = ''; // Clear denormalized fields
  }
}
