import 'package:flutter/material.dart';

import 'package:alloy/common/widgets/appbar/appbar.dart';
import 'package:alloy/features/account/screens/import_accounts/widgets/import_accounts_form.dart';
import 'package:alloy/utils/constants/sizes.dart';

class ImportAccountsScreen extends StatelessWidget {
  const ImportAccountsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: const TAppBar(title: Text('Import Accounts'), showBackArrow: true),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(TSizes.defaultSpace),
          child: Center(
            child: Column(
              children: [
                const SizedBox(height: TSizes.spaceBtwSections),
                ImportAccountsForm(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
