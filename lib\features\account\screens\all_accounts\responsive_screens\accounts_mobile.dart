import 'package:alloy/common/layouts/headers/list_header.dart';
import 'package:alloy/common/widgets/containers/primary_header_container.dart';
import 'package:alloy/common/widgets/data_table/search_field.dart';
import 'package:alloy/common/widgets/list_tiles/item_list_tile.dart';
import 'package:alloy/features/account/controller/account_controller.dart';
import 'package:alloy/features/account/screens/create_account/create_account_screen.dart';
import 'package:alloy/features/account/screens/edit_account/edit_account_screen.dart';
import 'package:alloy/utils/constants/colors.dart';
import 'package:alloy/utils/constants/sizes.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:iconsax/iconsax.dart';

class AccountsMobile extends StatelessWidget {
  const AccountsMobile({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<AccountController>();
    return Scaffold(
      floatingActionButton: FloatingActionButton(
        onPressed: () => Get.to(() => const CreateAccountScreen()),
        child: const Icon(Iconsax.add),
      ),
      body: Column(
        children: [
          TPrimaryHeaderContainer(
            height: 200,
            child: Column(
              children: [
                TListHeader(title: 'Accounts', showButton: false, textColor: TColors.white),
                const SizedBox(height: TSizes.spaceBtwItems),
                TSearchField(hintText: 'Search Accounts', onChanged: (query) => controller.searchQuery(query)),
              ],
            ),
          ),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) return const Center(child: CircularProgressIndicator());
              return ListView.separated(
                shrinkWrap: true,
                itemCount: controller.filteredItems.length,
                padding: const EdgeInsets.all(TSizes.defaultSpace),
                separatorBuilder: (_, __) => const SizedBox(height: TSizes.spaceBtwItems),
                itemBuilder: (_, index) {
                  final account = controller.filteredItems[index];
                  return TItemListTile(
                    title: account.name,
                    subTitle: account.emirates,
                    onEditPressed: () => Get.to(() => EditAccountScreen(account: account)),
                    onDeletePressed: () => controller.confirmDeleteItem(account),
                    leading: const Icon(Iconsax.user_octagon),
                    trailing: Text(account.businessType.name.capitalizeFirst!),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
