import 'package:alloy/common/layouts/templates/site_template.dart';
import 'package:alloy/features/dashboard/responsive_screens/dashboard_desktop.dart';
import 'package:alloy/features/dashboard/responsive_screens/dashboard_mobile.dart';
import 'package:alloy/features/dashboard/responsive_screens/dashboard_tablet.dart';
import 'package:flutter/material.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const TSiteTemplate(
      useLayout: true,
      desktop: DashboardDesktop(),
      tablet: DashboardTablet(),
      mobile: DashboardMobile(),
    );
  }
}
